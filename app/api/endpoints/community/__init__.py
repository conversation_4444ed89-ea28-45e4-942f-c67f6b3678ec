"""
社区功能模块主路由聚合器

将原来的 community.py 拆分为多个子模块，提高代码可维护性
"""
from fastapi import APIRouter

from .posts import router as posts_router
from .comments import router as comments_router
from .users import router as users_router
from .notifications import router as notifications_router
from .images import router as images_router
from .workouts import router as workouts_router

# 创建主路由器
router = APIRouter()

# 包含所有子模块路由
router.include_router(posts_router, tags=["community-posts"])
router.include_router(comments_router, tags=["community-comments"])
router.include_router(users_router, tags=["community-users"])
router.include_router(notifications_router, tags=["community-notifications"])
router.include_router(images_router, tags=["community-images"])
router.include_router(workouts_router, tags=["community-workouts"])
