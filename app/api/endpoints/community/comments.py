"""
社区评论和回复相关接口
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import models
from app.api import deps
from app.services.community_service import CommunityService
from app.schemas.community import (
    CommentCreate, CommentResponse
)

router = APIRouter()

@router.get("/posts/{post_id}/comments/")
async def get_comments(
    *,
    db: Session = Depends(deps.get_db),
    post_id: int,
    skip: int = 0,
    limit: int = 20,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional)
):
    """获取帖子的评论列表"""
    service = CommunityService(db)
    current_user_id = current_user.id if current_user else None
    return await service.get_comments(
        post_id=post_id,
        skip=skip,
        limit=limit,
        current_user_id=current_user_id
    )

@router.post("/posts/{post_id}/comments/", response_model=CommentResponse)
async def create_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    comment_in: CommentCreate
):
    """创建评论"""
    service = CommunityService(db)
    return await service.create_comment(current_user.id, comment_in)

@router.post("/comments/{comment_id}/replies/", response_model=CommentResponse)
async def create_reply(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int,
    content: str
):
    """对指定评论创建回复"""
    # 检查父评论是否存在
    from app import crud
    parent_comment = crud.crud_comment.get(db=db, id=comment_id)
    if not parent_comment:
        raise HTTPException(status_code=404, detail="Parent comment not found")

    # 创建回复评论
    reply_data = CommentCreate(
        content=content,
        post_id=parent_comment.post_id,
        parent_id=comment_id
    )

    service = CommunityService(db)
    return await service.create_comment(current_user.id, reply_data)

@router.get("/comments/{comment_id}/replies/")
async def get_comment_replies(
    *,
    db: Session = Depends(deps.get_db),
    comment_id: int,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional)
):
    """获取指定评论的回复列表"""
    from app import crud

    # 检查评论是否存在
    comment = crud.crud_comment.get(db=db, id=comment_id)
    if not comment:
        raise HTTPException(status_code=404, detail="Comment not found")

    # 获取回复列表
    replies = crud.crud_comment.get_replies(db=db, comment_id=comment_id)

    # 获取用户点赞的评论ID列表
    current_user_id = current_user.id if current_user else None
    liked_comment_ids = []
    if current_user_id:
        liked_comment_ids = crud.crud_comment_like.get_comments_liked_by_user(
            db=db, user_id=current_user_id
        )

    # 构建返回数据
    result = []
    for reply in replies:
        # 获取点赞数
        like_count = crud.crud_comment_like.count_by_comment(db=db, comment_id=reply.id)

        # 获取用户是否点赞
        is_liked = reply.id in liked_comment_ids

        # 获取用户信息
        user = crud.crud_user.get(db=db, id=reply.user_id)

        reply_data = {
            "id": reply.id,
            "content": reply.content,
            "user_id": reply.user_id,
            "post_id": reply.post_id,
            "parent_id": reply.parent_id,
            "status": str(reply.status.value) if hasattr(reply.status, 'value') else str(reply.status),
            "created_at": reply.created_at.isoformat() if reply.created_at else None,
            "updated_at": reply.updated_at.isoformat() if reply.updated_at else None,
            "like_count": like_count,
            "is_liked_by_current_user": is_liked,
            "user": {
                "id": user.id,
                "nickname": user.nickname,
                "avatar_url": getattr(user, 'avatar_url', None)
            } if user else None,
            "replies": []  # 回复的回复暂时为空，避免过深嵌套
        }
        result.append(reply_data)

    return {
        "total": len(result),
        "items": result
    }

@router.put("/comments/{comment_id}", response_model=CommentResponse)
async def update_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int,
    content: str
):
    """更新评论"""
    service = CommunityService(db)
    return await service.update_comment(comment_id, current_user.id, content)

@router.delete("/comments/{comment_id}")
async def delete_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int
):
    """删除评论"""
    service = CommunityService(db)
    return await service.delete_comment(comment_id, current_user.id)

@router.post("/comments/{comment_id}/like/")
async def like_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int
):
    """点赞评论"""
    service = CommunityService(db)
    return await service.like_comment(comment_id, current_user.id)

@router.delete("/comments/{comment_id}/like/")
async def unlike_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int
):
    """取消点赞评论"""
    from app import crud

    # 检查评论是否存在
    comment = crud.crud_comment.get(db=db, id=comment_id)
    if not comment:
        raise HTTPException(status_code=404, detail="评论不存在")

    # 检查是否已点赞
    like = crud.crud_comment_like.get_by_user_and_comment(
        db=db, user_id=current_user.id, comment_id=comment_id
    )

    if not like:
        raise HTTPException(status_code=400, detail="您还未点赞此评论")

    # 取消点赞
    crud.crud_comment_like.remove_like(
        db=db, user_id=current_user.id, comment_id=comment_id
    )

    return {"status": "unliked", "message": "取消点赞成功"}

@router.post("/comments/{comment_id}/report/")
async def report_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int,
    reason: str
):
    """举报评论"""
    service = CommunityService(db)
    return await service.report_comment(comment_id, current_user.id, reason)
