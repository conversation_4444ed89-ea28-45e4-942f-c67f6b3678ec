"""
社区图片相关接口
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import models
from app.api import deps
from app.services.community_service import CommunityService
from app.schemas.community import (
    ImageCreate, ImageUpdate, ImageResponse
)

router = APIRouter()

@router.post("/images/", response_model=ImageResponse)
async def create_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_in: ImageCreate
):
    """创建图片记录"""
    service = CommunityService(db)
    return await service.create_image(current_user.id, image_in)

@router.put("/images/{image_id}", response_model=ImageResponse)
async def update_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_id: int,
    image_in: ImageUpdate
):
    """更新图片记录"""
    service = CommunityService(db)
    return await service.update_image(image_id, current_user.id, image_in)

@router.delete("/images/{image_id}")
async def delete_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_id: int
):
    """删除图片记录"""
    service = CommunityService(db)
    return await service.delete_image(image_id, current_user.id)
