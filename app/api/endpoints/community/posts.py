"""
社区帖子相关接口
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
import enum

from app import models, schemas
from app.api import deps
from app.services.community_service import CommunityService
from app.schemas.community import (
    PostCreate, PostUpdate, PostResponse, PostListResponse
)

router = APIRouter()

@router.post("/posts/", response_model=PostResponse)
async def create_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_in: PostCreate
):
    """创建帖子"""
    service = CommunityService(db)
    return await service.create_post_with_workout(current_user.id, post_in)

@router.put("/posts/{post_id}", response_model=PostResponse)
async def update_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    post_in: PostUpdate
):
    """更新帖子"""
    service = CommunityService(db)
    return await service.update_post(post_id, current_user.id, post_in)

@router.delete("/posts/{post_id}")
async def delete_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int
):
    """删除帖子"""
    service = CommunityService(db)
    return await service.delete_post(post_id, current_user.id)

@router.get("/posts/", response_model=schemas.community.PostListResponse)
async def get_posts(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 20,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional)
):
    """获取帖子列表"""
    try:
        service = CommunityService(db)
        current_user_id = current_user.id if current_user else None
        result = await service.get_posts(skip=skip, limit=limit, current_user_id=current_user_id)

        # 数据清理和转换
        for item in result["items"]:
            # PostStatus枚举转为字符串
            if hasattr(item.get("status"), 'value'):
                item["status"] = str(item["status"].value)
            elif hasattr(item.get("status"), 'name'):
                item["status"] = str(item["status"].name)

            # 处理用户对象序列化
            if "user" in item and hasattr(item["user"], '__dict__'):
                user_obj = item["user"]
                item["user"] = {
                    "id": getattr(user_obj, 'id', None),
                    "nickname": getattr(user_obj, 'nickname', None),
                    "avatar_url": getattr(user_obj, 'avatar_url', None),
                    "gender": getattr(user_obj, 'gender', None),
                    "age": getattr(user_obj, 'age', None)
                }

            # 处理相关训练对象
            if "related_workout" in item and item["related_workout"]:
                workout_obj = item["related_workout"]
                if hasattr(workout_obj, '__dict__'):
                    item["related_workout"] = {
                        "id": getattr(workout_obj, 'id', None),
                        "name": getattr(workout_obj, 'name', None),
                        "title": getattr(workout_obj, 'title', None),
                        "date": getattr(workout_obj, 'training_date', None).isoformat() if getattr(workout_obj, 'training_date', None) else None
                    }
                else:
                    item["related_workout"] = None
            else:
                item["related_workout"] = None

            # 处理日期时间字段
            if "created_at" in item and hasattr(item["created_at"], 'isoformat'):
                item["created_at"] = item["created_at"].isoformat()
            if "updated_at" in item and hasattr(item["updated_at"], 'isoformat'):
                item["updated_at"] = item["updated_at"].isoformat()

            # 确保所有必需字段存在
            if "view_count" not in item:
                item["view_count"] = 0
            if "related_workout_detail" not in item:
                item["related_workout_detail"] = None
            if "images" not in item:
                item["images"] = []
            if "comments_summary" not in item:
                item["comments_summary"] = []
            if "image_urls" not in item:
                item["image_urls"] = []

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取帖子列表失败: {str(e)}")

@router.get("/posts/{post_id}")
async def get_post(
    *,
    db: Session = Depends(deps.get_db),
    post_id: int,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional)
):
    """获取帖子详情（增强版，包含用户关注状态）"""
    service = CommunityService(db)
    current_user_id = current_user.id if current_user else None
    result = await service.get_post(id=post_id, current_user_id=current_user_id)
    if not result:
        raise HTTPException(status_code=404, detail="Post not found")

    # 强制转换status为字符串
    if isinstance(result["status"], enum.Enum):
        result["status"] = str(result["status"].value)
    
    # 添加用户关注状态
    if current_user_id and result.get("user"):
        # 检查当前用户是否关注帖子作者
        from app.models.community.user_relation import UserRelation
        is_following = db.query(UserRelation).filter(
            UserRelation.follower_id == current_user_id,
            UserRelation.following_id == result["user"].id
        ).first() is not None
        
        # 在用户信息中添加关注状态
        if hasattr(result["user"], '__dict__'):
            user_dict = {
                "id": result["user"].id,
                "nickname": result["user"].nickname,
                "avatar_url": getattr(result["user"], 'avatar_url', None),
                "is_following": is_following
            }
            result["user"] = user_dict
        elif isinstance(result["user"], dict):
            result["user"]["is_following"] = is_following

    return result

@router.get("/posts/search/", response_model=List[PostResponse])
async def search_posts(
    *,
    db: Session = Depends(deps.get_db),
    keyword: str,
    skip: int = 0,
    limit: int = 20
):
    """搜索帖子"""
    service = CommunityService(db)
    return await service.search_posts(keyword, skip, limit)

@router.post("/posts/{post_id}/like/")
async def like_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int
):
    """点赞帖子"""
    service = CommunityService(db)
    return await service.like_post(post_id, current_user.id)

@router.post("/posts/{post_id}/report/")
async def report_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    reason: str
):
    """举报帖子"""
    service = CommunityService(db)
    return await service.report_post(post_id, current_user.id, reason)
