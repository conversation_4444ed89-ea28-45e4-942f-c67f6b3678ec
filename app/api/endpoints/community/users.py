"""
社区用户关注相关接口
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import models
from app.api import deps
from app.services.community_service import CommunityService
from app.schemas.community import UserRelationResponse

router = APIRouter()

@router.post("/users/{user_id}/follow/")
async def follow_user(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int
):
    """关注指定用户"""
    service = CommunityService(db)
    return await service.follow_user(current_user.id, user_id)

@router.delete("/users/{user_id}/follow/")
async def unfollow_user(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int
):
    """取消关注指定用户"""
    service = CommunityService(db)
    return await service.unfollow_user(current_user.id, user_id)

@router.get("/users/{user_id}/follow-status/")
async def get_follow_status(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int
) -> Dict[str, Any]:
    """检查当前用户对指定用户的关注状态"""
    from app.models.community.user_relation import UserRelation
    from app import crud
    
    # 检查目标用户是否存在
    target_user = crud.crud_user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 检查是否关注
    relation = db.query(UserRelation).filter(
        UserRelation.follower_id == current_user.id,
        UserRelation.following_id == user_id
    ).first()
    
    is_following = relation is not None
    
    # 获取关注和粉丝数量
    following_count = db.query(UserRelation).filter(
        UserRelation.follower_id == user_id
    ).count()
    
    followers_count = db.query(UserRelation).filter(
        UserRelation.following_id == user_id
    ).count()
    
    return {
        "user_id": user_id,
        "is_following": is_following,
        "following_count": following_count,
        "followers_count": followers_count,
        "followed_at": relation.created_at.isoformat() if relation else None
    }

@router.get("/users/{user_id}/following/", response_model=List[UserRelationResponse])
async def get_following(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    skip: int = 0,
    limit: int = 20
):
    """获取用户关注的人列表"""
    service = CommunityService(db)
    return await service.get_following(user_id, skip, limit)

@router.get("/users/{user_id}/followers/", response_model=List[UserRelationResponse])
async def get_followers(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    skip: int = 0,
    limit: int = 20
):
    """获取用户的粉丝列表"""
    service = CommunityService(db)
    return await service.get_followers(user_id, skip, limit)

@router.get("/users/{user_id}/stats/")
async def get_user_stats(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int
) -> Dict[str, Any]:
    """获取用户的社区统计信息"""
    from app.models.community.user_relation import UserRelation
    from app.models.community.post import Post
    from app.models.community.comment import Comment
    from app import crud
    
    # 检查用户是否存在
    user = crud.crud_user.get(db=db, id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 统计关注数
    following_count = db.query(UserRelation).filter(
        UserRelation.follower_id == user_id
    ).count()
    
    # 统计粉丝数
    followers_count = db.query(UserRelation).filter(
        UserRelation.following_id == user_id
    ).count()
    
    # 统计帖子数
    posts_count = db.query(Post).filter(
        Post.user_id == user_id,
        Post.status == "ACTIVE"
    ).count()
    
    # 统计评论数
    comments_count = db.query(Comment).filter(
        Comment.user_id == user_id,
        Comment.status == "ACTIVE"
    ).count()
    
    # 统计获得的点赞数（帖子点赞 + 评论点赞）
    from app.models.community.post_like import PostLike, CommentLike
    
    post_likes_count = db.query(PostLike).join(Post).filter(
        Post.user_id == user_id
    ).count()
    
    comment_likes_count = db.query(CommentLike).join(Comment).filter(
        Comment.user_id == user_id
    ).count()
    
    total_likes_received = post_likes_count + comment_likes_count
    
    return {
        "user_id": user_id,
        "following_count": following_count,
        "followers_count": followers_count,
        "posts_count": posts_count,
        "comments_count": comments_count,
        "total_likes_received": total_likes_received,
        "user_info": {
            "id": user.id,
            "nickname": user.nickname,
            "avatar_url": getattr(user, 'avatar_url', None),
            "created_at": user.created_at.isoformat() if hasattr(user, 'created_at') and user.created_at else None
        }
    }
