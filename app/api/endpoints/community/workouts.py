"""
社区训练分享相关接口
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path
from sqlalchemy.orm import Session

from app import models
from app.api import deps
from app.services.community_service import CommunityService
from app.services.workout_summary_service import WorkoutSummaryService
from app.schemas.community import (
    DailyWorkoutCreate, DailyWorkoutUpdate, DailyWorkoutResponse,
    WorkoutShareCreate
)

router = APIRouter()

# Workout Share Endpoints
@router.post("/workout/{workout_id}/share")
async def share_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int = Path(..., description="Workout ID，如果为0则创建新的workout"),
    share_data: WorkoutShareCreate,
    workout_summary_service: WorkoutSummaryService = Depends()
):
    """从训练记录创建Post并分享到社区，支持更新现有workout或创建新的workout"""
    try:
        # 处理workout_id为0的情况(创建新workout)
        actual_workout_id = None if workout_id == 0 else workout_id

        # 创建或更新Workout和DailyWorkout
        result = await workout_summary_service.create_or_update_workout_and_summary(
            db=db,
            user_id=current_user.id,
            workout_id=actual_workout_id,
            data={
                "title": share_data.title,
                "content": share_data.content,
                "workout_data": share_data.workout_data.model_dump() if hasattr(share_data, "workout_data") and share_data.workout_data else {},
                "visibility": share_data.visibility,
                "tags": share_data.tags if hasattr(share_data, "tags") else []
            }
        )

        daily_workout = result.get("daily_workout")

        if not daily_workout:
            raise HTTPException(status_code=404, detail="无法创建训练记录")

        # 创建Post并关联DailyWorkout
        from app.schemas.community import PostCreate
        post_data = PostCreate(
            title=share_data.title or "训练记录",
            content=share_data.content or "",
            daily_workout_id=daily_workout.id,
            images=share_data.images if hasattr(share_data, "images") else [],
            visibility=share_data.visibility,
            tags=share_data.tags if hasattr(share_data, "tags") else []
        )

        community_service = CommunityService(db)
        post = await community_service.create_post_with_workout(current_user.id, post_data)

        # 返回基本信息，避免复杂的响应模型
        return {
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "user_id": post.user_id,
            "created_at": post.created_at,
            "updated_at": post.updated_at,
            "tags": post.tags if hasattr(post, "tags") else [],
            "message": "训练分享成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分享失败: {str(e)}")

# Daily Workout Endpoints
@router.post("/daily-workouts/", response_model=DailyWorkoutResponse)
async def create_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_in: DailyWorkoutCreate
):
    """创建单日训练"""
    service = CommunityService(db)
    return await service.create_daily_workout(current_user.id, workout_in)

@router.put("/daily-workouts/{workout_id}", response_model=DailyWorkoutResponse)
async def update_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int,
    workout_in: DailyWorkoutUpdate
):
    """更新单日训练"""
    service = CommunityService(db)
    return await service.update_daily_workout(workout_id, current_user.id, workout_in)

@router.delete("/daily-workouts/{workout_id}")
async def delete_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int
):
    """删除单日训练"""
    service = CommunityService(db)
    return await service.delete_daily_workout(workout_id, current_user.id)

@router.get("/daily-workouts/{workout_id}", response_model=DailyWorkoutResponse)
async def get_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int
):
    """获取单日训练详情"""
    service = CommunityService(db)
    return await service.get_daily_workout(workout_id)

@router.get("/daily-workouts/", response_model=list[DailyWorkoutResponse])
async def get_daily_workouts(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100
):
    """获取单日训练列表"""
    service = CommunityService(db)
    return await service.get_daily_workouts(skip, limit)

@router.get("/daily-workouts/search/", response_model=list[DailyWorkoutResponse])
async def search_daily_workouts(
    *,
    db: Session = Depends(deps.get_db),
    keyword: str,
    skip: int = 0,
    limit: int = 20
):
    """搜索单日训练"""
    service = CommunityService(db)
    return await service.search_daily_workouts(keyword, skip, limit)
