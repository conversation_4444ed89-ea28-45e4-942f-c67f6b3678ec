from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from app.db.session import get_db
from app.models.user import User
from app.models.training_template import WorkoutTemplate
from app.models.workout_exercise import WorkoutExercise
from app.api.deps import get_current_user
from app.schemas.training_plan import TrainingTemplateCreate
from app.services.training_template_service import TrainingTemplateService
from app.services.set_record_manager import SetRecordManager

router = APIRouter()

# Get logger for this module
logger = logging.getLogger(__name__)

@router.get("/", response_model=List[dict])
def get_workout_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的所有训练模板，包含详细的运动信息和组记录
    """
    try:
        # 使用训练模板服务获取数据
        template_service = TrainingTemplateService(db)
        templates = template_service.get_user_templates_with_details(current_user.id)

        return [template.to_dict() for template in templates]

    except Exception as e:
        logger.error(f"获取训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取训练模板失败: {str(e)}")

@router.get("/{template_id}", response_model=dict)
def get_workout_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取特定训练模板的详情，包含详细的运动信息和组记录
    """
    try:
        # 使用训练模板服务获取数据
        template_service = TrainingTemplateService(db)
        template = template_service.get_template_with_details(template_id, current_user.id)

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        return template.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取训练模板详情失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取训练模板详情失败: {str(e)}")

@router.put("/{template_id}", response_model=dict)
def update_workout_template(
    template_id: int,
    template_data: TrainingTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新训练模板 - 支持差量更新

    参数:
    - template_id: 模板ID
    - template_data: 更新的模板数据

    功能:
    - 自动检测字段变更，只更新实际改变的字段
    - 支持更新模板基本信息和训练动作
    - 维护数据完整性
    """
    try:
        logger.info(f"收到更新训练模板请求，模板ID: {template_id}, 用户ID: {current_user.id}")
        logger.debug(f"更新数据: {template_data.model_dump()}")

        # 使用训练模板服务更新模板
        template_service = TrainingTemplateService(db)
        result = template_service.update_template(template_id, template_data, current_user.id)

        if not result:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新训练模板失败: {str(e)}")

@router.post("/", response_model=dict)
def create_workout_template(
    template_data: TrainingTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的训练模板

    参数:
    - name: 模板名称
    - description: 描述(可选)
    - estimated_duration: 预计时长(可选)
    - target_body_parts: 目标训练部位(可选)
    - training_scenario: 训练场景(可选)
    - exercises: 训练动作信息数组
    - notes: 备注(可选)
    """
    try:
        logger.info(f"收到创建训练模板请求，用户ID: {current_user.id}")
        logger.debug(f"模板数据: {template_data.model_dump()}")

        # 使用训练模板服务创建模板
        template_service = TrainingTemplateService(db)
        created_template = template_service.create_template(template_data, current_user.id)

        if not created_template:
            raise HTTPException(status_code=500, detail="创建训练模板失败")

        logger.info(f"成功创建训练模板，ID: {created_template.id}, 包含 {len(template_data.exercises)} 个动作")
        return created_template.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建训练模板失败: {str(e)}")

@router.delete("/{template_id}")
def delete_workout_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除训练模板
    """
    try:
        template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权删除")

        db.delete(template)  # 级联删除会自动删除关联的 WorkoutExercise
        db.commit()

        return {"message": "训练模板已删除"}

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除训练模板失败: {str(e)}")

@router.post("/{template_id}/apply", response_model=List[dict])
def apply_workout_template(
    template_id: int,
    date: Optional[str] = Query(None),
    body: Optional[dict] = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    应用训练模板到指定日期，创建 WorkoutExercise 和 SetRecord

    参数:
    - template_id: 模板ID
    - date: 应用日期(YYYY-MM-DD)，可以作为查询参数或请求体参数提供
    """
    try:
        # 从查询参数或请求体中获取日期
        date_value = date
        if not date_value and body and "date" in body:
            date_value = body.get("date")

        if not date_value:
            raise HTTPException(status_code=400, detail="缺少必要参数: date")

        # 验证日期格式
        try:
            target_date = datetime.strptime(date_value, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")

        # 使用训练模板服务获取模板
        template_service = TrainingTemplateService(db)
        template = template_service.get_template_with_details(template_id, current_user.id)

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        # 获取模板中的训练动作
        if not template.template_exercises or len(template.template_exercises) == 0:
            raise HTTPException(status_code=400, detail="该训练模板中没有训练动作")

        # 使用组记录管理器创建训练动作和组记录
        set_record_manager = SetRecordManager(db)
        created_exercises = []

        for template_exercise in template.template_exercises:
            # 创建新的 WorkoutExercise（复制模板配置）
            new_workout_exercise = WorkoutExercise(
                exercise_id=template_exercise.exercise_id,
                sets=template_exercise.sets,
                reps=template_exercise.reps,
                weight=template_exercise.weight,
                rest_seconds=template_exercise.rest_seconds,
                order=template_exercise.order,
                notes=template_exercise.notes,
                exercise_type=template_exercise.exercise_type,
                superset_group=template_exercise.superset_group
            )
            db.add(new_workout_exercise)
            db.flush()  # 获取ID

            # 使用组记录管理器创建组记录
            set_record_manager.create_set_records_for_exercise(
                workout_exercise_id=new_workout_exercise.id,
                sets_count=template_exercise.sets,
                template_weight=template_exercise.weight,
                template_reps=template_exercise.reps,
                target_date=target_date
            )

            created_exercises.append(new_workout_exercise)

        db.commit()

        # 返回创建的训练动作信息，包含详细的运动信息
        result = []
        for exercise in created_exercises:
            db.refresh(exercise)  # 刷新以获取关联数据
            exercise_dict = {
                "id": exercise.id,
                "exercise_id": exercise.exercise_id,
                "exercise_name": exercise.exercise.name if exercise.exercise else None,
                "sets": exercise.sets,
                "reps": exercise.reps,
                "weight": exercise.weight,
                "rest_seconds": exercise.rest_seconds,
                "order": exercise.order,
                "notes": exercise.notes,
                "exercise_type": exercise.exercise_type,
                # 添加来自Exercise模型的详细信息
                "name": exercise.exercise.name if exercise.exercise else None,
                "body_part_id": exercise.exercise.body_part_id if exercise.exercise else None,
                "equipment_id": exercise.exercise.equipment_id if exercise.exercise else None,
                "image_name": exercise.exercise.image_name if exercise.exercise else None,
                "exercise_type_detail": exercise.exercise.exercise_type if exercise.exercise else None,
                # 添加来自ExerciseDetail模型的详细信息
                "target_muscles_id": exercise.exercise.details.target_muscles_id if (exercise.exercise and exercise.exercise.details) else None,
                "synergist_muscles_id": exercise.exercise.details.synergist_muscles_id if (exercise.exercise and exercise.exercise.details) else None,
                "set_records": [
                    {
                        "id": sr.id,
                        "set_number": sr.set_number,
                        "weight": sr.weight,
                        "reps": sr.reps,
                        "completed": sr.completed,
                        "notes": sr.notes
                    }
                    for sr in exercise.set_records
                ],
                "applied_date": target_date.isoformat()
            }
            result.append(exercise_dict)

        return result

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"应用训练模板失败: {str(e)}")