from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session, joinedload
from datetime import datetime
import logging

from app.db.session import get_db
from app.models.user import User
from app.models.training_template import WorkoutTemplate  # 更新导入
from app.models.workout_exercise import WorkoutExercise
from app.models.set_record import SetRecord
from app.models.exercise import Exercise
from app.api.deps import get_current_user
from app.schemas.training_plan import TrainingTemplateCreate

router = APIRouter()

# Get logger for this module
logger = logging.getLogger(__name__)

@router.get("/", response_model=List[dict])
def get_workout_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的所有训练模板，包含详细的运动信息
    """
    try:
        # 使用joinedload来预加载关联的Exercise和ExerciseDetail数据，避免N+1查询问题

        templates = db.query(WorkoutTemplate).options(
            joinedload(WorkoutTemplate.template_exercises).joinedload(WorkoutExercise.exercise).joinedload(Exercise.details)
        ).filter(
            WorkoutTemplate.user_id == current_user.id
        ).order_by(WorkoutTemplate.created_at.desc()).all()

        return [template.to_dict() for template in templates]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取训练模板失败: {str(e)}")

@router.get("/{template_id}", response_model=dict)
def get_workout_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取特定训练模板的详情，包含详细的运动信息
    """
    try:
        # 使用joinedload来预加载关联的Exercise和ExerciseDetail数据，避免N+1查询问题

        template = db.query(WorkoutTemplate).options(
            joinedload(WorkoutTemplate.template_exercises).joinedload(WorkoutExercise.exercise).joinedload(Exercise.details)
        ).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        return template.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取训练模板详情失败: {str(e)}")

@router.put("/{template_id}", response_model=dict)
def update_workout_template(
    template_id: int,
    template_data: TrainingTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新训练模板 - 支持差量更新

    参数:
    - template_id: 模板ID
    - template_data: 更新的模板数据

    功能:
    - 自动检测字段变更，只更新实际改变的字段
    - 支持更新模板基本信息和训练动作
    - 维护数据完整性
    """
    try:
        logger.info(f"收到更新训练模板请求，模板ID: {template_id}, 用户ID: {current_user.id}")
        logger.debug(f"更新数据: {template_data.dict()}")

        # 获取现有模板，预加载关联的Exercise和ExerciseDetail数据

        existing_template = db.query(WorkoutTemplate).options(
            joinedload(WorkoutTemplate.template_exercises).joinedload(WorkoutExercise.exercise).joinedload(Exercise.details)
        ).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not existing_template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        # 比较并更新基本字段 (差量更新)
        template_fields_updated = []

        # 检查并更新各个字段
        if template_data.name != existing_template.name:
            existing_template.name = template_data.name
            template_fields_updated.append("name")

        if template_data.description != existing_template.description:
            existing_template.description = template_data.description
            template_fields_updated.append("description")

        if template_data.estimated_duration != existing_template.estimated_duration:
            existing_template.estimated_duration = template_data.estimated_duration
            template_fields_updated.append("estimated_duration")

        if template_data.target_body_parts != existing_template.target_body_parts:
            existing_template.target_body_parts = template_data.target_body_parts
            template_fields_updated.append("target_body_parts")

        if template_data.training_scenario != existing_template.training_scenario:
            existing_template.training_scenario = template_data.training_scenario
            template_fields_updated.append("training_scenario")

        if template_data.notes != existing_template.notes:
            existing_template.notes = template_data.notes
            template_fields_updated.append("notes")

        # 记录基本字段更新
        if template_fields_updated:
            logger.info(f"模板基本字段更新: {', '.join(template_fields_updated)}")
        else:
            logger.info("模板基本字段无变更")

        # 处理训练动作的更新 (差量更新)
        exercises_updated = False

        # 获取现有的训练动作
        existing_exercises = {ex.order: ex for ex in existing_template.template_exercises}
        new_exercises_data = {idx + 1: ex for idx, ex in enumerate(template_data.exercises)}

        # 比较和更新训练动作
        for order, new_exercise_data in new_exercises_data.items():

            # 处理exercise_id（与创建逻辑一致）
            exercise_id = new_exercise_data.exercise_id
            if isinstance(exercise_id, str):
                if exercise_id.startswith('added_exercise_'):
                    try:
                        parts = exercise_id.split('_')
                        if len(parts) >= 4:
                            real_exercise_id = int(parts[3])
                            existing_exercise = db.query(Exercise).filter(Exercise.id == real_exercise_id).first()
                            if existing_exercise:
                                exercise_id = real_exercise_id
                                logger.info(f"从临时ID {new_exercise_data.exercise_id} 解析出真实exercise_id: {exercise_id}")
                            else:
                                logger.warning(f"解析的exercise_id {real_exercise_id} 不存在，使用默认值")
                                exercise_id = 1
                        else:
                            logger.warning(f"临时exercise_id格式不正确: {exercise_id}，使用默认值")
                            exercise_id = 1
                    except (ValueError, IndexError) as e:
                        logger.warning(f"解析临时exercise_id失败: {e}，使用默认值")
                        exercise_id = 1
                elif exercise_id.isdigit():
                    exercise_id = int(exercise_id)
                else:
                    logger.warning(f"未知的exercise_id格式: {exercise_id}，使用默认值")
                    exercise_id = 1

            # 验证exercise_id是否存在
            exercise = db.query(Exercise).filter(Exercise.id == exercise_id).first()
            if not exercise:
                logger.warning(f"Exercise ID {exercise_id} 不存在，使用默认的第一个动作")
                first_exercise = db.query(Exercise).first()
                if first_exercise:
                    exercise_id = first_exercise.id
                else:
                    raise HTTPException(status_code=400, detail="系统中没有可用的健身动作")

            if order in existing_exercises:
                # 更新现有动作 (差量更新)
                existing_exercise = existing_exercises[order]
                exercise_updated = False

                if existing_exercise.exercise_id != exercise_id:
                    existing_exercise.exercise_id = exercise_id
                    exercise_updated = True

                if existing_exercise.sets != new_exercise_data.sets:
                    existing_exercise.sets = new_exercise_data.sets
                    exercise_updated = True

                if existing_exercise.reps != new_exercise_data.reps:
                    existing_exercise.reps = new_exercise_data.reps
                    exercise_updated = True

                if existing_exercise.weight != new_exercise_data.weight:
                    existing_exercise.weight = new_exercise_data.weight
                    exercise_updated = True

                if existing_exercise.rest_seconds != new_exercise_data.rest_seconds:
                    existing_exercise.rest_seconds = new_exercise_data.rest_seconds
                    exercise_updated = True

                if existing_exercise.notes != new_exercise_data.notes:
                    existing_exercise.notes = new_exercise_data.notes
                    exercise_updated = True

                if existing_exercise.exercise_type != new_exercise_data.exercise_type:
                    existing_exercise.exercise_type = new_exercise_data.exercise_type
                    exercise_updated = True

                if existing_exercise.superset_group != new_exercise_data.superset_group:
                    existing_exercise.superset_group = new_exercise_data.superset_group
                    exercise_updated = True

                if exercise_updated:
                    logger.info(f"更新第{order}个动作 (exercise_id: {exercise_id})")
                    exercises_updated = True

            else:
                # 添加新动作
                new_workout_exercise = WorkoutExercise(
                    template_id=existing_template.id,
                    exercise_id=exercise_id,
                    sets=new_exercise_data.sets,
                    reps=new_exercise_data.reps,
                    weight=new_exercise_data.weight,
                    rest_seconds=new_exercise_data.rest_seconds,
                    order=order,
                    notes=new_exercise_data.notes,
                    exercise_type=new_exercise_data.exercise_type,
                    superset_group=new_exercise_data.superset_group
                )
                db.add(new_workout_exercise)
                logger.info(f"添加新动作第{order}个 (exercise_id: {exercise_id})")
                exercises_updated = True

        # 删除不再需要的动作
        for order, existing_exercise in existing_exercises.items():
            if order not in new_exercises_data:
                db.delete(existing_exercise)
                logger.info(f"删除第{order}个动作 (exercise_id: {existing_exercise.exercise_id})")
                exercises_updated = True

        # 记录动作更新情况
        if exercises_updated:
            logger.info("训练动作已更新")
        else:
            logger.info("训练动作无变更")

        # 如果有任何更新，提交事务
        if template_fields_updated or exercises_updated:
            db.commit()
            db.refresh(existing_template)
            logger.info(f"成功更新训练模板 ID: {template_id}")

            return {
                "message": "训练模板更新成功",
                "updated_fields": template_fields_updated,
                "exercises_updated": exercises_updated,
                "template": existing_template.to_dict()
            }
        else:
            logger.info(f"训练模板 ID: {template_id} 无需更新")
            return {
                "message": "训练模板无变更",
                "updated_fields": [],
                "exercises_updated": False,
                "template": existing_template.to_dict()
            }

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新训练模板失败: {str(e)}")

@router.post("/", response_model=dict)
def create_workout_template(
    template_data: TrainingTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的训练模板

    参数:
    - name: 模板名称
    - description: 描述(可选)
    - estimated_duration: 预计时长(可选)
    - target_body_parts: 目标训练部位(可选)
    - training_scenario: 训练场景(可选)
    - exercises: 训练动作信息数组
    - notes: 备注(可选)
    """
    try:
        logger.info(f"收到创建训练模板请求，用户ID: {current_user.id}")
        logger.debug(f"模板数据: {template_data.dict()}")

        # 创建新的训练模板
        new_template = WorkoutTemplate(
            user_id=current_user.id,
            name=template_data.name,
            description=template_data.description,
            estimated_duration=template_data.estimated_duration,
            target_body_parts=template_data.target_body_parts,
            training_scenario=template_data.training_scenario,
            notes=template_data.notes
        )

        db.add(new_template)
        db.flush()  # 获取模板ID

        # 创建关联的训练动作
        for idx, exercise_data in enumerate(template_data.exercises):
            logger.debug(f"处理第{idx+1}个动作数据: {exercise_data.dict()}")

            # 处理exercise_id，如果是字符串需要特殊处理
            exercise_id = exercise_data.exercise_id
            if isinstance(exercise_id, str):
                if exercise_id.startswith('added_exercise_'):
                    # 对于临时动作ID，解析获取真实的exercise_id
                    # 格式：added_exercise_{timestamp}_{exercise_id}_{random}
                    try:
                        parts = exercise_id.split('_')
                        if len(parts) >= 4:
                            # 尝试从临时ID中提取真实的exercise_id（第3个部分）
                            real_exercise_id = int(parts[3])

                            # 验证该exercise_id是否存在
                            existing_exercise = db.query(Exercise).filter(Exercise.id == real_exercise_id).first()
                            if existing_exercise:
                                exercise_id = real_exercise_id
                                logger.info(f"从临时ID {exercise_data.exercise_id} 解析出真实exercise_id: {exercise_id}")
                            else:
                                logger.warning(f"解析的exercise_id {real_exercise_id} 不存在，使用默认值")
                                exercise_id = 1  # 使用默认的第一个动作
                        else:
                            logger.warning(f"临时exercise_id格式不正确: {exercise_id}，使用默认值")
                            exercise_id = 1
                    except (ValueError, IndexError) as e:
                        logger.warning(f"解析临时exercise_id失败: {e}，使用默认值")
                        exercise_id = 1
                elif exercise_id.isdigit():
                    exercise_id = int(exercise_id)
                else:
                    logger.warning(f"未知的exercise_id格式: {exercise_id}，使用默认值")
                    exercise_id = 1

            # 验证exercise_id是否存在
            exercise = db.query(Exercise).filter(Exercise.id == exercise_id).first()
            if not exercise:
                logger.warning(f"Exercise ID {exercise_id} 不存在，使用默认的第一个动作")
                # 获取第一个可用的动作作为默认值
                first_exercise = db.query(Exercise).first()
                if first_exercise:
                    exercise_id = first_exercise.id
                else:
                    raise HTTPException(status_code=400, detail="系统中没有可用的健身动作")

            # 由于Pydantic已经处理了sets数据标准化，直接使用
            sets_count = exercise_data.sets
            logger.info(f"处理动作{exercise_id}，组数: {sets_count}")

            workout_exercise = WorkoutExercise(
                template_id=new_template.id,
                exercise_id=exercise_id,
                sets=sets_count,
                reps=exercise_data.reps,
                weight=exercise_data.weight,
                rest_seconds=exercise_data.rest_seconds,
                order=idx + 1,
                notes=exercise_data.notes,
                exercise_type=exercise_data.exercise_type,
                superset_group=exercise_data.superset_group
            )
            db.add(workout_exercise)

        db.commit()

        # 重新查询模板以获取完整的关联数据

        created_template = db.query(WorkoutTemplate).options(
            joinedload(WorkoutTemplate.template_exercises).joinedload(WorkoutExercise.exercise).joinedload(Exercise.details)
        ).filter(WorkoutTemplate.id == new_template.id).first()

        logger.info(f"成功创建训练模板，ID: {new_template.id}, 包含 {len(template_data.exercises)} 个动作")
        return created_template.to_dict()

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建训练模板失败: {str(e)}")

@router.delete("/{template_id}")
def delete_workout_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除训练模板
    """
    try:
        template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权删除")

        db.delete(template)  # 级联删除会自动删除关联的 WorkoutExercise
        db.commit()

        return {"message": "训练模板已删除"}

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除训练模板失败: {str(e)}")

@router.post("/{template_id}/apply", response_model=List[dict])
def apply_workout_template(
    template_id: int,
    date: Optional[str] = Query(None),
    body: Optional[dict] = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    应用训练模板到指定日期，创建 WorkoutExercise 和 SetRecord

    参数:
    - template_id: 模板ID
    - date: 应用日期(YYYY-MM-DD)，可以作为查询参数或请求体参数提供
    """
    try:
        # 从查询参数或请求体中获取日期
        date_value = date
        if not date_value and body and "date" in body:
            date_value = body.get("date")

        if not date_value:
            raise HTTPException(status_code=400, detail="缺少必要参数: date")

        # 验证日期格式
        try:
            target_date = datetime.strptime(date_value, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")

        # 获取模板，预加载关联的Exercise和ExerciseDetail数据

        template = db.query(WorkoutTemplate).options(
            joinedload(WorkoutTemplate.template_exercises).joinedload(WorkoutExercise.exercise).joinedload(Exercise.details)
        ).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        # 获取模板中的训练动作
        if not template.template_exercises or len(template.template_exercises) == 0:
            raise HTTPException(status_code=400, detail="该训练模板中没有训练动作")

        # 创建新的 WorkoutExercise 记录（独立的，不关联任何 workout）
        created_exercises = []
        for template_exercise in template.template_exercises:
            # 创建新的 WorkoutExercise（复制模板配置）
            new_workout_exercise = WorkoutExercise(
                exercise_id=template_exercise.exercise_id,
                sets=template_exercise.sets,
                reps=template_exercise.reps,
                weight=template_exercise.weight,
                rest_seconds=template_exercise.rest_seconds,
                order=template_exercise.order,
                notes=template_exercise.notes,
                exercise_type=template_exercise.exercise_type,
                superset_group=template_exercise.superset_group
            )
            db.add(new_workout_exercise)
            db.flush()  # 获取ID

            # 为每个计划的组创建 SetRecord（初始状态为未完成）
            for set_num in range(1, template_exercise.sets + 1):
                set_record = SetRecord(
                    workout_exercise_id=new_workout_exercise.id,
                    set_number=set_num,
                    set_type="normal",
                    weight=float(template_exercise.weight) if template_exercise.weight and template_exercise.weight.replace('.', '').isdigit() else None,
                    reps=int(template_exercise.reps) if template_exercise.reps.isdigit() else None,
                    completed=False,
                    created_at=datetime.combine(target_date, datetime.min.time())
                )
                db.add(set_record)

            created_exercises.append(new_workout_exercise)

        db.commit()

        # 返回创建的训练动作信息，包含详细的运动信息
        result = []
        for exercise in created_exercises:
            db.refresh(exercise)  # 刷新以获取关联数据
            exercise_dict = {
                "id": exercise.id,
                "exercise_id": exercise.exercise_id,
                "exercise_name": exercise.exercise.name if exercise.exercise else None,
                "sets": exercise.sets,
                "reps": exercise.reps,
                "weight": exercise.weight,
                "rest_seconds": exercise.rest_seconds,
                "order": exercise.order,
                "notes": exercise.notes,
                "exercise_type": exercise.exercise_type,
                # 添加来自Exercise模型的详细信息
                "name": exercise.exercise.name if exercise.exercise else None,
                "body_part_id": exercise.exercise.body_part_id if exercise.exercise else None,
                "equipment_id": exercise.exercise.equipment_id if exercise.exercise else None,
                "image_name": exercise.exercise.image_name if exercise.exercise else None,
                "exercise_type_detail": exercise.exercise.exercise_type if exercise.exercise else None,
                # 添加来自ExerciseDetail模型的详细信息
                "target_muscles_id": exercise.exercise.details.target_muscles_id if (exercise.exercise and exercise.exercise.details) else None,
                "synergist_muscles_id": exercise.exercise.details.synergist_muscles_id if (exercise.exercise and exercise.exercise.details) else None,
                "set_records": [
                    {
                        "id": sr.id,
                        "set_number": sr.set_number,
                        "weight": sr.weight,
                        "reps": sr.reps,
                        "completed": sr.completed,
                        "notes": sr.notes
                    }
                    for sr in exercise.set_records
                ],
                "applied_date": target_date.isoformat()
            }
            result.append(exercise_dict)

        return result

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"应用训练模板失败: {str(e)}")