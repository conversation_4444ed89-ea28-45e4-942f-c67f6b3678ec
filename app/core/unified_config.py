"""
统一智能架构配置

用于管理统一智能架构集成项目的配置参数。
"""

from pydantic_settings import BaseSettings
from typing import Optional

class UnifiedArchitectureSettings(BaseSettings):
    """统一智能架构配置"""

    # 总体开关
    ENABLE_UNIFIED_ARCHITECTURE: bool = True
    UNIFIED_ARCH_PHASE: str = "phase2"  # phase1, phase2, phase3, phase4

    # 阶段一：传统系统集成配置
    ENABLE_ENHANCED_RECOGNIZER: bool = False
    ENABLE_LEGACY_PROCESSORS: bool = False
    ENABLE_HYBRID_ROUTER: bool = False

    # 阶段二：LangGraph配置
    ENABLE_LANGGRAPH: bool = True
    LANGGRAPH_CHECKPOINT_TTL: int = 3600
    LANGGRAPH_MAX_RECURSION: int = 50
    LANGGRAPH_ENABLE_STREAMING: bool = True

    # 阶段三：高级特性配置
    ENABLE_HUMAN_IN_LOOP: bool = False
    ENABLE_AB_TESTING: bool = False
    ENABLE_DYNAMIC_ROUTING: bool = False

    # LangSmith配置（可选）
    LANGSMITH_API_KEY: Optional[str] = None
    LANGSMITH_PROJECT: str = "fitness-ai-assistant"
    LANGSMITH_TRACING: bool = False

    # 性能配置
    UNIFIED_ARCH_CACHE_TTL: int = 300
    UNIFIED_ARCH_TIMEOUT: int = 30
    UNIFIED_ARCH_MAX_RETRIES: int = 3

    class Config:
        env_prefix = "UNIFIED_ARCH_"
        case_sensitive = True

# 全局配置实例
unified_settings = UnifiedArchitectureSettings()
