from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, SmallInteger, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class WorkoutTemplate(Base):
    """训练模板表 - 重命名自 TrainingTemplate"""
    __tablename__ = "workout_templates"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(255), nullable=False)  # 重命名：template_name -> name
    description = Column(Text, nullable=True)   # 新增：详细描述
    estimated_duration = Column(SmallInteger, nullable=True)  # 新增：预计时长（分钟）
    target_body_parts = Column(ARRAY(Integer), nullable=True)  # 新增：目标训练部位ID列表
    training_scenario = Column(String(20), nullable=True)  # 新增：训练场景：home, gym 等
    notes = Column(Text, nullable=True)  # 保留：备注
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系 - 直接关联 WorkoutExercise 而不是 JSON
    user = relationship("User", back_populates="workout_templates")
    template_exercises = relationship("WorkoutExercise",
                                    foreign_keys="WorkoutExercise.template_id",
                                    back_populates="workout_template",
                                    cascade="all, delete-orphan",
                                    order_by="WorkoutExercise.order")

    def to_dict(self):
        """转换为字典格式，包含详细的运动信息"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "name": self.name,
            "description": self.description,
            "estimated_duration": self.estimated_duration,
            "target_body_parts": self.target_body_parts,
            "training_scenario": self.training_scenario,
            "notes": self.notes,
            "exercise_count": len(self.template_exercises) if self.template_exercises else 0,
            "exercises": [
                {
                    "id": ex.id,
                    "exercise_id": ex.exercise_id,
                    "sets": ex.sets,
                    "reps": ex.reps,
                    "weight": ex.weight,
                    "rest_seconds": ex.rest_seconds,
                    "order": ex.order,
                    "notes": ex.notes,
                    "exercise_type": ex.exercise_type,
                    "superset_group": ex.superset_group,
                    # 添加来自Exercise模型的详细信息
                    "name": ex.exercise.name if ex.exercise else None,
                    "body_part_id": ex.exercise.body_part_id if ex.exercise else None,
                    "equipment_id": ex.exercise.equipment_id if ex.exercise else None,
                    "image_name": ex.exercise.image_name if ex.exercise else None,
                    "exercise_type_detail": ex.exercise.exercise_type if ex.exercise else None,
                    # 添加来自ExerciseDetail模型的详细信息
                    "target_muscles_id": ex.exercise.details.target_muscles_id if (ex.exercise and ex.exercise.details) else None,
                    "synergist_muscles_id": ex.exercise.details.synergist_muscles_id if (ex.exercise and ex.exercise.details) else None
                }
                for ex in sorted(self.template_exercises, key=lambda x: x.order)
            ] if self.template_exercises else [],
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }