from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from app.schemas.base import DateTimeModelMixin


class WorkoutExerciseBase(BaseModel):
    """训练动作基础模型"""
    exercise_id: Union[int, str]  # 支持整数ID和字符串ID（如'added_exercise_1746525022809_15_419'）
    sets: int
    reps: str
    rest_seconds: Optional[int] = 60
    order: int
    notes: Optional[str] = None
    exercise_type: str = "weight_reps"
    superset_group: Optional[int] = None
    weight: Optional[str] = None


class WorkoutExerciseCreate(WorkoutExerciseBase):
    """用于创建训练动作的模型"""
    pass


class WorkoutExerciseUpdate(BaseModel):
    """用于更新训练动作的模型"""
    sets: Optional[int] = None
    reps: Optional[str] = None
    rest_seconds: Optional[int] = None
    order: Optional[int] = None
    notes: Optional[str] = None
    exercise_type: Optional[str] = None
    superset_group: Optional[int] = None
    weight: Optional[str] = None


class WorkoutExerciseInDB(WorkoutExerciseBase):
    """数据库中的训练动作模型"""
    id: int
    workout_id: int

    class Config:
        from_attributes = True


class WorkoutExerciseWithDetail(WorkoutExerciseInDB):
    """包含详细信息的训练动作模型"""
    exercise_name: str
    exercise_image: Optional[str] = None
    exercise_description: Optional[str] = None
    set_records: Optional[List[Dict[str, Any]]] = None  # 添加组记录字段


class WorkoutBase(BaseModel):
    """训练日基础模型"""
    name: str
    day_of_week: Optional[int] = None
    day_number: int
    description: Optional[str] = None
    estimated_duration: Optional[int] = None  # 预计时长（分钟）
    scheduled_date: Optional[datetime] = None  # 计划执行日期
    target_body_parts: Optional[List[int]] = None  # 目标训练部位ID列表
    training_scenario: Optional[str] = None  # 训练场景：home, gym 等


class WorkoutCreate(WorkoutBase):
    """用于创建训练日的模型"""
    training_plan_id: int
    exercises: Optional[List[WorkoutExerciseCreate]] = []


class WorkoutUpdate(BaseModel):
    """用于更新训练日的模型"""
    name: Optional[str] = None
    day_of_week: Optional[int] = None
    description: Optional[str] = None
    estimated_duration: Optional[int] = None
    scheduled_date: Optional[datetime] = None
    status: Optional[str] = None


class WorkoutInDB(WorkoutBase, DateTimeModelMixin):
    """数据库中的训练日模型"""
    id: int
    training_plan_id: int

    class Config:
        from_attributes = True


class WorkoutWithExercises(WorkoutInDB):
    """包含训练动作的训练日模型"""
    exercises: List[WorkoutExerciseWithDetail] = []


class TrainingPlanBase(BaseModel):
    """训练计划基础模型"""
    plan_name: str = "个性化训练计划"
    description: Optional[str] = None
    fitness_goal: Optional[int] = None
    experience_level: Optional[int] = None
    duration_weeks: Optional[int] = 4
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_template: bool = False
    privacy_setting: int = 1  # 0: Public, 1: Private
    status: str = "active"  # active, completed, paused


class TrainingPlanCreate(TrainingPlanBase):
    """用于创建训练计划的模型"""
    user_id: int
    workouts: Optional[List[WorkoutCreate]] = []


class TrainingPlanUpdate(BaseModel):
    """用于更新训练计划的模型"""
    plan_name: Optional[str] = None
    description: Optional[str] = None
    fitness_goal: Optional[int] = None
    experience_level: Optional[int] = None
    duration_weeks: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_template: Optional[bool] = None
    privacy_setting: Optional[int] = None
    status: Optional[str] = None
    set_record_data: Optional[List[Dict[str, Any]]] = None

    # 保留原有的嵌套更新结构，以保持向后兼容
    plan_data: Optional[dict] = None
    workout_data: Optional[List[dict]] = None
    workout_exercise_data: Optional[List[dict]] = None
    training_record_data: Optional[List[dict]] = None

    class Config:
        schema_extra = {
            "example": {
                "plan_name": "更新后的计划名称",
                "description": "更新后的描述",
                "start_date": "2023-01-01T00:00:00Z",
                "end_date": "2023-01-28T00:00:00Z",
                "set_record_data": [
                    {
                        "id": 1,
                        "set_type": "normal",
                        "weight": 50.0,
                        "reps": 12,
                        "completed": True
                    }
                ],
                "plan_data": {
                    "plan_name": "更新后的计划名称",
                    "description": "更新后的描述"
                },
                "workout_data": [
                    {
                        "id": 1,
                        "name": "更新后的训练日名称",
                        "description": "更新后的描述"
                    }
                ]
            }
        }


class TrainingPlanInDB(TrainingPlanBase, DateTimeModelMixin):
    """数据库中的训练计划模型"""
    id: int
    user_id: int
    is_active: bool

    class Config:
        from_attributes = True


class TrainingPlanWithWorkouts(TrainingPlanInDB):
    """包含训练日的训练计划模型"""
    workouts: List[WorkoutWithExercises] = []


class TrainingPlanGenRequest(BaseModel):
    """训练计划生成请求"""
    user_id: int
    duration_weeks: int = 4
    days_per_week: int = Field(3, ge=1, le=7)
    fitness_goal: Optional[int] = None
    available_equipment: Optional[List[int]] = None
    focus_body_parts: Optional[List[int]] = None
    time_per_workout: Optional[int] = 60  # 分钟
    additional_notes: Optional[str] = None


class DailyWorkoutGenRequest(BaseModel):
    """单日训练计划生成请求"""
    user_id: int
    available_time: int = Field(60, ge=10, le=180)  # 分钟
    target_body_parts: Optional[List[int]] = None
    available_equipment: Optional[List[int]] = None
    recovery_level: Optional[int] = Field(5, ge=1, le=10)  # 恢复程度 1-10
    additional_notes: Optional[str] = None


class TrainingPlanSchema(BaseModel):
    """用于LLM生成的训练计划结构"""
    plan_name: str
    description: str
    duration_weeks: int
    workouts: List[Dict[str, Any]]

    class Config:
        schema_extra = {
            "example": {
                "plan_name": "初学者全身力量训练计划",
                "description": "这是一个为初学者设计的全身力量训练计划，每周三天，专注于基础复合动作，帮助建立基础力量和肌肉耐力。",
                "duration_weeks": 4,
                "workouts": [
                    {
                        "name": "第1天：全身训练A",
                        "day_number": 1,
                        "description": "专注于上肢和核心的全身训练日",
                        "estimated_duration": 60,
                        "exercises": [
                            {
                                "exercise_id": 15,
                                "sets": 3,
                                "reps": "10-12",
                                "rest_seconds": 60,
                                "order": 1,
                                "notes": "保持背部挺直，肩膀下沉",
                                "weight": "根据能力选择"
                            }
                        ]
                    }
                ]
            }
        }


class DailyWorkoutSchema(BaseModel):
    """用于LLM生成的单日训练计划结构"""
    workout_name: str
    description: str
    estimated_duration: int
    target_body_parts: List[int]
    exercises: List[Dict[str, Any]]

    class Config:
        schema_extra = {
            "example": {
                "workout_name": "上肢力量训练",
                "description": "这是一个针对胸部、肩部和手臂的训练，专注于增加上肢力量和肌肉体积。",
                "estimated_duration": 60,
                "target_body_parts": [2, 6, 5],  # 胸部、肩部、大臂
                "exercises": [
                    {
                        "exercise_id": 25,
                        "sets": 4,
                        "reps": "8-10",
                        "rest_seconds": 90,
                        "order": 1,
                        "notes": "控制动作速度，感受胸肌收缩",
                        "weight": "中等重量，能完成8-10次"
                    }
                ]
            }
        }


# Training Template Schemas
class TrainingTemplateExerciseCreate(BaseModel):
    """训练模板中的动作创建模型"""
    exercise_id: Union[int, str] = Field(..., description="动作ID，可以是整数或字符串（如临时动作ID）")
    sets: Union[int, List[Dict[str, Any]], Dict[str, Any], str] = Field(3, description="组数，可以是整数或包含详细信息的列表/字典")
    reps: Union[str, int] = Field("10", description="次数")
    weight: Optional[Union[str, int, float]] = None
    rest_seconds: Optional[Union[int, str]] = Field(60, description="休息时间（秒）")
    notes: Optional[str] = None
    exercise_type: str = Field("weight_reps", description="动作类型")
    superset_group: Optional[Union[int, str]] = None

    @validator('exercise_id', pre=True)
    def validate_exercise_id(cls, v):
        """验证和标准化exercise_id"""
        if isinstance(v, str):
            # 如果是字符串形式的临时动作ID，需要解析或处理
            if v.startswith('added_exercise_'):
                # 对于临时动作，可能需要先创建动作或使用占位符
                # 这里返回字符串，后续在API中特殊处理
                return v
            elif v.isdigit():
                return int(v)
            else:
                raise ValueError(f"Invalid exercise_id format: {v}")
        elif isinstance(v, (int, float)):
            return int(v)
        else:
            raise ValueError(f"exercise_id must be int or string, got {type(v)}")

    @validator('sets', pre=True)
    def validate_sets(cls, v):
        """验证和标准化sets数据"""
        if isinstance(v, list):
            # 如果是列表，返回列表长度
            return len(v)
        elif isinstance(v, dict):
            # 如果是字典，返回默认值
            return 3
        elif isinstance(v, (int, float)):
            # 如果是数字，直接返回整数
            return int(v)
        elif isinstance(v, str):
            if v.isdigit():
                return int(v)
            else:
                # 对于非数字字符串，返回默认值
                return 3
        else:
            # 其他情况返回默认值
            return 3

    @validator('reps', pre=True)
    def validate_reps(cls, v):
        """验证和标准化reps数据"""
        if v is None:
            return "10"
        elif isinstance(v, (int, float)):
            return str(int(v))
        elif isinstance(v, str):
            return v if v else "10"
        else:
            return "10"

    @validator('weight', pre=True)
    def validate_weight(cls, v):
        """验证和标准化weight数据"""
        if v is None or v == "":
            return None
        elif isinstance(v, (int, float)):
            return str(v)
        elif isinstance(v, str):
            return v
        else:
            return None

    @validator('rest_seconds', pre=True)
    def validate_rest_seconds(cls, v):
        """验证和标准化rest_seconds数据"""
        if v is None:
            return 60
        elif isinstance(v, (int, float)):
            return int(v)
        elif isinstance(v, str) and v.isdigit():
            return int(v)
        else:
            return 60

    @validator('superset_group', pre=True)
    def validate_superset_group(cls, v):
        """验证和标准化superset_group数据"""
        if v is None or v == "":
            return None
        elif isinstance(v, (int, float)):
            return int(v)
        elif isinstance(v, str) and v.isdigit():
            return int(v)
        else:
            return None


class TrainingTemplateCreate(BaseModel):
    """训练模板创建模型"""
    name: str = Field(..., min_length=1, max_length=100, description="模板名称")
    description: Optional[str] = Field(None, max_length=500, description="模板描述")
    estimated_duration: Optional[int] = Field(None, ge=5, le=300, description="预计时长（分钟）")
    target_body_parts: Optional[Union[str, List[int]]] = Field(None, description="目标训练部位，可以是字符串或整数列表")
    training_scenario: Optional[str] = Field(None, max_length=50, description="训练场景")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    exercises: List[TrainingTemplateExerciseCreate] = Field(..., min_items=1, description="训练动作列表")

    @validator('target_body_parts', pre=True)
    def validate_target_body_parts(cls, v):
        """验证和标准化target_body_parts数据"""
        if v is None or v == "":
            return None
        
        if isinstance(v, str):
            # 如果是字符串，需要转换为整数列表
            # 可能的格式：
            # 1. "胸部,肩部,手臂" -> 需要转换为对应的ID
            # 2. "1,2,3" -> 直接转换为[1,2,3]
            # 3. "胸部" -> 转换为对应的ID
            
            # 定义身体部位名称到ID的映射
            body_part_mapping = {
                "胸部": 2, "腿部": 1, "臀部": 3, "背部": 4, 
                "手臂": 5, "肩部": 6, "小臂": 7, "小腿": 8,
                "颈部": 9, "有氧": 10, "全身": 11, "腰腹部": 12,
                "爆发力": 13, "力量举": 14, "瑜伽": 15, "拉伸": 16,
                "二头": 17, "三头": 18, "股四头肌": 19, "腘绳肌": 20
            }
            
            # 按逗号分割字符串
            parts = [part.strip() for part in v.split(',') if part.strip()]
            result = []
            
            for part in parts:
                # 尝试直接转换为整数
                if part.isdigit():
                    result.append(int(part))
                # 尝试从映射中查找
                elif part in body_part_mapping:
                    result.append(body_part_mapping[part])
                else:
                    # 如果找不到映射，默认使用胸部(ID=2)
                    result.append(2)
            
            return result if result else None
        
        elif isinstance(v, list):
            # 如果已经是列表，确保所有元素都是整数
            return [int(item) for item in v if isinstance(item, (int, str)) and str(item).isdigit()]
        
        else:
            # 其他类型，返回None
            return None


class TrainingTemplateBase(BaseModel):
    """训练模板基础模型"""
    name: str
    description: Optional[str] = None
    estimated_duration: Optional[int] = None
    target_body_parts: Optional[str] = None
    training_scenario: Optional[str] = None
    notes: Optional[str] = None


class TrainingTemplateInDB(TrainingTemplateBase, DateTimeModelMixin):
    """数据库中的训练模板模型"""
    id: int
    user_id: int

    class Config:
        from_attributes = True


class TrainingTemplateWithExercises(TrainingTemplateInDB):
    """包含动作的训练模板模型"""
    template_exercises: List[WorkoutExerciseWithDetail] = []