from app.services.meal_service import MealService
from app.services.user import UserService
from app.services.favorite import FavoriteService
from app.services.llm_proxy_service import LLMProxyService
from app.services.chat_log_service import ChatLogService
from app.services.food_recognition_service import FoodRecognitionService
from app.services.llm_log_service import LLMLogService
from app.services.food_search_service import FoodSearchService
from app.services.model_service import ModelService

# 团队相关服务
from app.services.team_service import TeamService
from app.services.team_member_service import TeamMemberService
from app.services.team_client_service import TeamClientService
from app.services.team_training_service import TeamTrainingService
from app.services.team_stats_service import TeamStatsService

# 训练模板相关服务
from app.services.training_template_service import TrainingTemplateService
from app.services.exercise_data_processor import ExerciseDataProcessor
from app.services.set_record_manager import SetRecordManager
