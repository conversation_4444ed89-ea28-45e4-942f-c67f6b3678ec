"""
AI助手缓存模块

提供AI助手相关的缓存功能
"""

import logging
from typing import Dict, Any, Optional
from app.services.ai_assistant.common.cache import MemoryCacheService

logger = logging.getLogger(__name__)

# 全局缓存实例
_cache_service = MemoryCacheService()

class AIAssistantCache:
    """AI助手缓存服务"""
    
    @staticmethod
    async def get(key: str) -> Optional[Any]:
        """获取缓存项"""
        return await _cache_service.get(key)
    
    @staticmethod
    async def set(key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存项"""
        return await _cache_service.set(key, value, ttl)
    
    @staticmethod
    async def delete(key: str) -> bool:
        """删除缓存项"""
        return await _cache_service.delete(key)
    
    @staticmethod
    async def clear() -> bool:
        """清空缓存"""
        return await _cache_service.clear()

# 为了兼容性，提供模块级别的函数
async def get_cache(key: str) -> Optional[Any]:
    """获取缓存项"""
    return await AIAssistantCache.get(key)

async def set_cache(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """设置缓存项"""
    return await AIAssistantCache.set(key, value, ttl)

async def delete_cache(key: str) -> bool:
    """删除缓存项"""
    return await AIAssistantCache.delete(key)

async def clear_cache() -> bool:
    """清空缓存"""
    return await AIAssistantCache.clear()
