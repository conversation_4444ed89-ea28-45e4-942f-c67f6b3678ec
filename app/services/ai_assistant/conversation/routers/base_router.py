"""
路由器基类

定义统一智能架构中路由器的基础接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class BaseRouter(ABC):
    """路由器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.routing_rules = {}
        logger.info(f"初始化路由器: {name}")
    
    @abstractmethod
    async def route(self, intent: str, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """路由请求"""
        pass
    
    def add_routing_rule(self, rule_name: str, rule_config: Dict[str, Any]):
        """添加路由规则"""
        self.routing_rules[rule_name] = rule_config
        logger.info(f"添加路由规则: {rule_name}")
    
    def get_routing_rules(self) -> Dict[str, Any]:
        """获取路由规则"""
        return self.routing_rules
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "router": self.name,
            "status": "healthy",
            "rules_count": len(self.routing_rules)
        }
