"""
混合意图路由器

智能路由用户请求到最适合的处理系统。
"""

import logging
import asyncio
import time
from typing import Dict, Any, Optional, List
from app.services.ai_assistant.conversation.routers.base_router import BaseRouter
from app.services.ai_assistant.conversation.routers.unified_response import UnifiedResponse

logger = logging.getLogger(__name__)

class HybridIntentRouter(BaseRouter):
    """混合意图路由器"""
    
    def __init__(self, state_manager, enhanced_adapter, legacy_adapter):
        super().__init__("HybridIntentRouter")
        self.state_manager = state_manager
        self.enhanced_adapter = enhanced_adapter
        self.legacy_adapter = legacy_adapter
        
        # 初始化路由规则
        self._initialize_routing_rules()
        
        # 性能统计
        self.routing_stats = {
            "total_requests": 0,
            "enhanced_requests": 0,
            "legacy_requests": 0,
            "state_machine_requests": 0,
            "hybrid_requests": 0,
            "error_requests": 0
        }
        
        logger.info("混合意图路由器初始化完成")
    
    def _initialize_routing_rules(self):
        """初始化路由规则"""
        # 增强处理器优先的意图
        self.add_routing_rule("enhanced_priority", {
            "intents": {"fitness_advice"},
            "description": "复杂咨询优先使用增强识别器"
        })
        
        # 传统系统优先的意图
        self.add_routing_rule("legacy_priority", {
            "intents": {"exercise_action", "training_plan", "diet_advice"},
            "description": "专业化意图优先使用传统系统"
        })
        
        # 状态机优先的意图
        self.add_routing_rule("state_machine_priority", {
            "intents": {"general_chat", "help", "greeting"},
            "description": "对话类意图优先使用状态机"
        })
        
        # 混合处理的意图
        self.add_routing_rule("hybrid_processing", {
            "intents": {"complex_query", "multi_step_plan"},
            "description": "复杂查询使用混合处理"
        })
    
    async def route(self, intent: str, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """路由请求到合适的处理系统"""
        start_time = time.time()
        self.routing_stats["total_requests"] += 1
        
        try:
            # 确定路由策略
            routing_strategy = self._determine_routing_strategy(intent, context)
            logger.info(f"意图 {intent} 使用路由策略: {routing_strategy}")
            
            # 根据策略处理请求
            if routing_strategy == "enhanced":
                result = await self._handle_with_enhanced_system(intent, message, context)
                self.routing_stats["enhanced_requests"] += 1
            elif routing_strategy == "legacy":
                result = await self._handle_with_legacy_system(intent, message, context)
                self.routing_stats["legacy_requests"] += 1
            elif routing_strategy == "state_machine":
                result = await self._handle_with_state_machine(intent, message, context)
                self.routing_stats["state_machine_requests"] += 1
            elif routing_strategy == "hybrid":
                result = await self._handle_with_hybrid_evaluation(intent, message, context)
                self.routing_stats["hybrid_requests"] += 1
            else:
                # 默认使用状态机
                result = await self._handle_with_state_machine(intent, message, context)
                self.routing_stats["state_machine_requests"] += 1
            
            # 添加处理信息
            processing_time = time.time() - start_time
            result = UnifiedResponse.add_processing_info(
                result, processing_time, [f"routed_to_{routing_strategy}"]
            )
            
            return result
            
        except Exception as e:
            self.routing_stats["error_requests"] += 1
            logger.error(f"路由处理失败: {str(e)}")
            return UnifiedResponse.format_error_response(
                error_message=str(e),
                intent=intent,
                source_system="hybrid_router_error"
            )
    
    def _determine_routing_strategy(self, intent: str, context: Dict[str, Any]) -> str:
        """确定路由策略"""
        # 检查优先级规则
        for rule_name, rule_config in self.routing_rules.items():
            if intent in rule_config.get("intents", set()):
                if rule_name == "enhanced_priority":
                    return "enhanced"
                elif rule_name == "legacy_priority":
                    return "legacy"
                elif rule_name == "state_machine_priority":
                    return "state_machine"
                elif rule_name == "hybrid_processing":
                    return "hybrid"
        
        # 动态决策逻辑
        conversation_length = len(context.get("messages", []))
        message_complexity = self._analyze_message_complexity(context.get("message", ""))
        
        # 长对话优先使用状态机
        if conversation_length > 10:
            return "state_machine"
        
        # 高复杂度消息使用混合处理
        if message_complexity > 0.7:
            return "hybrid"
        
        # 中等复杂度使用增强系统
        if message_complexity > 0.4:
            return "enhanced"
        
        # 默认使用传统系统
        return "legacy"
    
    def _analyze_message_complexity(self, message: str) -> float:
        """分析消息复杂度"""
        if not message:
            return 0.0
        
        complexity_score = 0.0
        
        # 长度因子
        if len(message) > 100:
            complexity_score += 0.3
        
        # 关键词因子
        complex_keywords = ["计划", "方案", "如何", "为什么", "步骤", "过程", "原理"]
        keyword_count = sum(1 for keyword in complex_keywords if keyword in message)
        complexity_score += min(0.4, keyword_count * 0.1)
        
        # 问号数量
        question_count = message.count("？") + message.count("?")
        complexity_score += min(0.3, question_count * 0.1)
        
        return min(1.0, complexity_score)
    
    async def _handle_with_enhanced_system(self, intent: str, message: str, context: Dict) -> Dict:
        """使用增强系统处理"""
        try:
            # 重新识别意图
            enhanced_result = await self.enhanced_adapter.arecognize(message, context)
            
            # 使用识别结果更新上下文
            context["enhanced_intent"] = enhanced_result.intent_type
            context["enhanced_confidence"] = enhanced_result.confidence
            
            # 根据增强识别结果选择处理器
            if enhanced_result.confidence > 0.8:
                # 高置信度，使用传统系统处理
                return await self._handle_with_legacy_system(
                    enhanced_result.intent_type, message, context
                )
            else:
                # 低置信度，回退到状态机
                return await self._handle_with_state_machine(intent, message, context)
                
        except Exception as e:
            logger.error(f"增强系统处理失败: {str(e)}")
            return UnifiedResponse.format_error_response(
                f"增强系统处理失败: {str(e)}", intent, "enhanced_system_error"
            )
    
    async def _handle_with_legacy_system(self, intent: str, message: str, context: Dict) -> Dict:
        """使用传统系统处理"""
        try:
            # 检查传统系统是否支持该意图
            if not await self.legacy_adapter.can_handle(intent):
                logger.warning(f"传统系统不支持意图: {intent}，回退到状态机")
                return await self._handle_with_state_machine(intent, message, context)
            
            # 使用传统系统处理
            result = await self.legacy_adapter.handle_intent(intent, message, context)
            
            return UnifiedResponse.format_response(
                content=result.get("content", ""),
                intent=intent,
                confidence=result.get("confidence", 0.8),
                structured_data=result.get("structured_data"),
                source_system="legacy_system",
                metadata=result.get("metadata")
            )
            
        except Exception as e:
            logger.error(f"传统系统处理失败: {str(e)}")
            return UnifiedResponse.format_error_response(
                f"传统系统处理失败: {str(e)}", intent, "legacy_system_error"
            )
    
    async def _handle_with_state_machine(self, intent: str, message: str, context: Dict) -> Dict:
        """使用状态机处理"""
        try:
            conversation_id = context.get("conversation_id")
            if not conversation_id:
                raise ValueError("缺少conversation_id")
            
            current_state = await self.state_manager.get_current_state(conversation_id)
            
            result = await current_state.handle_message(
                message, intent, context.get("user_profile", {})
            )
            
            return UnifiedResponse.format_response(
                content=result.get("response", ""),
                intent=intent,
                confidence=result.get("confidence", 0.8),
                structured_data=result.get("structured_data"),
                source_system="state_machine",
                metadata=result.get("metadata")
            )
            
        except Exception as e:
            logger.error(f"状态机处理失败: {str(e)}")
            return UnifiedResponse.format_error_response(
                f"状态机处理失败: {str(e)}", intent, "state_machine_error"
            )
    
    async def _handle_with_hybrid_evaluation(self, intent: str, message: str, context: Dict) -> Dict:
        """混合评估处理"""
        try:
            # 并行调用多个系统
            tasks = []
            
            # 总是包含状态机
            tasks.append(self._handle_with_state_machine(intent, message, context))
            
            # 根据意图类型决定其他系统
            if await self.legacy_adapter.can_handle(intent):
                tasks.append(self._handle_with_legacy_system(intent, message, context))
            
            # 执行并行处理
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 过滤有效结果
            valid_results = [
                r for r in results 
                if not isinstance(r, Exception) and r.get("success", False)
            ]
            
            if not valid_results:
                return UnifiedResponse.format_error_response(
                    "所有处理系统都失败了", intent, "hybrid_total_failure"
                )
            
            # 选择最佳结果
            best_result = self._select_best_result(valid_results)
            
            return UnifiedResponse.format_hybrid_response(
                responses=valid_results,
                selected_response=best_result,
                selection_reason="基于质量评分选择"
            )
            
        except Exception as e:
            logger.error(f"混合评估处理失败: {str(e)}")
            return UnifiedResponse.format_error_response(
                f"混合评估处理失败: {str(e)}", intent, "hybrid_evaluation_error"
            )
    
    def _select_best_result(self, results: List[Dict]) -> Dict:
        """选择最佳结果"""
        if not results:
            return UnifiedResponse.format_error_response("没有可选择的结果", "unknown", "no_results")
        
        # 基于质量评分选择
        best_result = max(results, key=lambda x: x.get("quality_score", 0))
        
        logger.info(f"选择最佳结果: {best_result.get('source_system')} (质量评分: {best_result.get('quality_score', 0):.2f})")
        
        return best_result
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        base_health = await super().health_check()
        
        # 检查适配器健康状态
        enhanced_health = await self.enhanced_adapter.health_check() if self.enhanced_adapter else {"status": "unavailable"}
        legacy_health = await self.legacy_adapter.health_check() if self.legacy_adapter else {"status": "unavailable"}
        
        router_health = {
            "enhanced_adapter": enhanced_health,
            "legacy_adapter": legacy_health,
            "routing_stats": self.routing_stats.copy()
        }
        
        base_health.update(router_health)
        return base_health
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """获取路由统计信息"""
        stats = self.routing_stats.copy()
        
        if stats["total_requests"] > 0:
            stats["enhanced_percentage"] = stats["enhanced_requests"] / stats["total_requests"] * 100
            stats["legacy_percentage"] = stats["legacy_requests"] / stats["total_requests"] * 100
            stats["state_machine_percentage"] = stats["state_machine_requests"] / stats["total_requests"] * 100
            stats["hybrid_percentage"] = stats["hybrid_requests"] / stats["total_requests"] * 100
            stats["error_percentage"] = stats["error_requests"] / stats["total_requests"] * 100
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.routing_stats = {
            "total_requests": 0,
            "enhanced_requests": 0,
            "legacy_requests": 0,
            "state_machine_requests": 0,
            "hybrid_requests": 0,
            "error_requests": 0
        }
        logger.info("路由统计信息已重置")
