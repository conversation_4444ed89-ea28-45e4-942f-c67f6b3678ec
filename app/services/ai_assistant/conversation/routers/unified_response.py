"""
统一响应格式化器

为统一智能架构提供标准化的响应格式。
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)

class UnifiedResponse:
    """统一响应格式化器"""
    
    @staticmethod
    def format_response(
        content: str,
        intent: str,
        confidence: float,
        structured_data: Optional[Dict[str, Any]] = None,
        source_system: str = "unknown",
        metadata: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        格式化统一响应
        
        Args:
            content: 响应内容
            intent: 意图类型
            confidence: 置信度
            structured_data: 结构化数据
            source_system: 来源系统
            metadata: 元数据
            error: 错误信息
            
        Returns:
            统一格式的响应字典
        """
        response = {
            "content": content,
            "intent": intent,
            "confidence": max(0.0, min(1.0, confidence)),  # 确保在0-1范围内
            "source_system": source_system,
            "timestamp": datetime.now().isoformat(),
            "success": error is None
        }
        
        # 添加结构化数据
        if structured_data:
            response["structured_data"] = structured_data
        
        # 添加元数据
        if metadata:
            response["metadata"] = metadata
        
        # 添加错误信息
        if error:
            response["error"] = error
            response["success"] = False
        
        # 添加响应质量评分
        response["quality_score"] = UnifiedResponse._calculate_quality_score(
            content, confidence, structured_data, error
        )
        
        return response
    
    @staticmethod
    def format_streaming_response(
        content_chunk: str,
        intent: str,
        chunk_type: str = "content",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        格式化流式响应块
        
        Args:
            content_chunk: 内容块
            intent: 意图类型
            chunk_type: 块类型 (content, structured_data, metadata, complete)
            metadata: 元数据
            
        Returns:
            流式响应块
        """
        chunk = {
            "type": chunk_type,
            "content": content_chunk,
            "intent": intent,
            "timestamp": datetime.now().isoformat()
        }
        
        if metadata:
            chunk["metadata"] = metadata
        
        return chunk
    
    @staticmethod
    def format_error_response(
        error_message: str,
        intent: str = "unknown",
        source_system: str = "unknown",
        error_code: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            error_message: 错误消息
            intent: 意图类型
            source_system: 来源系统
            error_code: 错误代码
            
        Returns:
            错误响应字典
        """
        response = {
            "content": f"抱歉，处理您的请求时出现了问题: {error_message}",
            "intent": intent,
            "confidence": 0.0,
            "source_system": source_system,
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": error_message,
            "quality_score": 0.0
        }
        
        if error_code:
            response["error_code"] = error_code
        
        return response
    
    @staticmethod
    def format_hybrid_response(
        responses: List[Dict[str, Any]],
        selected_response: Dict[str, Any],
        selection_reason: str
    ) -> Dict[str, Any]:
        """
        格式化混合处理响应
        
        Args:
            responses: 所有响应列表
            selected_response: 选中的响应
            selection_reason: 选择原因
            
        Returns:
            混合响应字典
        """
        hybrid_response = selected_response.copy()
        
        # 添加混合处理信息
        hybrid_response["hybrid_info"] = {
            "total_responses": len(responses),
            "selection_reason": selection_reason,
            "alternative_sources": [
                r.get("source_system", "unknown") for r in responses 
                if r != selected_response
            ]
        }
        
        # 更新来源系统
        hybrid_response["source_system"] = f"hybrid_{selected_response.get('source_system', 'unknown')}"
        
        return hybrid_response
    
    @staticmethod
    def _calculate_quality_score(
        content: str,
        confidence: float,
        structured_data: Optional[Dict[str, Any]],
        error: Optional[str]
    ) -> float:
        """
        计算响应质量评分
        
        Args:
            content: 响应内容
            confidence: 置信度
            structured_data: 结构化数据
            error: 错误信息
            
        Returns:
            质量评分 (0.0 - 1.0)
        """
        if error:
            return 0.0
        
        score = 0.0
        
        # 置信度权重 (40%)
        score += confidence * 0.4
        
        # 内容质量权重 (40%)
        content_length = len(content.strip())
        if content_length == 0:
            content_score = 0.0
        elif content_length < 10:
            content_score = 0.3
        elif content_length < 50:
            content_score = 0.6
        elif content_length <= 500:
            content_score = 1.0
        else:
            content_score = 0.8  # 过长的内容可能质量不高
        
        score += content_score * 0.4
        
        # 结构化数据权重 (20%)
        if structured_data and len(structured_data) > 0:
            score += 0.2
        
        return min(1.0, score)
    
    @staticmethod
    def merge_responses(responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        合并多个响应
        
        Args:
            responses: 响应列表
            
        Returns:
            合并后的响应
        """
        if not responses:
            return UnifiedResponse.format_error_response("没有可合并的响应")
        
        if len(responses) == 1:
            return responses[0]
        
        # 选择质量评分最高的响应作为基础
        best_response = max(responses, key=lambda x: x.get("quality_score", 0))
        
        # 合并内容
        merged_content = best_response.get("content", "")
        
        # 合并结构化数据
        merged_structured_data = {}
        for response in responses:
            if response.get("structured_data"):
                merged_structured_data.update(response["structured_data"])
        
        # 合并元数据
        merged_metadata = {
            "merged_from": [r.get("source_system", "unknown") for r in responses],
            "merge_timestamp": datetime.now().isoformat()
        }
        
        for response in responses:
            if response.get("metadata"):
                merged_metadata.update(response["metadata"])
        
        # 创建合并响应
        merged_response = best_response.copy()
        merged_response["content"] = merged_content
        merged_response["source_system"] = "merged"
        merged_response["metadata"] = merged_metadata
        
        if merged_structured_data:
            merged_response["structured_data"] = merged_structured_data
        
        return merged_response
    
    @staticmethod
    def validate_response(response: Dict[str, Any]) -> bool:
        """
        验证响应格式
        
        Args:
            response: 响应字典
            
        Returns:
            是否有效
        """
        required_fields = ["content", "intent", "confidence", "source_system", "timestamp", "success"]
        
        for field in required_fields:
            if field not in response:
                logger.error(f"响应缺少必需字段: {field}")
                return False
        
        # 验证置信度范围
        confidence = response.get("confidence", 0)
        if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
            logger.error(f"置信度值无效: {confidence}")
            return False
        
        # 验证成功标志
        success = response.get("success")
        if not isinstance(success, bool):
            logger.error(f"成功标志无效: {success}")
            return False
        
        return True
    
    @staticmethod
    def add_processing_info(
        response: Dict[str, Any],
        processing_time: float,
        processing_steps: List[str]
    ) -> Dict[str, Any]:
        """
        添加处理信息
        
        Args:
            response: 原始响应
            processing_time: 处理时间（秒）
            processing_steps: 处理步骤
            
        Returns:
            添加处理信息后的响应
        """
        if "metadata" not in response:
            response["metadata"] = {}
        
        response["metadata"]["processing_info"] = {
            "processing_time_seconds": processing_time,
            "processing_steps": processing_steps,
            "steps_count": len(processing_steps)
        }
        
        return response
    
    @staticmethod
    def create_debug_response(
        original_response: Dict[str, Any],
        debug_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建调试响应
        
        Args:
            original_response: 原始响应
            debug_info: 调试信息
            
        Returns:
            包含调试信息的响应
        """
        debug_response = original_response.copy()
        
        if "metadata" not in debug_response:
            debug_response["metadata"] = {}
        
        debug_response["metadata"]["debug_info"] = debug_info
        debug_response["debug_mode"] = True
        
        return debug_response
