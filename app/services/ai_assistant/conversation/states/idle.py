"""
空闲状态模块

该模块实现了对话的默认状态，处理一般性聊天和分发到专门的意图状态。
"""

import logging
from typing import Dict, Any, Type, List, Optional

from app.services.ai_assistant.conversation.states.base import ConversationState
from app.services.ai_assistant.intent.recognition.recognizer import BaseIntentRecognizer
from app.services.ai_assistant.intent.recognition.factory import IntentRecognizerFactory
from app.services.ai_assistant.intent.handlers.factory import IntentHandlerFactory
from app.services.ai_assistant.intent.handlers.base import BaseIntentHandler
from app.services.ai_assistant.llm.proxy import DefaultLLMProxy
from app.services.ai_assistant.knowledge.retriever import default_retriever

# 统一架构相关导入
from app.core.unified_config import unified_settings

logger = logging.getLogger(__name__)

class IdleState(ConversationState):
    """
    空闲状态

    这是对话的默认状态，处理一般性聊天并根据识别的意图转换到其他专门的状态。
    """

    name = "idle"

    def __init__(self, context: Dict[str, Any]):
        """
        初始化空闲状态

        Args:
            context: 对话上下文
        """
        super().__init__(context)
        self.intent_recognizer_factory = IntentRecognizerFactory()
        self.intent_handler_factory = IntentHandlerFactory()

        # 根据统一架构配置选择意图识别器
        self.recognizer = self._initialize_recognizer()

        # 创建通用聊天处理器
        # 使用统一的LLM代理创建方法
        self.llm_proxy = self._create_llm_proxy()

        self.general_chat_handler = self.intent_handler_factory.create(
            "general_chat",
            llm_proxy=self.llm_proxy,
            knowledge_retriever=default_retriever
        )

        # 记录是否需要转换状态
        self.need_transition = False
        self.next_state_name = None

    async def process_message(self, message: str) -> Dict[str, Any]:
        """
        处理用户消息

        识别意图并决定是由本状态处理还是转换到其他状态。

        Args:
            message: 用户输入的消息

        Returns:
            处理结果，包含响应和状态信息
        """
        logger.info(f"IdleState处理消息: {message[:30]}...")

        # 识别意图
        intent_result = await self.recognizer.arecognize(message)
        intent_type = intent_result.intent_type
        confidence = intent_result.confidence

        logger.info(f"识别到意图: {intent_type}，置信度: {confidence}")

        # 检查是否需要转换到特定意图状态
        self.need_transition = self._check_transition(intent_type, confidence)

        if self.need_transition:
            # 记录下一个状态的名称，留给状态管理器处理转换
            self.next_state_name = self._get_state_for_intent(intent_type)
            logger.info(f"需要转换到状态: {self.next_state_name}")

            # 更新上下文
            self.update_context({
                "previous_intent": intent_type,
                "intent_parameters": intent_result.parameters
            })

            # 返回中间响应
            return {
                "response": f"我明白了，您想{self._get_intent_description(intent_type)}。",
                "intent_type": intent_type,
                "confidence": confidence,
                "transitioning": True,
                "next_state": self.next_state_name
            }
        else:
            # 由通用聊天处理器处理
            handler_result = await self.general_chat_handler.handle(
                intent_type,
                {"text": message},
                self.context
            )

            # 更新上下文
            self.update_context({
                "previous_intent": intent_type,
                "previous_response": handler_result.get("content", "")
            })

            # 返回处理结果
            return {
                "response": handler_result.get("content", ""),
                "intent_type": intent_type,
                "confidence": confidence,
                "transitioning": False,
                "suggested_next_intents": handler_result.get("suggested_next_intents", [])
            }

    def should_transition(self) -> bool:
        """
        检查是否应该转换到新状态

        Returns:
            如果需要转换，返回True；否则返回False
        """
        return self.need_transition

    def get_next_state(self) -> Type[ConversationState]:
        """
        获取下一个状态

        Returns:
            下一个状态的类
        """
        if not self.next_state_name:
            # 如果没有设置下一个状态，返回自身
            return IdleState

        # 根据状态名称导入相应的状态类
        if self.next_state_name == "fitness_advice":
            from app.services.ai_assistant.conversation.states.fitness_advice import FitnessAdviceState
            return FitnessAdviceState
        elif self.next_state_name == "training_plan":
            from app.services.ai_assistant.conversation.states.training_plan import TrainingPlanState
            return TrainingPlanState
        elif self.next_state_name == "exercise_action":
            from app.services.ai_assistant.conversation.states.exercise_action import ExerciseActionState
            return ExerciseActionState
        elif self.next_state_name == "diet_advice":
            from app.services.ai_assistant.conversation.states.diet_advice import DietAdviceState
            return DietAdviceState
        else:
            # 默认返回自身
            return IdleState

    @classmethod
    def can_handle(cls, intent: str) -> bool:
        """
        检查该状态是否可以处理指定的意图

        Args:
            intent: 意图类型

        Returns:
            如果可以处理该意图，返回True；否则返回False
        """
        # 空闲状态可以处理一般性聊天意图
        general_chat_intents = [
            "greeting", "farewell", "thanks", "help", "smalltalk",
            "unknown", "general_chat"
        ]
        return intent in general_chat_intents

    def _check_transition(self, intent_type: str, confidence: float) -> bool:
        """
        检查是否需要根据意图转换状态

        Args:
            intent_type: 意图类型
            confidence: 识别置信度

        Returns:
            如果需要转换，返回True；否则返回False
        """
        # 如果置信度太低，不转换
        if confidence < 0.3:  # 降低置信度阈值，便于测试
            return False

        # 一般性聊天意图不需要转换
        if IdleState.can_handle(intent_type):
            return False

        # 特定领域意图需要转换到对应状态
        specialized_intents = [
            "fitness_advice", "training_plan", "exercise_action",
            "diet_advice", "workout_tracking"
        ]

        return intent_type in specialized_intents

    def _get_state_for_intent(self, intent_type: str) -> str:
        """
        根据意图类型获取对应的状态名称

        Args:
            intent_type: 意图类型

        Returns:
            状态名称
        """
        # 意图类型到状态名称的映射
        intent_to_state = {
            "fitness_advice": "fitness_advice",
            "training_plan": "training_plan",
            "exercise_action": "exercise_action",
            "diet_advice": "diet_advice",
            "workout_tracking": "workout_tracking"
        }

        return intent_to_state.get(intent_type, "idle")

    def _get_intent_description(self, intent_type: str) -> str:
        """
        获取意图的中文描述

        Args:
            intent_type: 意图类型

        Returns:
            意图的中文描述
        """
        # 意图类型到描述的映射
        intent_descriptions = {
            "fitness_advice": "了解健身建议",
            "training_plan": "制定训练计划",
            "exercise_action": "了解特定的运动动作",
            "diet_advice": "获取饮食建议",
            "workout_tracking": "记录您的训练情况"
        }

        return intent_descriptions.get(intent_type, "继续我们的对话")

    def _initialize_recognizer(self):
        """
        根据统一架构配置初始化意图识别器

        Returns:
            配置的意图识别器实例
        """
        try:
            # 检查是否启用统一架构和增强识别器
            if (unified_settings.ENABLE_UNIFIED_ARCHITECTURE and
                unified_settings.ENABLE_ENHANCED_RECOGNIZER):

                logger.info("启用增强版意图识别器适配器")

                # 创建LLM代理（复用现有逻辑）
                llm_proxy = self._create_llm_proxy()

                # 导入并创建增强版适配器
                from app.services.ai_assistant.intent.adapters.enhanced_recognizer_adapter import EnhancedIntentRecognizerAdapter

                enhanced_adapter = EnhancedIntentRecognizerAdapter(llm_proxy)

                # 异步初始化适配器
                import asyncio
                try:
                    # 在事件循环中初始化
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果事件循环正在运行，创建任务
                        asyncio.create_task(enhanced_adapter.initialize())
                    else:
                        # 如果没有事件循环，同步运行
                        loop.run_until_complete(enhanced_adapter.initialize())
                except RuntimeError:
                    # 如果无法获取事件循环，延迟初始化
                    logger.warning("无法立即初始化增强适配器，将在首次使用时初始化")

                return enhanced_adapter

            else:
                logger.info("使用标准意图识别器")
                return self.intent_recognizer_factory.create_rule_based_recognizer()

        except Exception as e:
            logger.error(f"初始化增强识别器失败: {str(e)}")
            logger.info("回退到标准意图识别器")
            return self.intent_recognizer_factory.create_rule_based_recognizer()

    def _create_llm_proxy(self):
        """
        创建LLM代理（复用现有逻辑）

        Returns:
            LLM代理实例
        """
        from app.services.ai_assistant.llm.factory import LLMProxyFactory
        try:
            # 确保提供商已加载
            LLMProxyFactory.load_providers()
            llm_proxy = LLMProxyFactory.get_provider("qwen")
            logger.info("创建Qwen LLM代理")
            return llm_proxy
        except Exception as e:
            logger.error(f"无法创建Qwen代理: {str(e)}")
            # 尝试其他真实LLM提供商
            try:
                available_providers = list(LLMProxyFactory._providers.keys())
                logger.info(f"可用的LLM提供商: {available_providers}")

                # 尝试使用第一个可用的真实提供商
                if available_providers:
                    fallback_provider = available_providers[0]
                    llm_proxy = LLMProxyFactory.get_provider(fallback_provider)
                    logger.info(f"使用回退提供商: {fallback_provider}")
                    return llm_proxy
                else:
                    raise Exception("没有可用的LLM提供商")
            except Exception as e2:
                logger.error(f"所有LLM提供商都不可用: {str(e2)}")
                # 只有在所有真实提供商都失败时才使用默认代理
                from app.services.ai_assistant.llm.proxy import DefaultLLMProxy
                llm_proxy = DefaultLLMProxy()
                logger.warning("使用DefaultLLMProxy作为最后的回退选项")
                return llm_proxy