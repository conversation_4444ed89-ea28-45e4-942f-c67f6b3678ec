"""
复杂推理引擎

处理复杂的健身规划和建议逻辑，支持多层次推理。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger(__name__)

class ReasoningType(Enum):
    """推理类型枚举"""
    CAUSAL = "causal"          # 因果推理
    TEMPORAL = "temporal"      # 时间推理
    SPATIAL = "spatial"        # 空间推理
    LOGICAL = "logical"        # 逻辑推理
    ANALOGICAL = "analogical"  # 类比推理
    ABDUCTIVE = "abductive"    # 溯因推理

class ConfidenceLevel(Enum):
    """置信度级别"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"

@dataclass
class ReasoningStep:
    """推理步骤"""
    step_id: str
    reasoning_type: ReasoningType
    premise: Dict[str, Any]
    conclusion: Dict[str, Any]
    confidence: float
    evidence: List[Dict[str, Any]]
    timestamp: datetime

@dataclass
class ReasoningChain:
    """推理链"""
    chain_id: str
    user_id: str
    query: str
    steps: List[ReasoningStep]
    final_conclusion: Dict[str, Any]
    overall_confidence: float
    reasoning_time_ms: float
    created_at: datetime

@dataclass
class ReasoningContext:
    """推理上下文"""
    user_profile: Dict[str, Any]
    current_goals: List[Dict[str, Any]]
    historical_data: List[Dict[str, Any]]
    constraints: List[Dict[str, Any]]
    preferences: Dict[str, Any]

class ComplexReasoningEngine:
    """复杂推理引擎"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化复杂推理引擎
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.max_reasoning_depth = self.config.get("max_depth", 5)
        self.confidence_threshold = self.config.get("confidence_threshold", 0.7)
        self.reasoning_timeout_seconds = self.config.get("timeout_seconds", 30)
        
        # 推理规则库
        self.reasoning_rules = self._initialize_reasoning_rules()
        
        # 推理历史
        self.reasoning_history: Dict[str, List[ReasoningChain]] = {}
        
        # 统计信息
        self.stats = {
            "total_reasonings": 0,
            "by_type": {reasoning_type.value: 0 for reasoning_type in ReasoningType},
            "avg_confidence": 0.0,
            "avg_reasoning_time_ms": 0.0
        }
        
        logger.info("复杂推理引擎初始化完成")
    
    async def reason(
        self, 
        user_id: str, 
        query: str, 
        context: ReasoningContext
    ) -> ReasoningChain:
        """
        执行复杂推理
        
        Args:
            user_id: 用户ID
            query: 推理查询
            context: 推理上下文
            
        Returns:
            推理链结果
        """
        start_time = datetime.now()
        
        try:
            # 创建推理链
            chain_id = f"reasoning_{user_id}_{int(start_time.timestamp())}"
            reasoning_chain = ReasoningChain(
                chain_id=chain_id,
                user_id=user_id,
                query=query,
                steps=[],
                final_conclusion={},
                overall_confidence=0.0,
                reasoning_time_ms=0.0,
                created_at=start_time
            )
            
            # 分析查询类型
            query_analysis = await self._analyze_query(query, context)
            
            # 执行多层推理
            reasoning_steps = await self._execute_reasoning_steps(
                query_analysis, context, reasoning_chain
            )
            
            # 整合推理结果
            final_conclusion = await self._integrate_reasoning_results(reasoning_steps)
            
            # 计算整体置信度
            overall_confidence = await self._calculate_overall_confidence(reasoning_steps)
            
            # 完善推理链
            reasoning_chain.steps = reasoning_steps
            reasoning_chain.final_conclusion = final_conclusion
            reasoning_chain.overall_confidence = overall_confidence
            reasoning_chain.reasoning_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            # 存储推理历史
            await self._store_reasoning_history(reasoning_chain)
            
            # 更新统计信息
            await self._update_stats(reasoning_chain)
            
            logger.info(f"推理完成: {chain_id}, 置信度: {overall_confidence:.2f}")
            return reasoning_chain
            
        except Exception as e:
            logger.error(f"推理执行失败: {str(e)}")
            # 返回错误推理链
            return ReasoningChain(
                chain_id=f"error_{user_id}_{int(start_time.timestamp())}",
                user_id=user_id,
                query=query,
                steps=[],
                final_conclusion={"error": str(e)},
                overall_confidence=0.0,
                reasoning_time_ms=(datetime.now() - start_time).total_seconds() * 1000,
                created_at=start_time
            )
    
    async def _analyze_query(self, query: str, context: ReasoningContext) -> Dict[str, Any]:
        """分析查询类型和意图"""
        try:
            analysis = {
                "query_type": "fitness_planning",
                "required_reasoning_types": [],
                "complexity_level": "medium",
                "key_entities": [],
                "temporal_aspects": [],
                "causal_relationships": []
            }
            
            query_lower = query.lower()
            
            # 识别推理类型需求
            if any(word in query_lower for word in ["为什么", "原因", "因为", "导致"]):
                analysis["required_reasoning_types"].append(ReasoningType.CAUSAL)
            
            if any(word in query_lower for word in ["什么时候", "多久", "时间", "计划"]):
                analysis["required_reasoning_types"].append(ReasoningType.TEMPORAL)
            
            if any(word in query_lower for word in ["如何", "怎样", "步骤", "方法"]):
                analysis["required_reasoning_types"].append(ReasoningType.LOGICAL)
            
            if any(word in query_lower for word in ["类似", "像", "比较", "对比"]):
                analysis["required_reasoning_types"].append(ReasoningType.ANALOGICAL)
            
            # 提取关键实体
            fitness_entities = ["减肥", "增肌", "力量", "有氧", "瑜伽", "跑步", "游泳"]
            analysis["key_entities"] = [entity for entity in fitness_entities if entity in query]
            
            # 评估复杂度
            complexity_indicators = len(analysis["required_reasoning_types"]) + len(analysis["key_entities"])
            if complexity_indicators >= 4:
                analysis["complexity_level"] = "high"
            elif complexity_indicators >= 2:
                analysis["complexity_level"] = "medium"
            else:
                analysis["complexity_level"] = "low"
            
            return analysis
            
        except Exception as e:
            logger.error(f"查询分析失败: {str(e)}")
            return {"query_type": "unknown", "required_reasoning_types": [ReasoningType.LOGICAL]}
    
    async def _execute_reasoning_steps(
        self, 
        query_analysis: Dict[str, Any], 
        context: ReasoningContext,
        reasoning_chain: ReasoningChain
    ) -> List[ReasoningStep]:
        """执行推理步骤"""
        try:
            reasoning_steps = []
            required_types = query_analysis.get("required_reasoning_types", [ReasoningType.LOGICAL])
            
            # 为每种推理类型执行推理
            for reasoning_type in required_types:
                step = await self._execute_single_reasoning_step(
                    reasoning_type, query_analysis, context, reasoning_chain
                )
                if step:
                    reasoning_steps.append(step)
            
            # 如果没有特定推理类型，执行默认逻辑推理
            if not reasoning_steps:
                default_step = await self._execute_single_reasoning_step(
                    ReasoningType.LOGICAL, query_analysis, context, reasoning_chain
                )
                if default_step:
                    reasoning_steps.append(default_step)
            
            return reasoning_steps
            
        except Exception as e:
            logger.error(f"推理步骤执行失败: {str(e)}")
            return []
    
    async def _execute_single_reasoning_step(
        self,
        reasoning_type: ReasoningType,
        query_analysis: Dict[str, Any],
        context: ReasoningContext,
        reasoning_chain: ReasoningChain
    ) -> Optional[ReasoningStep]:
        """执行单个推理步骤"""
        try:
            step_id = f"step_{len(reasoning_chain.steps) + 1}_{reasoning_type.value}"
            
            if reasoning_type == ReasoningType.CAUSAL:
                return await self._causal_reasoning(step_id, query_analysis, context)
            elif reasoning_type == ReasoningType.TEMPORAL:
                return await self._temporal_reasoning(step_id, query_analysis, context)
            elif reasoning_type == ReasoningType.LOGICAL:
                return await self._logical_reasoning(step_id, query_analysis, context)
            elif reasoning_type == ReasoningType.ANALOGICAL:
                return await self._analogical_reasoning(step_id, query_analysis, context)
            else:
                return await self._default_reasoning(step_id, query_analysis, context)
                
        except Exception as e:
            logger.error(f"单步推理失败 ({reasoning_type.value}): {str(e)}")
            return None
    
    async def _causal_reasoning(
        self, 
        step_id: str, 
        query_analysis: Dict[str, Any], 
        context: ReasoningContext
    ) -> ReasoningStep:
        """因果推理"""
        try:
            # 分析用户目标和当前状态的因果关系
            user_goals = context.current_goals
            user_profile = context.user_profile
            
            premise = {
                "user_goals": user_goals,
                "current_fitness_level": user_profile.get("fitness_level", "beginner"),
                "available_time": user_profile.get("available_time", 30),
                "equipment": user_profile.get("equipment", [])
            }
            
            # 推理因果关系
            if "减肥" in query_analysis.get("key_entities", []):
                conclusion = {
                    "cause": "热量摄入大于消耗",
                    "effect": "体重增加",
                    "solution": "增加有氧运动，控制饮食",
                    "expected_outcome": "体重下降"
                }
                confidence = 0.85
            elif "增肌" in query_analysis.get("key_entities", []):
                conclusion = {
                    "cause": "肌肉纤维微损伤和蛋白质合成",
                    "effect": "肌肉增长",
                    "solution": "力量训练配合充足蛋白质",
                    "expected_outcome": "肌肉量增加"
                }
                confidence = 0.8
            else:
                conclusion = {
                    "cause": "缺乏规律运动",
                    "effect": "体能下降",
                    "solution": "制定规律的运动计划",
                    "expected_outcome": "体能改善"
                }
                confidence = 0.7
            
            evidence = [
                {"type": "scientific_principle", "content": "能量平衡原理"},
                {"type": "user_data", "content": premise}
            ]
            
            return ReasoningStep(
                step_id=step_id,
                reasoning_type=ReasoningType.CAUSAL,
                premise=premise,
                conclusion=conclusion,
                confidence=confidence,
                evidence=evidence,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"因果推理失败: {str(e)}")
            raise
    
    async def _temporal_reasoning(
        self, 
        step_id: str, 
        query_analysis: Dict[str, Any], 
        context: ReasoningContext
    ) -> ReasoningStep:
        """时间推理"""
        try:
            user_profile = context.user_profile
            available_time = user_profile.get("available_time", 30)
            fitness_level = user_profile.get("fitness_level", "beginner")
            
            premise = {
                "available_time_per_session": available_time,
                "fitness_level": fitness_level,
                "weekly_frequency": user_profile.get("weekly_frequency", 3)
            }
            
            # 基于时间的推理
            if available_time < 20:
                conclusion = {
                    "recommended_workout_type": "高强度间歇训练(HIIT)",
                    "session_duration": "15-20分钟",
                    "frequency": "每周4-5次",
                    "progression_timeline": "4-6周见效"
                }
                confidence = 0.8
            elif available_time < 45:
                conclusion = {
                    "recommended_workout_type": "混合训练",
                    "session_duration": "30-40分钟",
                    "frequency": "每周3-4次",
                    "progression_timeline": "6-8周见效"
                }
                confidence = 0.85
            else:
                conclusion = {
                    "recommended_workout_type": "分化训练",
                    "session_duration": "45-60分钟",
                    "frequency": "每周5-6次",
                    "progression_timeline": "8-12周见效"
                }
                confidence = 0.9
            
            evidence = [
                {"type": "time_constraint", "content": f"可用时间: {available_time}分钟"},
                {"type": "training_principle", "content": "训练频率与强度的反比关系"}
            ]
            
            return ReasoningStep(
                step_id=step_id,
                reasoning_type=ReasoningType.TEMPORAL,
                premise=premise,
                conclusion=conclusion,
                confidence=confidence,
                evidence=evidence,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"时间推理失败: {str(e)}")
            raise
    
    async def _logical_reasoning(
        self, 
        step_id: str, 
        query_analysis: Dict[str, Any], 
        context: ReasoningContext
    ) -> ReasoningStep:
        """逻辑推理"""
        try:
            user_profile = context.user_profile
            goals = context.current_goals
            constraints = context.constraints
            
            premise = {
                "user_goals": goals,
                "fitness_level": user_profile.get("fitness_level"),
                "constraints": constraints,
                "preferences": context.preferences
            }
            
            # 逻辑推理过程
            reasoning_steps = []
            
            # 步骤1: 目标分析
            if goals:
                primary_goal = goals[0] if goals else {"type": "general_fitness"}
                reasoning_steps.append(f"主要目标: {primary_goal.get('type', 'general_fitness')}")
            
            # 步骤2: 约束分析
            if constraints:
                constraint_analysis = [f"约束: {c.get('type', 'unknown')}" for c in constraints]
                reasoning_steps.extend(constraint_analysis)
            
            # 步骤3: 方案推导
            if "减肥" in str(goals):
                solution = "有氧运动 + 饮食控制"
            elif "增肌" in str(goals):
                solution = "力量训练 + 蛋白质补充"
            else:
                solution = "综合训练计划"
            
            reasoning_steps.append(f"推荐方案: {solution}")
            
            conclusion = {
                "logical_steps": reasoning_steps,
                "recommended_approach": solution,
                "reasoning_validity": "valid"
            }
            
            confidence = 0.8
            
            evidence = [
                {"type": "goal_analysis", "content": premise["user_goals"]},
                {"type": "constraint_analysis", "content": premise["constraints"]}
            ]
            
            return ReasoningStep(
                step_id=step_id,
                reasoning_type=ReasoningType.LOGICAL,
                premise=premise,
                conclusion=conclusion,
                confidence=confidence,
                evidence=evidence,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"逻辑推理失败: {str(e)}")
            raise
    
    async def _analogical_reasoning(
        self, 
        step_id: str, 
        query_analysis: Dict[str, Any], 
        context: ReasoningContext
    ) -> ReasoningStep:
        """类比推理"""
        try:
            user_profile = context.user_profile
            historical_data = context.historical_data
            
            premise = {
                "current_situation": user_profile,
                "historical_cases": historical_data
            }
            
            # 寻找相似案例
            similar_cases = []
            for case in historical_data:
                similarity_score = await self._calculate_similarity(user_profile, case)
                if similarity_score > 0.7:
                    similar_cases.append({
                        "case": case,
                        "similarity": similarity_score
                    })
            
            if similar_cases:
                best_case = max(similar_cases, key=lambda x: x["similarity"])
                conclusion = {
                    "analogous_case": best_case["case"],
                    "similarity_score": best_case["similarity"],
                    "recommended_approach": best_case["case"].get("successful_approach"),
                    "expected_results": best_case["case"].get("results")
                }
                confidence = best_case["similarity"]
            else:
                conclusion = {
                    "analogous_case": None,
                    "similarity_score": 0.0,
                    "recommended_approach": "基础训练方案",
                    "expected_results": "渐进式改善"
                }
                confidence = 0.5
            
            evidence = [
                {"type": "historical_data", "content": f"分析了 {len(historical_data)} 个历史案例"},
                {"type": "similarity_analysis", "content": f"找到 {len(similar_cases)} 个相似案例"}
            ]
            
            return ReasoningStep(
                step_id=step_id,
                reasoning_type=ReasoningType.ANALOGICAL,
                premise=premise,
                conclusion=conclusion,
                confidence=confidence,
                evidence=evidence,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"类比推理失败: {str(e)}")
            raise
    
    async def _default_reasoning(
        self, 
        step_id: str, 
        query_analysis: Dict[str, Any], 
        context: ReasoningContext
    ) -> ReasoningStep:
        """默认推理"""
        try:
            premise = {"query_analysis": query_analysis, "context": "default"}
            conclusion = {"recommendation": "基于常识的健身建议", "approach": "渐进式训练"}
            confidence = 0.6
            evidence = [{"type": "default", "content": "基础推理规则"}]
            
            return ReasoningStep(
                step_id=step_id,
                reasoning_type=ReasoningType.LOGICAL,
                premise=premise,
                conclusion=conclusion,
                confidence=confidence,
                evidence=evidence,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"默认推理失败: {str(e)}")
            raise
    
    async def _calculate_similarity(self, profile1: Dict[str, Any], profile2: Dict[str, Any]) -> float:
        """计算用户档案相似度"""
        try:
            similarity_factors = []
            
            # 比较健身水平
            if profile1.get("fitness_level") == profile2.get("fitness_level"):
                similarity_factors.append(0.3)
            
            # 比较目标
            goals1 = set(profile1.get("goals", []))
            goals2 = set(profile2.get("goals", []))
            goal_similarity = len(goals1.intersection(goals2)) / max(len(goals1.union(goals2)), 1)
            similarity_factors.append(goal_similarity * 0.4)
            
            # 比较可用时间
            time1 = profile1.get("available_time", 30)
            time2 = profile2.get("available_time", 30)
            time_similarity = 1 - abs(time1 - time2) / max(time1, time2, 1)
            similarity_factors.append(time_similarity * 0.3)
            
            return sum(similarity_factors)
            
        except Exception as e:
            logger.error(f"相似度计算失败: {str(e)}")
            return 0.0
    
    async def _integrate_reasoning_results(self, reasoning_steps: List[ReasoningStep]) -> Dict[str, Any]:
        """整合推理结果"""
        try:
            if not reasoning_steps:
                return {"error": "没有推理步骤"}
            
            # 收集所有结论
            conclusions = [step.conclusion for step in reasoning_steps]
            
            # 整合推荐方案
            recommendations = []
            for conclusion in conclusions:
                if "solution" in conclusion:
                    recommendations.append(conclusion["solution"])
                elif "recommended_approach" in conclusion:
                    recommendations.append(conclusion["recommended_approach"])
            
            # 生成最终建议
            final_recommendation = self._synthesize_recommendations(recommendations)
            
            return {
                "final_recommendation": final_recommendation,
                "reasoning_steps_count": len(reasoning_steps),
                "contributing_factors": [step.reasoning_type.value for step in reasoning_steps],
                "integrated_conclusion": "基于多维度推理的综合建议"
            }
            
        except Exception as e:
            logger.error(f"推理结果整合失败: {str(e)}")
            return {"error": str(e)}
    
    def _synthesize_recommendations(self, recommendations: List[str]) -> str:
        """合成推荐方案"""
        if not recommendations:
            return "建议制定个性化的健身计划"
        
        # 简化的合成逻辑
        unique_recommendations = list(set(recommendations))
        if len(unique_recommendations) == 1:
            return unique_recommendations[0]
        else:
            return f"综合建议: {'; '.join(unique_recommendations[:3])}"
    
    async def _calculate_overall_confidence(self, reasoning_steps: List[ReasoningStep]) -> float:
        """计算整体置信度"""
        if not reasoning_steps:
            return 0.0
        
        confidences = [step.confidence for step in reasoning_steps]
        return sum(confidences) / len(confidences)
    
    async def _store_reasoning_history(self, reasoning_chain: ReasoningChain):
        """存储推理历史"""
        try:
            user_id = reasoning_chain.user_id
            if user_id not in self.reasoning_history:
                self.reasoning_history[user_id] = []
            
            self.reasoning_history[user_id].append(reasoning_chain)
            
            # 限制历史记录数量
            max_history = self.config.get("max_history_per_user", 100)
            if len(self.reasoning_history[user_id]) > max_history:
                self.reasoning_history[user_id] = self.reasoning_history[user_id][-max_history:]
                
        except Exception as e:
            logger.error(f"推理历史存储失败: {str(e)}")
    
    async def _update_stats(self, reasoning_chain: ReasoningChain):
        """更新统计信息"""
        try:
            self.stats["total_reasonings"] += 1
            
            # 按类型统计
            for step in reasoning_chain.steps:
                self.stats["by_type"][step.reasoning_type.value] += 1
            
            # 更新平均置信度
            current_avg_confidence = self.stats["avg_confidence"]
            total_count = self.stats["total_reasonings"]
            new_avg_confidence = ((current_avg_confidence * (total_count - 1)) + reasoning_chain.overall_confidence) / total_count
            self.stats["avg_confidence"] = new_avg_confidence
            
            # 更新平均推理时间
            current_avg_time = self.stats["avg_reasoning_time_ms"]
            new_avg_time = ((current_avg_time * (total_count - 1)) + reasoning_chain.reasoning_time_ms) / total_count
            self.stats["avg_reasoning_time_ms"] = new_avg_time
            
        except Exception as e:
            logger.error(f"统计信息更新失败: {str(e)}")
    
    def _initialize_reasoning_rules(self) -> Dict[str, Any]:
        """初始化推理规则库"""
        return {
            "fitness_rules": {
                "weight_loss": "有氧运动 + 热量控制",
                "muscle_gain": "力量训练 + 蛋白质补充",
                "endurance": "有氧训练 + 渐进负荷",
                "strength": "力量训练 + 复合动作"
            },
            "temporal_rules": {
                "beginner": "每周3次，每次30分钟",
                "intermediate": "每周4-5次，每次45分钟",
                "advanced": "每周5-6次，每次60分钟"
            },
            "causal_rules": {
                "calorie_deficit": "减重",
                "calorie_surplus": "增重",
                "progressive_overload": "力量增长"
            }
        }
    
    def get_reasoning_stats(self) -> Dict[str, Any]:
        """获取推理统计信息"""
        return self.stats.copy()
    
    def get_user_reasoning_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户推理历史"""
        user_history = self.reasoning_history.get(user_id, [])
        recent_history = user_history[-limit:] if user_history else []
        return [chain.final_conclusion for chain in recent_history]
