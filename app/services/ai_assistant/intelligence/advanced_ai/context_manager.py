"""
上下文管理器

管理对话上下文、会话状态和用户档案信息。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

logger = logging.getLogger(__name__)

class ContextType(Enum):
    """上下文类型枚举"""
    CONVERSATION = "conversation"
    SESSION = "session"
    USER_PROFILE = "user_profile"
    DOMAIN = "domain"
    TEMPORAL = "temporal"
    SITUATIONAL = "situational"

class ContextScope(Enum):
    """上下文范围枚举"""
    IMMEDIATE = "immediate"      # 当前对话轮次
    SHORT_TERM = "short_term"    # 当前会话
    MEDIUM_TERM = "medium_term"  # 近期会话
    LONG_TERM = "long_term"      # 历史记录

@dataclass
class ContextItem:
    """上下文项"""
    item_id: str
    context_type: ContextType
    scope: ContextScope
    content: Dict[str, Any]
    importance: float
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    expiry_time: Optional[datetime] = None
    tags: List[str] = field(default_factory=list)

@dataclass
class ConversationContext:
    """对话上下文"""
    conversation_id: str
    user_id: str
    messages: List[Dict[str, Any]]
    current_topic: Optional[str]
    intent_history: List[str]
    context_items: List[ContextItem]
    metadata: Dict[str, Any]
    created_at: datetime
    last_updated: datetime

class ContextManager:
    """上下文管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化上下文管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.max_context_window = self.config.get("window_size", 1000)
        self.context_retention_hours = self.config.get("retention_hours", 24)
        self.auto_cleanup_enabled = self.config.get("auto_cleanup", True)
        
        # 上下文存储
        self.conversations: Dict[str, ConversationContext] = {}
        self.user_profiles: Dict[str, Dict[str, Any]] = {}
        self.domain_contexts: Dict[str, Dict[str, Any]] = {}
        
        # 上下文索引
        self.context_index: Dict[str, ContextItem] = {}
        
        # 统计信息
        self.stats = {
            "total_conversations": 0,
            "active_conversations": 0,
            "total_context_items": 0,
            "by_type": {context_type.value: 0 for context_type in ContextType},
            "by_scope": {scope.value: 0 for scope in ContextScope}
        }
        
        logger.info("上下文管理器初始化完成")
    
    async def create_conversation_context(
        self, 
        user_id: str, 
        conversation_id: Optional[str] = None
    ) -> str:
        """
        创建对话上下文
        
        Args:
            user_id: 用户ID
            conversation_id: 对话ID（可选）
            
        Returns:
            对话ID
        """
        try:
            if not conversation_id:
                conversation_id = f"conv_{user_id}_{int(datetime.now().timestamp())}"
            
            # 创建对话上下文
            conversation_context = ConversationContext(
                conversation_id=conversation_id,
                user_id=user_id,
                messages=[],
                current_topic=None,
                intent_history=[],
                context_items=[],
                metadata={},
                created_at=datetime.now(),
                last_updated=datetime.now()
            )
            
            # 存储对话上下文
            self.conversations[conversation_id] = conversation_context
            
            # 加载用户档案上下文
            await self._load_user_profile_context(user_id, conversation_context)
            
            # 更新统计信息
            self.stats["total_conversations"] += 1
            self.stats["active_conversations"] += 1
            
            logger.info(f"对话上下文创建成功: {conversation_id}")
            return conversation_id
            
        except Exception as e:
            logger.error(f"对话上下文创建失败: {str(e)}")
            raise
    
    async def add_message_to_context(
        self, 
        conversation_id: str, 
        message: Dict[str, Any]
    ) -> bool:
        """
        添加消息到上下文
        
        Args:
            conversation_id: 对话ID
            message: 消息内容
            
        Returns:
            添加是否成功
        """
        try:
            conversation = self.conversations.get(conversation_id)
            if not conversation:
                logger.warning(f"对话上下文不存在: {conversation_id}")
                return False
            
            # 添加消息
            conversation.messages.append({
                **message,
                "timestamp": datetime.now().isoformat(),
                "message_id": str(uuid.uuid4())
            })
            
            # 更新意图历史
            if "intent" in message:
                conversation.intent_history.append(message["intent"])
                # 限制意图历史长度
                if len(conversation.intent_history) > 10:
                    conversation.intent_history = conversation.intent_history[-10:]
            
            # 更新当前话题
            await self._update_current_topic(conversation, message)
            
            # 维护上下文窗口大小
            await self._maintain_context_window(conversation)
            
            # 提取并存储上下文项
            await self._extract_context_items(conversation, message)
            
            conversation.last_updated = datetime.now()
            
            logger.debug(f"消息添加到上下文: {conversation_id}")
            return True
            
        except Exception as e:
            logger.error(f"消息添加失败: {str(e)}")
            return False
    
    async def get_relevant_context(
        self, 
        conversation_id: str, 
        query: str,
        context_types: Optional[List[ContextType]] = None,
        max_items: int = 10
    ) -> List[ContextItem]:
        """
        获取相关上下文
        
        Args:
            conversation_id: 对话ID
            query: 查询内容
            context_types: 上下文类型过滤
            max_items: 最大返回项数
            
        Returns:
            相关上下文项列表
        """
        try:
            conversation = self.conversations.get(conversation_id)
            if not conversation:
                return []
            
            # 收集候选上下文项
            candidate_items = []
            
            # 从对话上下文中收集
            candidate_items.extend(conversation.context_items)
            
            # 从用户档案中收集
            user_profile_items = await self._get_user_profile_context_items(conversation.user_id)
            candidate_items.extend(user_profile_items)
            
            # 从领域上下文中收集
            domain_items = await self._get_domain_context_items("fitness")
            candidate_items.extend(domain_items)
            
            # 过滤上下文类型
            if context_types:
                candidate_items = [item for item in candidate_items if item.context_type in context_types]
            
            # 计算相关性并排序
            relevant_items = await self._rank_context_items(candidate_items, query, conversation)
            
            # 更新访问记录
            for item in relevant_items[:max_items]:
                await self._update_context_access(item)
            
            return relevant_items[:max_items]
            
        except Exception as e:
            logger.error(f"相关上下文获取失败: {str(e)}")
            return []
    
    async def update_user_profile_context(
        self, 
        user_id: str, 
        profile_updates: Dict[str, Any]
    ) -> bool:
        """
        更新用户档案上下文
        
        Args:
            user_id: 用户ID
            profile_updates: 档案更新内容
            
        Returns:
            更新是否成功
        """
        try:
            if user_id not in self.user_profiles:
                self.user_profiles[user_id] = {}
            
            # 更新用户档案
            self.user_profiles[user_id].update(profile_updates)
            self.user_profiles[user_id]["last_updated"] = datetime.now().isoformat()
            
            # 创建或更新用户档案上下文项
            profile_context_item = ContextItem(
                item_id=f"profile_{user_id}",
                context_type=ContextType.USER_PROFILE,
                scope=ContextScope.LONG_TERM,
                content=self.user_profiles[user_id],
                importance=0.9,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                tags=["user_profile", "persistent"]
            )
            
            self.context_index[profile_context_item.item_id] = profile_context_item
            
            logger.info(f"用户档案上下文更新成功: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"用户档案上下文更新失败: {str(e)}")
            return False
    
    async def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """获取对话摘要"""
        try:
            conversation = self.conversations.get(conversation_id)
            if not conversation:
                return {"error": "对话不存在"}
            
            # 生成摘要
            summary = {
                "conversation_id": conversation_id,
                "user_id": conversation.user_id,
                "message_count": len(conversation.messages),
                "current_topic": conversation.current_topic,
                "intent_history": conversation.intent_history,
                "context_items_count": len(conversation.context_items),
                "duration_minutes": (conversation.last_updated - conversation.created_at).total_seconds() / 60,
                "created_at": conversation.created_at.isoformat(),
                "last_updated": conversation.last_updated.isoformat()
            }
            
            # 添加最近消息摘要
            recent_messages = conversation.messages[-5:] if conversation.messages else []
            summary["recent_messages"] = [
                {
                    "role": msg.get("role", "unknown"),
                    "content_preview": msg.get("content", "")[:100],
                    "timestamp": msg.get("timestamp")
                }
                for msg in recent_messages
            ]
            
            return summary
            
        except Exception as e:
            logger.error(f"对话摘要获取失败: {str(e)}")
            return {"error": str(e)}
    
    async def _load_user_profile_context(
        self, 
        user_id: str, 
        conversation_context: ConversationContext
    ):
        """加载用户档案上下文"""
        try:
            user_profile = self.user_profiles.get(user_id, {})
            
            if user_profile:
                profile_context_item = ContextItem(
                    item_id=f"profile_{user_id}",
                    context_type=ContextType.USER_PROFILE,
                    scope=ContextScope.LONG_TERM,
                    content=user_profile,
                    importance=0.9,
                    created_at=datetime.now(),
                    last_accessed=datetime.now(),
                    tags=["user_profile"]
                )
                
                conversation_context.context_items.append(profile_context_item)
                self.context_index[profile_context_item.item_id] = profile_context_item
                
        except Exception as e:
            logger.error(f"用户档案上下文加载失败: {str(e)}")
    
    async def _update_current_topic(
        self, 
        conversation: ConversationContext, 
        message: Dict[str, Any]
    ):
        """更新当前话题"""
        try:
            # 简化的话题识别
            content = message.get("content", "").lower()
            
            fitness_topics = {
                "减肥": ["减肥", "减重", "瘦身"],
                "增肌": ["增肌", "肌肉", "力量"],
                "有氧": ["有氧", "跑步", "游泳"],
                "营养": ["营养", "饮食", "食物"],
                "计划": ["计划", "安排", "目标"]
            }
            
            for topic, keywords in fitness_topics.items():
                if any(keyword in content for keyword in keywords):
                    conversation.current_topic = topic
                    break
                    
        except Exception as e:
            logger.error(f"话题更新失败: {str(e)}")
    
    async def _maintain_context_window(self, conversation: ConversationContext):
        """维护上下文窗口大小"""
        try:
            # 限制消息数量
            if len(conversation.messages) > self.max_context_window:
                # 保留最近的消息
                conversation.messages = conversation.messages[-self.max_context_window:]
            
            # 清理过期的上下文项
            current_time = datetime.now()
            valid_context_items = []
            
            for item in conversation.context_items:
                if item.expiry_time and current_time > item.expiry_time:
                    # 从索引中移除过期项
                    if item.item_id in self.context_index:
                        del self.context_index[item.item_id]
                else:
                    valid_context_items.append(item)
            
            conversation.context_items = valid_context_items
            
        except Exception as e:
            logger.error(f"上下文窗口维护失败: {str(e)}")
    
    async def _extract_context_items(
        self, 
        conversation: ConversationContext, 
        message: Dict[str, Any]
    ):
        """从消息中提取上下文项"""
        try:
            # 提取实体和关键信息
            content = message.get("content", "")
            intent = message.get("intent", "")
            
            # 创建消息上下文项
            message_context_item = ContextItem(
                item_id=f"msg_{conversation.conversation_id}_{len(conversation.messages)}",
                context_type=ContextType.CONVERSATION,
                scope=ContextScope.SHORT_TERM,
                content={
                    "message": content,
                    "intent": intent,
                    "role": message.get("role", "user")
                },
                importance=0.5,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                expiry_time=datetime.now() + timedelta(hours=self.context_retention_hours),
                tags=["message", intent] if intent else ["message"]
            )
            
            conversation.context_items.append(message_context_item)
            self.context_index[message_context_item.item_id] = message_context_item
            
            # 提取特定领域的上下文项
            if "健身" in content or "运动" in content:
                fitness_context_item = ContextItem(
                    item_id=f"fitness_{conversation.conversation_id}_{len(conversation.messages)}",
                    context_type=ContextType.DOMAIN,
                    scope=ContextScope.MEDIUM_TERM,
                    content={"domain": "fitness", "content": content},
                    importance=0.7,
                    created_at=datetime.now(),
                    last_accessed=datetime.now(),
                    tags=["fitness", "domain"]
                )
                
                conversation.context_items.append(fitness_context_item)
                self.context_index[fitness_context_item.item_id] = fitness_context_item
                
        except Exception as e:
            logger.error(f"上下文项提取失败: {str(e)}")
    
    async def _rank_context_items(
        self, 
        items: List[ContextItem], 
        query: str, 
        conversation: ConversationContext
    ) -> List[ContextItem]:
        """对上下文项进行相关性排序"""
        try:
            scored_items = []
            
            for item in items:
                score = await self._calculate_relevance_score(item, query, conversation)
                scored_items.append((item, score))
            
            # 按分数排序
            scored_items.sort(key=lambda x: x[1], reverse=True)
            
            return [item for item, score in scored_items if score > 0.1]
            
        except Exception as e:
            logger.error(f"上下文项排序失败: {str(e)}")
            return items
    
    async def _calculate_relevance_score(
        self, 
        item: ContextItem, 
        query: str, 
        conversation: ConversationContext
    ) -> float:
        """计算上下文项的相关性分数"""
        try:
            score = 0.0
            
            # 基础重要性分数
            score += item.importance * 0.3
            
            # 内容相关性
            content_str = json.dumps(item.content, ensure_ascii=False).lower()
            query_lower = query.lower()
            
            # 关键词匹配
            query_words = query_lower.split()
            matching_words = sum(1 for word in query_words if word in content_str)
            if query_words:
                score += (matching_words / len(query_words)) * 0.4
            
            # 时间衰减
            time_diff = (datetime.now() - item.last_accessed).total_seconds() / 3600  # 小时
            time_decay = max(0, 1 - time_diff / 24)  # 24小时内线性衰减
            score += time_decay * 0.2
            
            # 访问频率
            access_bonus = min(item.access_count / 10, 0.1)  # 最多加0.1分
            score += access_bonus
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"相关性分数计算失败: {str(e)}")
            return 0.0
    
    async def _get_user_profile_context_items(self, user_id: str) -> List[ContextItem]:
        """获取用户档案上下文项"""
        try:
            profile_item_id = f"profile_{user_id}"
            if profile_item_id in self.context_index:
                return [self.context_index[profile_item_id]]
            return []
        except Exception as e:
            logger.error(f"用户档案上下文项获取失败: {str(e)}")
            return []
    
    async def _get_domain_context_items(self, domain: str) -> List[ContextItem]:
        """获取领域上下文项"""
        try:
            domain_items = []
            for item in self.context_index.values():
                if (item.context_type == ContextType.DOMAIN and 
                    item.content.get("domain") == domain):
                    domain_items.append(item)
            return domain_items
        except Exception as e:
            logger.error(f"领域上下文项获取失败: {str(e)}")
            return []
    
    async def _update_context_access(self, item: ContextItem):
        """更新上下文访问记录"""
        try:
            item.last_accessed = datetime.now()
            item.access_count += 1
        except Exception as e:
            logger.error(f"上下文访问记录更新失败: {str(e)}")
    
    async def cleanup_expired_contexts(self):
        """清理过期上下文"""
        if not self.auto_cleanup_enabled:
            return
        
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=self.context_retention_hours)
            
            # 清理过期对话
            expired_conversations = []
            for conv_id, conversation in self.conversations.items():
                if conversation.last_updated < cutoff_time:
                    expired_conversations.append(conv_id)
            
            for conv_id in expired_conversations:
                del self.conversations[conv_id]
                self.stats["active_conversations"] -= 1
            
            # 清理过期上下文项
            expired_items = []
            for item_id, item in self.context_index.items():
                if item.expiry_time and current_time > item.expiry_time:
                    expired_items.append(item_id)
            
            for item_id in expired_items:
                del self.context_index[item_id]
            
            logger.info(f"上下文清理完成: 删除 {len(expired_conversations)} 个对话, {len(expired_items)} 个上下文项")
            
        except Exception as e:
            logger.error(f"上下文清理失败: {str(e)}")
    
    def get_context_stats(self) -> Dict[str, Any]:
        """获取上下文统计信息"""
        return {
            "stats": self.stats.copy(),
            "active_conversations": len(self.conversations),
            "total_context_items": len(self.context_index),
            "user_profiles": len(self.user_profiles),
            "config": {
                "max_context_window": self.max_context_window,
                "retention_hours": self.context_retention_hours,
                "auto_cleanup_enabled": self.auto_cleanup_enabled
            }
        }
