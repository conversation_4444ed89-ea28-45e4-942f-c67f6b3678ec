"""
长期记忆系统

维护用户的长期健身历史、偏好和成就记录。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

logger = logging.getLogger(__name__)

class MemoryType(Enum):
    """记忆类型枚举"""
    INTERACTION = "interaction"
    PREFERENCE = "preference"
    ACHIEVEMENT = "achievement"
    GOAL = "goal"
    WORKOUT = "workout"
    NUTRITION = "nutrition"
    PROGRESS = "progress"
    MILESTONE = "milestone"

class MemoryImportance(Enum):
    """记忆重要性枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class Memory:
    """记忆数据结构"""
    memory_id: str
    user_id: str
    memory_type: MemoryType
    content: Dict[str, Any]
    importance: MemoryImportance
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    tags: List[str] = field(default_factory=list)
    related_memories: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "memory_id": self.memory_id,
            "user_id": self.user_id,
            "memory_type": self.memory_type.value,
            "content": self.content,
            "importance": self.importance.value,
            "created_at": self.created_at.isoformat(),
            "last_accessed": self.last_accessed.isoformat(),
            "access_count": self.access_count,
            "tags": self.tags,
            "related_memories": self.related_memories,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Memory":
        """从字典创建记忆实例"""
        return cls(
            memory_id=data["memory_id"],
            user_id=data["user_id"],
            memory_type=MemoryType(data["memory_type"]),
            content=data["content"],
            importance=MemoryImportance(data["importance"]),
            created_at=datetime.fromisoformat(data["created_at"]),
            last_accessed=datetime.fromisoformat(data["last_accessed"]),
            access_count=data.get("access_count", 0),
            tags=data.get("tags", []),
            related_memories=data.get("related_memories", []),
            metadata=data.get("metadata", {})
        )

@dataclass
class MemoryQuery:
    """记忆查询条件"""
    user_id: str
    memory_types: Optional[List[MemoryType]] = None
    tags: Optional[List[str]] = None
    importance_levels: Optional[List[MemoryImportance]] = None
    date_range: Optional[Tuple[datetime, datetime]] = None
    content_keywords: Optional[List[str]] = None
    limit: int = 10
    sort_by: str = "last_accessed"  # created_at, last_accessed, access_count, importance

class LongTermMemorySystem:
    """长期记忆系统"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化长期记忆系统
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.retention_days = self.config.get("retention_days", 30)
        self.max_memories_per_user = self.config.get("max_memories_per_user", 1000)
        self.auto_cleanup_enabled = self.config.get("auto_cleanup_enabled", True)
        
        # 内存存储（生产环境应使用数据库）
        self.memories: Dict[str, List[Memory]] = {}
        self.memory_index: Dict[str, Memory] = {}
        
        # 统计信息
        self.stats = {
            "total_memories": 0,
            "by_type": {memory_type.value: 0 for memory_type in MemoryType},
            "by_importance": {importance.value: 0 for importance in MemoryImportance},
            "total_users": 0
        }
        
        logger.info("长期记忆系统初始化完成")
    
    async def store_memory(
        self, 
        user_id: str, 
        memory_type: MemoryType, 
        content: Dict[str, Any],
        importance: MemoryImportance = MemoryImportance.MEDIUM,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        存储记忆
        
        Args:
            user_id: 用户ID
            memory_type: 记忆类型
            content: 记忆内容
            importance: 重要性级别
            tags: 标签列表
            metadata: 元数据
            
        Returns:
            记忆ID
        """
        try:
            # 创建记忆实例
            memory = Memory(
                memory_id=str(uuid.uuid4()),
                user_id=user_id,
                memory_type=memory_type,
                content=content,
                importance=importance,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                tags=tags or [],
                metadata=metadata or {}
            )
            
            # 存储记忆
            if user_id not in self.memories:
                self.memories[user_id] = []
            
            self.memories[user_id].append(memory)
            self.memory_index[memory.memory_id] = memory
            
            # 检查存储限制
            await self._enforce_storage_limits(user_id)
            
            # 更新统计信息
            await self._update_stats(memory, operation="add")
            
            # 建立关联
            await self._establish_memory_relations(memory)
            
            logger.info(f"记忆存储成功: {memory.memory_id} ({memory_type.value})")
            return memory.memory_id
            
        except Exception as e:
            logger.error(f"记忆存储失败: {str(e)}")
            raise
    
    async def retrieve_memories(self, query: MemoryQuery) -> List[Memory]:
        """
        检索记忆
        
        Args:
            query: 查询条件
            
        Returns:
            匹配的记忆列表
        """
        try:
            user_memories = self.memories.get(query.user_id, [])
            
            if not user_memories:
                return []
            
            # 应用过滤条件
            filtered_memories = await self._filter_memories(user_memories, query)
            
            # 排序
            sorted_memories = await self._sort_memories(filtered_memories, query.sort_by)
            
            # 限制数量
            result_memories = sorted_memories[:query.limit]
            
            # 更新访问记录
            for memory in result_memories:
                await self._update_access_record(memory)
            
            logger.info(f"记忆检索完成: 用户 {query.user_id}, 返回 {len(result_memories)} 条记忆")
            return result_memories
            
        except Exception as e:
            logger.error(f"记忆检索失败: {str(e)}")
            return []
    
    async def get_memory_by_id(self, memory_id: str) -> Optional[Memory]:
        """根据ID获取记忆"""
        try:
            memory = self.memory_index.get(memory_id)
            if memory:
                await self._update_access_record(memory)
            return memory
        except Exception as e:
            logger.error(f"记忆获取失败: {str(e)}")
            return None
    
    async def update_memory(
        self, 
        memory_id: str, 
        content: Optional[Dict[str, Any]] = None,
        importance: Optional[MemoryImportance] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        更新记忆
        
        Args:
            memory_id: 记忆ID
            content: 新内容
            importance: 新重要性级别
            tags: 新标签
            metadata: 新元数据
            
        Returns:
            更新是否成功
        """
        try:
            memory = self.memory_index.get(memory_id)
            if not memory:
                logger.warning(f"记忆不存在: {memory_id}")
                return False
            
            # 更新字段
            if content is not None:
                memory.content = content
            if importance is not None:
                memory.importance = importance
            if tags is not None:
                memory.tags = tags
            if metadata is not None:
                memory.metadata = metadata
            
            memory.last_accessed = datetime.now()
            
            logger.info(f"记忆更新成功: {memory_id}")
            return True
            
        except Exception as e:
            logger.error(f"记忆更新失败: {str(e)}")
            return False
    
    async def delete_memory(self, memory_id: str) -> bool:
        """删除记忆"""
        try:
            memory = self.memory_index.get(memory_id)
            if not memory:
                return False
            
            # 从用户记忆列表中移除
            user_memories = self.memories.get(memory.user_id, [])
            self.memories[memory.user_id] = [m for m in user_memories if m.memory_id != memory_id]
            
            # 从索引中移除
            del self.memory_index[memory_id]
            
            # 更新统计信息
            await self._update_stats(memory, operation="remove")
            
            logger.info(f"记忆删除成功: {memory_id}")
            return True
            
        except Exception as e:
            logger.error(f"记忆删除失败: {str(e)}")
            return False
    
    async def get_user_memory_summary(self, user_id: str) -> Dict[str, Any]:
        """获取用户记忆摘要"""
        try:
            user_memories = self.memories.get(user_id, [])
            
            if not user_memories:
                return {
                    "user_id": user_id,
                    "total_memories": 0,
                    "by_type": {},
                    "by_importance": {},
                    "oldest_memory": None,
                    "newest_memory": None,
                    "most_accessed": None
                }
            
            # 统计分析
            by_type = {}
            by_importance = {}
            
            for memory in user_memories:
                # 按类型统计
                memory_type = memory.memory_type.value
                by_type[memory_type] = by_type.get(memory_type, 0) + 1
                
                # 按重要性统计
                importance = memory.importance.value
                by_importance[importance] = by_importance.get(importance, 0) + 1
            
            # 找出特殊记忆
            oldest_memory = min(user_memories, key=lambda m: m.created_at)
            newest_memory = max(user_memories, key=lambda m: m.created_at)
            most_accessed = max(user_memories, key=lambda m: m.access_count)
            
            return {
                "user_id": user_id,
                "total_memories": len(user_memories),
                "by_type": by_type,
                "by_importance": by_importance,
                "oldest_memory": {
                    "id": oldest_memory.memory_id,
                    "type": oldest_memory.memory_type.value,
                    "created_at": oldest_memory.created_at.isoformat()
                },
                "newest_memory": {
                    "id": newest_memory.memory_id,
                    "type": newest_memory.memory_type.value,
                    "created_at": newest_memory.created_at.isoformat()
                },
                "most_accessed": {
                    "id": most_accessed.memory_id,
                    "type": most_accessed.memory_type.value,
                    "access_count": most_accessed.access_count
                }
            }
            
        except Exception as e:
            logger.error(f"用户记忆摘要获取失败: {str(e)}")
            return {"user_id": user_id, "error": str(e)}
    
    async def _filter_memories(self, memories: List[Memory], query: MemoryQuery) -> List[Memory]:
        """过滤记忆"""
        filtered = memories
        
        # 按类型过滤
        if query.memory_types:
            filtered = [m for m in filtered if m.memory_type in query.memory_types]
        
        # 按标签过滤
        if query.tags:
            filtered = [m for m in filtered if any(tag in m.tags for tag in query.tags)]
        
        # 按重要性过滤
        if query.importance_levels:
            filtered = [m for m in filtered if m.importance in query.importance_levels]
        
        # 按日期范围过滤
        if query.date_range:
            start_date, end_date = query.date_range
            filtered = [m for m in filtered if start_date <= m.created_at <= end_date]
        
        # 按内容关键词过滤
        if query.content_keywords:
            filtered = [m for m in filtered if self._content_matches_keywords(m.content, query.content_keywords)]
        
        return filtered
    
    async def _sort_memories(self, memories: List[Memory], sort_by: str) -> List[Memory]:
        """排序记忆"""
        if sort_by == "created_at":
            return sorted(memories, key=lambda m: m.created_at, reverse=True)
        elif sort_by == "last_accessed":
            return sorted(memories, key=lambda m: m.last_accessed, reverse=True)
        elif sort_by == "access_count":
            return sorted(memories, key=lambda m: m.access_count, reverse=True)
        elif sort_by == "importance":
            importance_order = {
                MemoryImportance.CRITICAL: 4,
                MemoryImportance.HIGH: 3,
                MemoryImportance.MEDIUM: 2,
                MemoryImportance.LOW: 1
            }
            return sorted(memories, key=lambda m: importance_order[m.importance], reverse=True)
        else:
            return memories
    
    def _content_matches_keywords(self, content: Dict[str, Any], keywords: List[str]) -> bool:
        """检查内容是否匹配关键词"""
        content_str = json.dumps(content, ensure_ascii=False).lower()
        return any(keyword.lower() in content_str for keyword in keywords)
    
    async def _update_access_record(self, memory: Memory):
        """更新访问记录"""
        memory.last_accessed = datetime.now()
        memory.access_count += 1
    
    async def _enforce_storage_limits(self, user_id: str):
        """强制执行存储限制"""
        user_memories = self.memories.get(user_id, [])
        
        if len(user_memories) > self.max_memories_per_user:
            # 按重要性和访问频率排序，删除最不重要的记忆
            sorted_memories = sorted(
                user_memories,
                key=lambda m: (m.importance.value, m.access_count, m.last_accessed)
            )
            
            # 保留最重要的记忆
            self.memories[user_id] = sorted_memories[-self.max_memories_per_user:]
            
            # 从索引中移除被删除的记忆
            removed_memories = sorted_memories[:-self.max_memories_per_user]
            for memory in removed_memories:
                if memory.memory_id in self.memory_index:
                    del self.memory_index[memory.memory_id]
            
            logger.info(f"用户 {user_id} 记忆清理完成，删除 {len(removed_memories)} 条记忆")
    
    async def _establish_memory_relations(self, memory: Memory):
        """建立记忆关联"""
        try:
            user_memories = self.memories.get(memory.user_id, [])
            
            # 寻找相关记忆
            related_memories = []
            for existing_memory in user_memories:
                if existing_memory.memory_id == memory.memory_id:
                    continue
                
                # 检查类型相关性
                if existing_memory.memory_type == memory.memory_type:
                    related_memories.append(existing_memory.memory_id)
                
                # 检查标签相关性
                if any(tag in existing_memory.tags for tag in memory.tags):
                    related_memories.append(existing_memory.memory_id)
            
            # 限制关联数量
            memory.related_memories = related_memories[:5]
            
        except Exception as e:
            logger.error(f"记忆关联建立失败: {str(e)}")
    
    async def _update_stats(self, memory: Memory, operation: str):
        """更新统计信息"""
        try:
            if operation == "add":
                self.stats["total_memories"] += 1
                self.stats["by_type"][memory.memory_type.value] += 1
                self.stats["by_importance"][memory.importance.value] += 1
                
                # 更新用户数量
                if memory.user_id not in self.memories or len(self.memories[memory.user_id]) == 1:
                    self.stats["total_users"] += 1
                    
            elif operation == "remove":
                self.stats["total_memories"] -= 1
                self.stats["by_type"][memory.memory_type.value] -= 1
                self.stats["by_importance"][memory.importance.value] -= 1
                
        except Exception as e:
            logger.error(f"统计信息更新失败: {str(e)}")
    
    async def cleanup_expired_memories(self):
        """清理过期记忆"""
        if not self.auto_cleanup_enabled:
            return
        
        try:
            cutoff_date = datetime.now() - timedelta(days=self.retention_days)
            total_removed = 0
            
            for user_id, user_memories in list(self.memories.items()):
                # 过滤出未过期的记忆
                valid_memories = []
                for memory in user_memories:
                    # 重要记忆不删除
                    if memory.importance in [MemoryImportance.HIGH, MemoryImportance.CRITICAL]:
                        valid_memories.append(memory)
                    # 检查是否过期
                    elif memory.created_at >= cutoff_date:
                        valid_memories.append(memory)
                    else:
                        # 从索引中移除
                        if memory.memory_id in self.memory_index:
                            del self.memory_index[memory.memory_id]
                        total_removed += 1
                
                # 更新用户记忆列表
                if valid_memories:
                    self.memories[user_id] = valid_memories
                else:
                    del self.memories[user_id]
            
            logger.info(f"记忆清理完成，删除 {total_removed} 条过期记忆")
            
        except Exception as e:
            logger.error(f"记忆清理失败: {str(e)}")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        return {
            "stats": self.stats.copy(),
            "config": {
                "retention_days": self.retention_days,
                "max_memories_per_user": self.max_memories_per_user,
                "auto_cleanup_enabled": self.auto_cleanup_enabled
            },
            "memory_distribution": {
                user_id: len(memories) for user_id, memories in self.memories.items()
            }
        }
