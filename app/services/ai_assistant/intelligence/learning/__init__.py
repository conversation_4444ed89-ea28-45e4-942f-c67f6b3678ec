"""
智能学习模块

实现用户行为学习、适应性引擎和个性化服务。
"""

from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

# 学习模块配置
LEARNING_CONFIG = {
    "min_interactions": 5,
    "learning_window_days": 30,
    "adaptation_threshold": 0.75,
    "personalization_factors": [
        "exercise_preferences",
        "difficulty_level",
        "time_preferences", 
        "goal_types",
        "interaction_patterns"
    ]
}

def initialize_learning_module(config: Optional[Dict[str, Any]] = None) -> bool:
    """初始化学习模块"""
    try:
        global LEARNING_CONFIG
        if config:
            LEARNING_CONFIG.update(config)
        
        logger.info("学习模块初始化成功")
        return True
    except Exception as e:
        logger.error(f"学习模块初始化失败: {str(e)}")
        return False

# 导出主要组件
__all__ = [
    "LEARNING_CONFIG",
    "initialize_learning_module"
]
