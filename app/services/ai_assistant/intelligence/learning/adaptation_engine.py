"""
适应性引擎

根据用户学习结果动态调整系统行为和响应策略。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from .learning_models import (
    UserPreferences, LearningPattern, AdaptationRule,
    InteractionType, DifficultyLevel, GoalType
)
from .user_behavior_learner import UserBehaviorLearner

logger = logging.getLogger(__name__)

@dataclass
class AdaptationContext:
    """适应性上下文"""
    user_id: str
    current_message: str
    intent: str
    confidence: float
    preferences: Optional[UserPreferences]
    patterns: List[LearningPattern]
    session_context: Dict[str, Any]
    timestamp: datetime

class AdaptationEngine:
    """适应性引擎"""
    
    def __init__(self, learner: UserBehaviorLearner, config: Optional[Dict[str, Any]] = None):
        """
        初始化适应性引擎
        
        Args:
            learner: 用户行为学习器
            config: 配置参数
        """
        self.learner = learner
        self.config = config or {}
        self.adaptation_threshold = self.config.get("adaptation_threshold", 0.7)
        self.max_adaptations_per_session = self.config.get("max_adaptations_per_session", 5)
        
        # 适应性规则存储
        self.adaptation_rules: Dict[str, List[AdaptationRule]] = {}
        self.session_adaptations: Dict[str, int] = {}
        
        # 注册默认适应性规则
        self._register_default_rules()
        
        logger.info("适应性引擎初始化完成")
    
    def _register_default_rules(self):
        """注册默认适应性规则"""
        try:
            # 难度级别适应规则
            self._add_global_rule(
                rule_id="difficulty_adaptation",
                condition={"type": "difficulty_mismatch"},
                action={"type": "adjust_difficulty", "factor": 0.8},
                priority=1
            )
            
            # 时间偏好适应规则
            self._add_global_rule(
                rule_id="time_preference_adaptation",
                condition={"type": "time_pattern_detected"},
                action={"type": "suggest_optimal_time", "confidence_threshold": 0.7},
                priority=2
            )
            
            # 内容偏好适应规则
            self._add_global_rule(
                rule_id="content_preference_adaptation",
                condition={"type": "content_pattern_detected"},
                action={"type": "personalize_content", "adaptation_strength": 0.8},
                priority=3
            )
            
            # 交互频率适应规则
            self._add_global_rule(
                rule_id="frequency_adaptation",
                condition={"type": "frequency_pattern_detected"},
                action={"type": "adjust_interaction_style", "factor": 1.2},
                priority=4
            )
            
            logger.info("默认适应性规则注册完成")
            
        except Exception as e:
            logger.error(f"默认规则注册失败: {str(e)}")
    
    def _add_global_rule(self, rule_id: str, condition: Dict[str, Any], 
                        action: Dict[str, Any], priority: int = 1):
        """添加全局适应性规则"""
        rule = AdaptationRule(
            rule_id=rule_id,
            user_id="global",
            condition=condition,
            action=action,
            priority=priority
        )
        
        if "global" not in self.adaptation_rules:
            self.adaptation_rules["global"] = []
        
        self.adaptation_rules["global"].append(rule)
    
    async def adapt_response(
        self, 
        user_id: str, 
        message: str, 
        intent: str, 
        confidence: float,
        base_response: str,
        session_context: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        根据用户特征适应响应
        
        Args:
            user_id: 用户ID
            message: 用户消息
            intent: 识别的意图
            confidence: 意图置信度
            base_response: 基础响应
            session_context: 会话上下文
            
        Returns:
            (适应后的响应, 适应元数据)
        """
        try:
            # 检查会话适应次数限制
            session_key = f"{user_id}_{datetime.now().strftime('%Y%m%d_%H')}"
            if self.session_adaptations.get(session_key, 0) >= self.max_adaptations_per_session:
                logger.info(f"用户 {user_id} 本会话适应次数已达上限")
                return base_response, {"adapted": False, "reason": "session_limit_reached"}
            
            # 获取用户学习数据
            preferences = self.learner.get_user_preferences(user_id)
            patterns = self.learner.get_user_patterns(user_id)
            
            # 创建适应性上下文
            context = AdaptationContext(
                user_id=user_id,
                current_message=message,
                intent=intent,
                confidence=confidence,
                preferences=preferences,
                patterns=patterns,
                session_context=session_context or {},
                timestamp=datetime.now()
            )
            
            # 执行适应性调整
            adapted_response, adaptation_metadata = await self._execute_adaptations(
                context, base_response
            )
            
            # 更新会话计数
            if adaptation_metadata.get("adapted", False):
                self.session_adaptations[session_key] = \
                    self.session_adaptations.get(session_key, 0) + 1
            
            return adapted_response, adaptation_metadata
            
        except Exception as e:
            logger.error(f"响应适应失败: {str(e)}")
            return base_response, {"adapted": False, "error": str(e)}
    
    async def _execute_adaptations(
        self, 
        context: AdaptationContext, 
        base_response: str
    ) -> Tuple[str, Dict[str, Any]]:
        """执行适应性调整"""
        try:
            adapted_response = base_response
            applied_adaptations = []
            
            # 获取适用的规则
            applicable_rules = await self._get_applicable_rules(context)
            
            # 按优先级排序
            applicable_rules.sort(key=lambda rule: rule.priority)
            
            # 执行适应性规则
            for rule in applicable_rules:
                try:
                    adaptation_result = await self._apply_adaptation_rule(
                        rule, context, adapted_response
                    )
                    
                    if adaptation_result["success"]:
                        adapted_response = adaptation_result["response"]
                        applied_adaptations.append({
                            "rule_id": rule.rule_id,
                            "action_type": rule.action.get("type"),
                            "effect": adaptation_result.get("effect", "unknown")
                        })
                        
                        # 更新规则统计
                        rule.application_count += 1
                        rule.last_applied = datetime.now()
                        
                except Exception as e:
                    logger.error(f"适应性规则 {rule.rule_id} 执行失败: {str(e)}")
                    continue
            
            # 生成适应性元数据
            adaptation_metadata = {
                "adapted": len(applied_adaptations) > 0,
                "adaptations_applied": applied_adaptations,
                "adaptation_count": len(applied_adaptations),
                "user_preferences_used": context.preferences is not None,
                "patterns_considered": len(context.patterns),
                "adaptation_timestamp": context.timestamp.isoformat()
            }
            
            if len(applied_adaptations) > 0:
                logger.info(f"用户 {context.user_id} 应用了 {len(applied_adaptations)} 个适应性调整")
            
            return adapted_response, adaptation_metadata
            
        except Exception as e:
            logger.error(f"适应性执行失败: {str(e)}")
            return base_response, {"adapted": False, "error": str(e)}
    
    async def _get_applicable_rules(self, context: AdaptationContext) -> List[AdaptationRule]:
        """获取适用的适应性规则"""
        applicable_rules = []
        
        try:
            # 检查全局规则
            global_rules = self.adaptation_rules.get("global", [])
            for rule in global_rules:
                if await self._evaluate_rule_condition(rule, context):
                    applicable_rules.append(rule)
            
            # 检查用户特定规则
            user_rules = self.adaptation_rules.get(context.user_id, [])
            for rule in user_rules:
                if await self._evaluate_rule_condition(rule, context):
                    applicable_rules.append(rule)
            
            return applicable_rules
            
        except Exception as e:
            logger.error(f"规则评估失败: {str(e)}")
            return []
    
    async def _evaluate_rule_condition(
        self, 
        rule: AdaptationRule, 
        context: AdaptationContext
    ) -> bool:
        """评估规则条件"""
        try:
            condition = rule.condition
            condition_type = condition.get("type")
            
            if condition_type == "difficulty_mismatch":
                return await self._check_difficulty_mismatch(context)
            
            elif condition_type == "time_pattern_detected":
                return await self._check_time_pattern(context)
            
            elif condition_type == "content_pattern_detected":
                return await self._check_content_pattern(context)
            
            elif condition_type == "frequency_pattern_detected":
                return await self._check_frequency_pattern(context)
            
            else:
                logger.warning(f"未知的条件类型: {condition_type}")
                return False
                
        except Exception as e:
            logger.error(f"条件评估失败: {str(e)}")
            return False
    
    async def _check_difficulty_mismatch(self, context: AdaptationContext) -> bool:
        """检查难度不匹配"""
        if not context.preferences:
            return False
        
        # 简化的难度检查逻辑
        user_level = context.preferences.difficulty_level
        message_lower = context.current_message.lower()
        
        # 检查是否有难度相关的关键词
        if user_level == DifficultyLevel.BEGINNER:
            return any(word in message_lower for word in ["高级", "专业", "困难"])
        elif user_level == DifficultyLevel.ADVANCED:
            return any(word in message_lower for word in ["简单", "基础", "入门"])
        
        return False
    
    async def _check_time_pattern(self, context: AdaptationContext) -> bool:
        """检查时间模式"""
        time_patterns = [p for p in context.patterns if p.pattern_type == "time_preference"]
        return len(time_patterns) > 0 and any(p.confidence >= 0.7 for p in time_patterns)
    
    async def _check_content_pattern(self, context: AdaptationContext) -> bool:
        """检查内容模式"""
        content_patterns = [p for p in context.patterns if p.pattern_type == "content_preference"]
        return len(content_patterns) > 0 and any(p.confidence >= 0.6 for p in content_patterns)
    
    async def _check_frequency_pattern(self, context: AdaptationContext) -> bool:
        """检查频率模式"""
        frequency_patterns = [p for p in context.patterns if p.pattern_type == "interaction_frequency"]
        return len(frequency_patterns) > 0 and any(p.confidence >= 0.6 for p in frequency_patterns)
    
    async def _apply_adaptation_rule(
        self, 
        rule: AdaptationRule, 
        context: AdaptationContext, 
        current_response: str
    ) -> Dict[str, Any]:
        """应用适应性规则"""
        try:
            action = rule.action
            action_type = action.get("type")
            
            if action_type == "adjust_difficulty":
                return await self._adjust_difficulty(action, context, current_response)
            
            elif action_type == "suggest_optimal_time":
                return await self._suggest_optimal_time(action, context, current_response)
            
            elif action_type == "personalize_content":
                return await self._personalize_content(action, context, current_response)
            
            elif action_type == "adjust_interaction_style":
                return await self._adjust_interaction_style(action, context, current_response)
            
            else:
                logger.warning(f"未知的动作类型: {action_type}")
                return {"success": False, "response": current_response}
                
        except Exception as e:
            logger.error(f"规则应用失败: {str(e)}")
            return {"success": False, "response": current_response, "error": str(e)}
    
    async def _adjust_difficulty(
        self, 
        action: Dict[str, Any], 
        context: AdaptationContext, 
        response: str
    ) -> Dict[str, Any]:
        """调整难度级别"""
        try:
            if not context.preferences:
                return {"success": False, "response": response}
            
            user_level = context.preferences.difficulty_level
            
            # 根据用户级别调整响应
            if user_level == DifficultyLevel.BEGINNER:
                adapted_response = response + "\n\n💡 作为初学者，建议您从基础动作开始，循序渐进地提高强度。"
            elif user_level == DifficultyLevel.ADVANCED:
                adapted_response = response + "\n\n🔥 以您的水平，可以尝试更具挑战性的变式动作来提升效果。"
            else:
                adapted_response = response + "\n\n⚡ 根据您的经验，可以适当调整动作强度以匹配您的能力水平。"
            
            return {
                "success": True,
                "response": adapted_response,
                "effect": f"difficulty_adjusted_for_{user_level.value}"
            }
            
        except Exception as e:
            logger.error(f"难度调整失败: {str(e)}")
            return {"success": False, "response": response}
    
    async def _suggest_optimal_time(
        self, 
        action: Dict[str, Any], 
        context: AdaptationContext, 
        response: str
    ) -> Dict[str, Any]:
        """建议最佳时间"""
        try:
            time_patterns = [p for p in context.patterns if p.pattern_type == "time_preference"]
            
            if not time_patterns:
                return {"success": False, "response": response}
            
            best_pattern = max(time_patterns, key=lambda p: p.confidence)
            peak_hour = best_pattern.pattern_data.get("peak_hour")
            
            if peak_hour is not None:
                time_suggestion = self._format_time_suggestion(peak_hour)
                adapted_response = response + f"\n\n⏰ 根据您的使用习惯，{time_suggestion}可能是您的最佳锻炼时间。"
                
                return {
                    "success": True,
                    "response": adapted_response,
                    "effect": f"time_suggestion_added_{peak_hour}h"
                }
            
            return {"success": False, "response": response}
            
        except Exception as e:
            logger.error(f"时间建议失败: {str(e)}")
            return {"success": False, "response": response}
    
    async def _personalize_content(
        self, 
        action: Dict[str, Any], 
        context: AdaptationContext, 
        response: str
    ) -> Dict[str, Any]:
        """个性化内容"""
        try:
            if not context.preferences:
                return {"success": False, "response": response}
            
            # 根据用户偏好添加个性化内容
            personalization = []
            
            if context.preferences.goals:
                goal_names = [goal.value for goal in context.preferences.goals]
                personalization.append(f"针对您的{'/'.join(goal_names)}目标")
            
            if context.preferences.exercise_types:
                exercise_types = context.preferences.exercise_types
                personalization.append(f"结合您偏爱的{'/'.join(exercise_types)}训练")
            
            if personalization:
                adapted_response = response + f"\n\n🎯 {', '.join(personalization)}，这个建议会更适合您。"
                
                return {
                    "success": True,
                    "response": adapted_response,
                    "effect": "content_personalized"
                }
            
            return {"success": False, "response": response}
            
        except Exception as e:
            logger.error(f"内容个性化失败: {str(e)}")
            return {"success": False, "response": response}
    
    async def _adjust_interaction_style(
        self, 
        action: Dict[str, Any], 
        context: AdaptationContext, 
        response: str
    ) -> Dict[str, Any]:
        """调整交互风格"""
        try:
            if not context.preferences:
                return {"success": False, "response": response}
            
            style = context.preferences.communication_style
            
            if style == "friendly":
                adapted_response = response + "\n\n😊 希望这个建议对您有帮助！有任何问题随时告诉我。"
            elif style == "professional":
                adapted_response = response + "\n\n如需更详细的指导或有其他问题，请随时咨询。"
            else:
                adapted_response = response + "\n\n如有疑问，欢迎继续交流。"
            
            return {
                "success": True,
                "response": adapted_response,
                "effect": f"style_adjusted_{style}"
            }
            
        except Exception as e:
            logger.error(f"交互风格调整失败: {str(e)}")
            return {"success": False, "response": response}
    
    def _format_time_suggestion(self, hour: int) -> str:
        """格式化时间建议"""
        if 6 <= hour < 10:
            return "早上6-10点"
        elif 10 <= hour < 14:
            return "上午10点-下午2点"
        elif 14 <= hour < 18:
            return "下午2-6点"
        elif 18 <= hour < 22:
            return "晚上6-10点"
        else:
            return f"{hour}点左右"
    
    def get_adaptation_stats(self, user_id: str) -> Dict[str, Any]:
        """获取适应性统计"""
        user_rules = self.adaptation_rules.get(user_id, [])
        global_rules = self.adaptation_rules.get("global", [])
        
        return {
            "user_specific_rules": len(user_rules),
            "global_rules": len(global_rules),
            "total_rules": len(user_rules) + len(global_rules),
            "session_adaptations": self.session_adaptations.get(
                f"{user_id}_{datetime.now().strftime('%Y%m%d_%H')}", 0
            )
        }
