"""
学习模型定义

定义用户行为学习的数据模型和结构。
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import logging

logger = logging.getLogger(__name__)

class InteractionType(Enum):
    """交互类型枚举"""
    CHAT = "chat"
    EXERCISE_REQUEST = "exercise_request"
    NUTRITION_QUERY = "nutrition_query"
    GOAL_SETTING = "goal_setting"
    FEEDBACK = "feedback"
    PREFERENCE_UPDATE = "preference_update"

class DifficultyLevel(Enum):
    """难度级别枚举"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

class GoalType(Enum):
    """目标类型枚举"""
    WEIGHT_LOSS = "weight_loss"
    MUSCLE_GAIN = "muscle_gain"
    ENDURANCE = "endurance"
    STRENGTH = "strength"
    FLEXIBILITY = "flexibility"
    GENERAL_FITNESS = "general_fitness"

@dataclass
class UserInteraction:
    """用户交互记录"""
    user_id: str
    interaction_id: str
    interaction_type: InteractionType
    timestamp: datetime
    content: str
    intent: Optional[str] = None
    confidence: float = 0.0
    response_time_ms: float = 0.0
    satisfaction_score: Optional[float] = None
    context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "user_id": self.user_id,
            "interaction_id": self.interaction_id,
            "interaction_type": self.interaction_type.value,
            "timestamp": self.timestamp.isoformat(),
            "content": self.content,
            "intent": self.intent,
            "confidence": self.confidence,
            "response_time_ms": self.response_time_ms,
            "satisfaction_score": self.satisfaction_score,
            "context": self.context,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "UserInteraction":
        """从字典创建实例"""
        return cls(
            user_id=data["user_id"],
            interaction_id=data["interaction_id"],
            interaction_type=InteractionType(data["interaction_type"]),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            content=data["content"],
            intent=data.get("intent"),
            confidence=data.get("confidence", 0.0),
            response_time_ms=data.get("response_time_ms", 0.0),
            satisfaction_score=data.get("satisfaction_score"),
            context=data.get("context", {}),
            metadata=data.get("metadata", {})
        )

@dataclass
class UserPreferences:
    """用户偏好模型"""
    user_id: str
    exercise_types: List[str] = field(default_factory=list)
    difficulty_level: DifficultyLevel = DifficultyLevel.BEGINNER
    preferred_workout_duration: int = 30  # 分钟
    preferred_time_slots: List[str] = field(default_factory=list)
    goals: List[GoalType] = field(default_factory=list)
    equipment_available: List[str] = field(default_factory=list)
    physical_limitations: List[str] = field(default_factory=list)
    motivation_factors: List[str] = field(default_factory=list)
    communication_style: str = "friendly"
    language_preference: str = "zh"
    updated_at: datetime = field(default_factory=datetime.now)
    confidence_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "user_id": self.user_id,
            "exercise_types": self.exercise_types,
            "difficulty_level": self.difficulty_level.value,
            "preferred_workout_duration": self.preferred_workout_duration,
            "preferred_time_slots": self.preferred_time_slots,
            "goals": [goal.value for goal in self.goals],
            "equipment_available": self.equipment_available,
            "physical_limitations": self.physical_limitations,
            "motivation_factors": self.motivation_factors,
            "communication_style": self.communication_style,
            "language_preference": self.language_preference,
            "updated_at": self.updated_at.isoformat(),
            "confidence_score": self.confidence_score
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "UserPreferences":
        """从字典创建实例"""
        return cls(
            user_id=data["user_id"],
            exercise_types=data.get("exercise_types", []),
            difficulty_level=DifficultyLevel(data.get("difficulty_level", "beginner")),
            preferred_workout_duration=data.get("preferred_workout_duration", 30),
            preferred_time_slots=data.get("preferred_time_slots", []),
            goals=[GoalType(goal) for goal in data.get("goals", [])],
            equipment_available=data.get("equipment_available", []),
            physical_limitations=data.get("physical_limitations", []),
            motivation_factors=data.get("motivation_factors", []),
            communication_style=data.get("communication_style", "friendly"),
            language_preference=data.get("language_preference", "zh"),
            updated_at=datetime.fromisoformat(data.get("updated_at", datetime.now().isoformat())),
            confidence_score=data.get("confidence_score", 0.0)
        )

@dataclass
class LearningPattern:
    """学习模式"""
    pattern_id: str
    user_id: str
    pattern_type: str
    pattern_data: Dict[str, Any]
    frequency: int = 1
    confidence: float = 0.0
    first_observed: datetime = field(default_factory=datetime.now)
    last_observed: datetime = field(default_factory=datetime.now)
    is_active: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "pattern_id": self.pattern_id,
            "user_id": self.user_id,
            "pattern_type": self.pattern_type,
            "pattern_data": self.pattern_data,
            "frequency": self.frequency,
            "confidence": self.confidence,
            "first_observed": self.first_observed.isoformat(),
            "last_observed": self.last_observed.isoformat(),
            "is_active": self.is_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "LearningPattern":
        """从字典创建实例"""
        return cls(
            pattern_id=data["pattern_id"],
            user_id=data["user_id"],
            pattern_type=data["pattern_type"],
            pattern_data=data["pattern_data"],
            frequency=data.get("frequency", 1),
            confidence=data.get("confidence", 0.0),
            first_observed=datetime.fromisoformat(data["first_observed"]),
            last_observed=datetime.fromisoformat(data["last_observed"]),
            is_active=data.get("is_active", True)
        )

@dataclass
class AdaptationRule:
    """适应性规则"""
    rule_id: str
    user_id: str
    condition: Dict[str, Any]
    action: Dict[str, Any]
    priority: int = 1
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    last_applied: Optional[datetime] = None
    application_count: int = 0
    success_rate: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "rule_id": self.rule_id,
            "user_id": self.user_id,
            "condition": self.condition,
            "action": self.action,
            "priority": self.priority,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat(),
            "last_applied": self.last_applied.isoformat() if self.last_applied else None,
            "application_count": self.application_count,
            "success_rate": self.success_rate
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AdaptationRule":
        """从字典创建实例"""
        return cls(
            rule_id=data["rule_id"],
            user_id=data["user_id"],
            condition=data["condition"],
            action=data["action"],
            priority=data.get("priority", 1),
            is_active=data.get("is_active", True),
            created_at=datetime.fromisoformat(data["created_at"]),
            last_applied=datetime.fromisoformat(data["last_applied"]) if data.get("last_applied") else None,
            application_count=data.get("application_count", 0),
            success_rate=data.get("success_rate", 0.0)
        )

class LearningDataValidator:
    """学习数据验证器"""
    
    @staticmethod
    def validate_interaction(interaction: UserInteraction) -> bool:
        """验证用户交互数据"""
        try:
            # 检查必需字段
            if not interaction.user_id or not interaction.interaction_id:
                return False
            
            # 检查时间戳
            if interaction.timestamp > datetime.now():
                return False
            
            # 检查置信度范围
            if not (0.0 <= interaction.confidence <= 1.0):
                return False
            
            # 检查响应时间
            if interaction.response_time_ms < 0:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"交互数据验证失败: {str(e)}")
            return False
    
    @staticmethod
    def validate_preferences(preferences: UserPreferences) -> bool:
        """验证用户偏好数据"""
        try:
            # 检查用户ID
            if not preferences.user_id:
                return False
            
            # 检查时长范围
            if not (5 <= preferences.preferred_workout_duration <= 180):
                return False
            
            # 检查置信度
            if not (0.0 <= preferences.confidence_score <= 1.0):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"偏好数据验证失败: {str(e)}")
            return False

# 工具函数
def create_interaction_from_message(
    user_id: str,
    message: str,
    intent: str,
    confidence: float,
    response_time_ms: float
) -> UserInteraction:
    """从消息创建交互记录"""
    import uuid
    
    return UserInteraction(
        user_id=user_id,
        interaction_id=str(uuid.uuid4()),
        interaction_type=InteractionType.CHAT,
        timestamp=datetime.now(),
        content=message,
        intent=intent,
        confidence=confidence,
        response_time_ms=response_time_ms
    )

def create_default_preferences(user_id: str) -> UserPreferences:
    """创建默认用户偏好"""
    return UserPreferences(
        user_id=user_id,
        goals=[GoalType.GENERAL_FITNESS],
        communication_style="friendly",
        language_preference="zh"
    )
