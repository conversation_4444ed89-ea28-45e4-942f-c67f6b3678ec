"""
个性化服务

提供基于用户学习结果的个性化健身建议和交互体验。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from .learning_models import (
    UserPreferences, LearningPattern, InteractionType,
    DifficultyLevel, GoalType, create_interaction_from_message
)
from .user_behavior_learner import UserBehaviorLearner
from .adaptation_engine import AdaptationEngine

logger = logging.getLogger(__name__)

@dataclass
class PersonalizationRequest:
    """个性化请求"""
    user_id: str
    request_type: str
    content: str
    context: Dict[str, Any]
    timestamp: datetime

@dataclass
class PersonalizationResponse:
    """个性化响应"""
    content: str
    confidence: float
    personalization_factors: List[str]
    recommendations: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class PersonalizationService:
    """个性化服务"""

    def __init__(
        self,
        learner: UserBehaviorLearner,
        adaptation_engine: AdaptationEngine,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化个性化服务

        Args:
            learner: 用户行为学习器
            adaptation_engine: 适应性引擎
            config: 配置参数
        """
        self.learner = learner
        self.adaptation_engine = adaptation_engine
        self.config = config or {}

        # 个性化配置
        self.min_confidence_threshold = self.config.get("min_confidence_threshold", 0.6)
        self.max_recommendations = self.config.get("max_recommendations", 5)
        self.personalization_strength = self.config.get("personalization_strength", 0.8)

        logger.info("个性化服务初始化完成")

    async def personalize_response(
        self,
        user_id: str,
        message: str,
        intent: str,
        base_response: str,
        context: Optional[Dict[str, Any]] = None
    ) -> PersonalizationResponse:
        """
        个性化响应

        Args:
            user_id: 用户ID
            message: 用户消息
            intent: 识别的意图
            base_response: 基础响应
            context: 上下文信息

        Returns:
            个性化响应
        """
        try:
            # 记录用户交互
            interaction = create_interaction_from_message(
                user_id=user_id,
                message=message,
                intent=intent,
                confidence=context.get("confidence", 0.8) if context else 0.8,
                response_time_ms=context.get("response_time_ms", 0.0) if context else 0.0
            )
            await self.learner.record_interaction(interaction)

            # 获取用户学习数据
            preferences = self.learner.get_user_preferences(user_id)
            patterns = self.learner.get_user_patterns(user_id)

            # 应用适应性调整
            adapted_response, adaptation_metadata = await self.adaptation_engine.adapt_response(
                user_id=user_id,
                message=message,
                intent=intent,
                confidence=context.get("confidence", 0.8) if context else 0.8,
                base_response=base_response,
                session_context=context
            )

            # 生成个性化建议
            recommendations = await self._generate_personalized_recommendations(
                user_id, intent, preferences, patterns
            )

            # 计算个性化置信度
            personalization_confidence = self._calculate_personalization_confidence(
                preferences, patterns, adaptation_metadata
            )

            # 识别个性化因子
            personalization_factors = self._identify_personalization_factors(
                preferences, patterns, adaptation_metadata
            )

            # 构建个性化响应
            personalized_response = PersonalizationResponse(
                content=adapted_response,
                confidence=personalization_confidence,
                personalization_factors=personalization_factors,
                recommendations=recommendations,
                metadata={
                    "adaptation_metadata": adaptation_metadata,
                    "user_preferences_available": preferences is not None,
                    "patterns_count": len(patterns),
                    "personalization_timestamp": datetime.now().isoformat()
                }
            )

            logger.info(f"个性化响应生成完成: {user_id}, 置信度: {personalization_confidence:.2f}")
            return personalized_response

        except Exception as e:
            logger.error(f"个性化响应失败: {str(e)}")
            # 返回基础响应作为回退
            return PersonalizationResponse(
                content=base_response,
                confidence=0.5,
                personalization_factors=[],
                recommendations=[],
                metadata={"error": str(e)}
            )

    async def _generate_personalized_recommendations(
        self,
        user_id: str,
        intent: str,
        preferences: Optional[UserPreferences],
        patterns: List[LearningPattern]
    ) -> List[Dict[str, Any]]:
        """生成个性化建议"""
        try:
            recommendations = []

            if not preferences:
                return recommendations

            # 基于意图生成建议
            if intent == "exercise_action":
                recommendations.extend(
                    await self._generate_exercise_recommendations(preferences, patterns)
                )
            elif intent == "nutrition_advice":
                recommendations.extend(
                    await self._generate_nutrition_recommendations(preferences, patterns)
                )
            elif intent == "goal_setting":
                recommendations.extend(
                    await self._generate_goal_recommendations(preferences, patterns)
                )

            # 基于时间模式生成建议
            time_recommendations = await self._generate_time_based_recommendations(patterns)
            recommendations.extend(time_recommendations)

            # 限制建议数量
            return recommendations[:self.max_recommendations]

        except Exception as e:
            logger.error(f"个性化建议生成失败: {str(e)}")
            return []

    async def _generate_exercise_recommendations(
        self,
        preferences: UserPreferences,
        patterns: List[LearningPattern]
    ) -> List[Dict[str, Any]]:
        """生成运动建议"""
        recommendations = []

        try:
            # 基于用户目标的建议
            for goal in preferences.goals:
                if goal == GoalType.WEIGHT_LOSS:
                    recommendations.append({
                        "type": "exercise",
                        "title": "有氧运动建议",
                        "content": "基于您的减重目标，建议增加有氧运动，如快走、慢跑或游泳。",
                        "priority": 1,
                        "source": "goal_based"
                    })
                elif goal == GoalType.MUSCLE_GAIN:
                    recommendations.append({
                        "type": "exercise",
                        "title": "力量训练建议",
                        "content": "为了增肌目标，建议进行渐进式力量训练，重点关注复合动作。",
                        "priority": 1,
                        "source": "goal_based"
                    })

            # 基于难度级别的建议
            if preferences.difficulty_level == DifficultyLevel.BEGINNER:
                recommendations.append({
                    "type": "exercise",
                    "title": "初学者友好训练",
                    "content": "建议从基础动作开始，每周3-4次训练，每次20-30分钟。",
                    "priority": 2,
                    "source": "difficulty_based"
                })
            elif preferences.difficulty_level == DifficultyLevel.ADVANCED:
                recommendations.append({
                    "type": "exercise",
                    "title": "高级训练变式",
                    "content": "可以尝试更具挑战性的动作变式和高强度间歇训练。",
                    "priority": 2,
                    "source": "difficulty_based"
                })

            # 基于设备可用性的建议
            if "resistance_band" in preferences.equipment_available:
                recommendations.append({
                    "type": "exercise",
                    "title": "弹力带训练",
                    "content": "利用您的弹力带进行全身力量训练，方便且有效。",
                    "priority": 3,
                    "source": "equipment_based"
                })

            return recommendations

        except Exception as e:
            logger.error(f"运动建议生成失败: {str(e)}")
            return []

    async def _generate_nutrition_recommendations(
        self,
        preferences: UserPreferences,
        patterns: List[LearningPattern]
    ) -> List[Dict[str, Any]]:
        """生成营养建议"""
        recommendations = []

        try:
            # 基于目标的营养建议
            for goal in preferences.goals:
                if goal == GoalType.WEIGHT_LOSS:
                    recommendations.append({
                        "type": "nutrition",
                        "title": "减重饮食建议",
                        "content": "建议控制热量摄入，增加蛋白质比例，多吃蔬菜和水果。",
                        "priority": 1,
                        "source": "goal_based"
                    })
                elif goal == GoalType.MUSCLE_GAIN:
                    recommendations.append({
                        "type": "nutrition",
                        "title": "增肌营养建议",
                        "content": "确保充足的蛋白质摄入（每公斤体重1.6-2.2g），配合适量碳水化合物。",
                        "priority": 1,
                        "source": "goal_based"
                    })

            return recommendations

        except Exception as e:
            logger.error(f"营养建议生成失败: {str(e)}")
            return []

    async def _generate_goal_recommendations(
        self,
        preferences: UserPreferences,
        patterns: List[LearningPattern]
    ) -> List[Dict[str, Any]]:
        """生成目标建议"""
        recommendations = []

        try:
            # 基于当前目标的进阶建议
            if GoalType.GENERAL_FITNESS in preferences.goals:
                recommendations.append({
                    "type": "goal",
                    "title": "目标细化建议",
                    "content": "考虑将一般健身目标细化为具体目标，如力量提升或耐力改善。",
                    "priority": 1,
                    "source": "goal_refinement"
                })

            # 基于训练时长的目标建议
            if preferences.preferred_workout_duration < 20:
                recommendations.append({
                    "type": "goal",
                    "title": "训练时长建议",
                    "content": "建议逐步增加训练时长至30-45分钟，以获得更好的训练效果。",
                    "priority": 2,
                    "source": "duration_based"
                })

            return recommendations

        except Exception as e:
            logger.error(f"目标建议生成失败: {str(e)}")
            return []

    async def _generate_time_based_recommendations(
        self,
        patterns: List[LearningPattern]
    ) -> List[Dict[str, Any]]:
        """生成基于时间模式的建议"""
        recommendations = []

        try:
            time_patterns = [p for p in patterns if p.pattern_type == "time_preference"]

            for pattern in time_patterns:
                if pattern.confidence >= 0.7:
                    peak_hour = pattern.pattern_data.get("peak_hour")
                    if peak_hour is not None:
                        time_desc = self._format_time_description(peak_hour)
                        recommendations.append({
                            "type": "timing",
                            "title": "最佳训练时间",
                            "content": f"根据您的使用习惯，{time_desc}是您的活跃时段，建议安排训练。",
                            "priority": 3,
                            "source": "time_pattern"
                        })

            return recommendations

        except Exception as e:
            logger.error(f"时间建议生成失败: {str(e)}")
            return []

    def _format_time_description(self, hour: int) -> str:
        """格式化时间描述"""
        if 6 <= hour < 10:
            return "早晨时光"
        elif 10 <= hour < 14:
            return "上午时段"
        elif 14 <= hour < 18:
            return "下午时光"
        elif 18 <= hour < 22:
            return "傍晚时段"
        else:
            return "深夜时分"

    def _calculate_personalization_confidence(
        self,
        preferences: Optional[UserPreferences],
        patterns: List[LearningPattern],
        adaptation_metadata: Dict[str, Any]
    ) -> float:
        """计算个性化置信度"""
        try:
            confidence_factors = []

            # 用户偏好置信度
            if preferences:
                # 确保至少有基础置信度
                pref_confidence = max(preferences.confidence_score, 0.4)
                confidence_factors.append(pref_confidence)

            # 模式置信度
            if patterns:
                pattern_confidences = [p.confidence for p in patterns]
                avg_pattern_confidence = sum(pattern_confidences) / len(pattern_confidences)
                confidence_factors.append(avg_pattern_confidence)

            # 适应性应用置信度
            if adaptation_metadata.get("adapted", False):
                confidence_factors.append(0.8)

            # 数据量因子
            if preferences and len(patterns) >= 2:
                confidence_factors.append(0.9)
            elif preferences:
                # 即使没有模式，有偏好也给一定置信度
                confidence_factors.append(0.6)

            # 基础置信度保证
            if not confidence_factors:
                return 0.5

            final_confidence = sum(confidence_factors) / len(confidence_factors)
            return max(final_confidence, 0.3)  # 最低置信度保证

        except Exception as e:
            logger.error(f"置信度计算失败: {str(e)}")
            return 0.5

    def _identify_personalization_factors(
        self,
        preferences: Optional[UserPreferences],
        patterns: List[LearningPattern],
        adaptation_metadata: Dict[str, Any]
    ) -> List[str]:
        """识别个性化因子"""
        factors = []

        try:
            if preferences:
                factors.append("user_preferences")

                if preferences.goals:
                    factors.append("fitness_goals")

                if preferences.exercise_types:
                    factors.append("exercise_preferences")

                if preferences.difficulty_level != DifficultyLevel.BEGINNER:
                    factors.append("experience_level")

            if patterns:
                pattern_types = [p.pattern_type for p in patterns]
                if "time_preference" in pattern_types:
                    factors.append("time_patterns")
                if "content_preference" in pattern_types:
                    factors.append("content_patterns")
                if "interaction_frequency" in pattern_types:
                    factors.append("usage_patterns")

            if adaptation_metadata.get("adapted", False):
                factors.append("adaptive_response")

            return factors

        except Exception as e:
            logger.error(f"个性化因子识别失败: {str(e)}")
            return []

    async def get_user_personalization_summary(self, user_id: str) -> Dict[str, Any]:
        """获取用户个性化摘要"""
        try:
            preferences = self.learner.get_user_preferences(user_id)
            patterns = self.learner.get_user_patterns(user_id)
            learning_summary = self.learner.get_learning_summary(user_id)
            adaptation_stats = self.adaptation_engine.get_adaptation_stats(user_id)

            return {
                "user_id": user_id,
                "personalization_available": preferences is not None,
                "confidence_score": preferences.confidence_score if preferences else 0.0,
                "learning_summary": learning_summary,
                "adaptation_stats": adaptation_stats,
                "patterns_detected": len(patterns),
                "last_update": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"个性化摘要获取失败: {str(e)}")
            return {"user_id": user_id, "error": str(e)}

    async def reset_user_personalization(self, user_id: str) -> bool:
        """重置用户个性化数据"""
        try:
            # 清除学习数据
            if user_id in self.learner.interactions:
                del self.learner.interactions[user_id]

            if user_id in self.learner.preferences:
                del self.learner.preferences[user_id]

            if user_id in self.learner.patterns:
                del self.learner.patterns[user_id]

            # 清除适应性规则
            if user_id in self.adaptation_engine.adaptation_rules:
                del self.adaptation_engine.adaptation_rules[user_id]

            logger.info(f"用户 {user_id} 个性化数据已重置")
            return True

        except Exception as e:
            logger.error(f"个性化数据重置失败: {str(e)}")
            return False
