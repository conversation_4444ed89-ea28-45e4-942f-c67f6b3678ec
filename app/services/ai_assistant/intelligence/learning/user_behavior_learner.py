"""
用户行为学习器

实现用户行为模式的学习和分析功能。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import json
import numpy as np
from dataclasses import asdict

from .learning_models import (
    UserInteraction, UserPreferences, LearningPattern, 
    InteractionType, DifficultyLevel, GoalType,
    LearningDataValidator, create_default_preferences
)

logger = logging.getLogger(__name__)

class UserBehaviorLearner:
    """用户行为学习器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化用户行为学习器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.min_interactions = self.config.get("min_interactions", 5)
        self.learning_window_days = self.config.get("learning_window_days", 30)
        self.pattern_confidence_threshold = self.config.get("pattern_confidence_threshold", 0.7)
        
        # 内存存储（生产环境应使用数据库）
        self.interactions: Dict[str, List[UserInteraction]] = defaultdict(list)
        self.preferences: Dict[str, UserPreferences] = {}
        self.patterns: Dict[str, List[LearningPattern]] = defaultdict(list)
        
        logger.info("用户行为学习器初始化完成")
    
    async def record_interaction(self, interaction: UserInteraction) -> bool:
        """
        记录用户交互
        
        Args:
            interaction: 用户交互记录
            
        Returns:
            记录是否成功
        """
        try:
            # 验证交互数据
            if not LearningDataValidator.validate_interaction(interaction):
                logger.warning(f"无效的交互数据: {interaction.interaction_id}")
                return False
            
            # 存储交互记录
            self.interactions[interaction.user_id].append(interaction)
            
            # 限制存储数量（保留最近的交互）
            max_interactions = self.config.get("max_interactions_per_user", 1000)
            if len(self.interactions[interaction.user_id]) > max_interactions:
                self.interactions[interaction.user_id] = \
                    self.interactions[interaction.user_id][-max_interactions:]
            
            # 触发学习更新
            await self._update_learning(interaction.user_id)
            
            logger.debug(f"记录用户交互: {interaction.user_id} - {interaction.interaction_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"记录交互失败: {str(e)}")
            return False
    
    async def learn_user_preferences(self, user_id: str) -> Optional[UserPreferences]:
        """
        学习用户偏好
        
        Args:
            user_id: 用户ID
            
        Returns:
            学习到的用户偏好
        """
        try:
            user_interactions = self.interactions.get(user_id, [])
            
            if len(user_interactions) < self.min_interactions:
                logger.info(f"用户 {user_id} 交互数据不足，使用默认偏好")
                return create_default_preferences(user_id)
            
            # 获取时间窗口内的交互
            cutoff_date = datetime.now() - timedelta(days=self.learning_window_days)
            recent_interactions = [
                interaction for interaction in user_interactions
                if interaction.timestamp >= cutoff_date
            ]
            
            if not recent_interactions:
                return create_default_preferences(user_id)
            
            # 分析交互模式
            preferences = await self._analyze_preferences(user_id, recent_interactions)
            
            # 存储学习结果
            self.preferences[user_id] = preferences
            
            logger.info(f"学习用户偏好完成: {user_id}, 置信度: {preferences.confidence_score:.2f}")
            return preferences
            
        except Exception as e:
            logger.error(f"学习用户偏好失败: {str(e)}")
            return create_default_preferences(user_id)
    
    async def _analyze_preferences(
        self, 
        user_id: str, 
        interactions: List[UserInteraction]
    ) -> UserPreferences:
        """
        分析用户偏好
        
        Args:
            user_id: 用户ID
            interactions: 交互记录列表
            
        Returns:
            分析得出的用户偏好
        """
        # 获取现有偏好或创建默认偏好
        current_preferences = self.preferences.get(user_id, create_default_preferences(user_id))
        
        # 分析运动类型偏好
        exercise_mentions = []
        for interaction in interactions:
            if interaction.interaction_type == InteractionType.EXERCISE_REQUEST:
                # 从内容中提取运动类型（简化实现）
                content_lower = interaction.content.lower()
                if "跑步" in content_lower or "有氧" in content_lower:
                    exercise_mentions.append("cardio")
                elif "力量" in content_lower or "举重" in content_lower:
                    exercise_mentions.append("strength")
                elif "瑜伽" in content_lower or "拉伸" in content_lower:
                    exercise_mentions.append("flexibility")
        
        # 统计最常提到的运动类型
        if exercise_mentions:
            exercise_counter = Counter(exercise_mentions)
            current_preferences.exercise_types = [
                exercise for exercise, _ in exercise_counter.most_common(3)
            ]
        
        # 分析难度级别偏好
        difficulty_indicators = []
        for interaction in interactions:
            content_lower = interaction.content.lower()
            if "初学" in content_lower or "新手" in content_lower:
                difficulty_indicators.append(DifficultyLevel.BEGINNER)
            elif "进阶" in content_lower or "中级" in content_lower:
                difficulty_indicators.append(DifficultyLevel.INTERMEDIATE)
            elif "高级" in content_lower or "专业" in content_lower:
                difficulty_indicators.append(DifficultyLevel.ADVANCED)
        
        if difficulty_indicators:
            difficulty_counter = Counter(difficulty_indicators)
            current_preferences.difficulty_level = difficulty_counter.most_common(1)[0][0]
        
        # 分析目标类型
        goal_mentions = []
        for interaction in interactions:
            content_lower = interaction.content.lower()
            if "减肥" in content_lower or "减重" in content_lower:
                goal_mentions.append(GoalType.WEIGHT_LOSS)
            elif "增肌" in content_lower or "肌肉" in content_lower:
                goal_mentions.append(GoalType.MUSCLE_GAIN)
            elif "耐力" in content_lower:
                goal_mentions.append(GoalType.ENDURANCE)
            elif "力量" in content_lower:
                goal_mentions.append(GoalType.STRENGTH)
        
        if goal_mentions:
            goal_counter = Counter(goal_mentions)
            current_preferences.goals = [
                goal for goal, _ in goal_counter.most_common(2)
            ]
        
        # 分析时间偏好
        time_mentions = []
        for interaction in interactions:
            hour = interaction.timestamp.hour
            if 6 <= hour < 10:
                time_mentions.append("morning")
            elif 10 <= hour < 14:
                time_mentions.append("midday")
            elif 14 <= hour < 18:
                time_mentions.append("afternoon")
            elif 18 <= hour < 22:
                time_mentions.append("evening")
        
        if time_mentions:
            time_counter = Counter(time_mentions)
            current_preferences.preferred_time_slots = [
                time_slot for time_slot, _ in time_counter.most_common(2)
            ]
        
        # 计算置信度
        confidence_factors = []
        if len(interactions) >= self.min_interactions:
            confidence_factors.append(min(len(interactions) / 20, 1.0))  # 交互数量因子
        
        if exercise_mentions:
            confidence_factors.append(0.8)  # 运动偏好因子
        
        if goal_mentions:
            confidence_factors.append(0.7)  # 目标因子
        
        current_preferences.confidence_score = np.mean(confidence_factors) if confidence_factors else 0.3
        current_preferences.updated_at = datetime.now()
        
        return current_preferences
    
    async def detect_patterns(self, user_id: str) -> List[LearningPattern]:
        """
        检测用户行为模式
        
        Args:
            user_id: 用户ID
            
        Returns:
            检测到的行为模式列表
        """
        try:
            user_interactions = self.interactions.get(user_id, [])
            
            if len(user_interactions) < self.min_interactions:
                return []
            
            patterns = []
            
            # 检测时间模式
            time_pattern = await self._detect_time_pattern(user_id, user_interactions)
            if time_pattern:
                patterns.append(time_pattern)
            
            # 检测交互频率模式
            frequency_pattern = await self._detect_frequency_pattern(user_id, user_interactions)
            if frequency_pattern:
                patterns.append(frequency_pattern)
            
            # 检测内容偏好模式
            content_pattern = await self._detect_content_pattern(user_id, user_interactions)
            if content_pattern:
                patterns.append(content_pattern)
            
            # 存储模式
            self.patterns[user_id] = patterns
            
            logger.info(f"检测到用户 {user_id} 的 {len(patterns)} 个行为模式")
            return patterns
            
        except Exception as e:
            logger.error(f"模式检测失败: {str(e)}")
            return []
    
    async def _detect_time_pattern(
        self, 
        user_id: str, 
        interactions: List[UserInteraction]
    ) -> Optional[LearningPattern]:
        """检测时间使用模式"""
        try:
            # 分析用户活跃时间
            hour_counts = defaultdict(int)
            for interaction in interactions:
                hour_counts[interaction.timestamp.hour] += 1
            
            if not hour_counts:
                return None
            
            # 找出最活跃的时间段
            most_active_hour = max(hour_counts, key=hour_counts.get)
            total_interactions = len(interactions)
            peak_ratio = hour_counts[most_active_hour] / total_interactions
            
            if peak_ratio >= 0.3:  # 如果某个时间段占比超过30%
                import uuid
                return LearningPattern(
                    pattern_id=str(uuid.uuid4()),
                    user_id=user_id,
                    pattern_type="time_preference",
                    pattern_data={
                        "peak_hour": most_active_hour,
                        "peak_ratio": peak_ratio,
                        "hour_distribution": dict(hour_counts)
                    },
                    frequency=hour_counts[most_active_hour],
                    confidence=min(peak_ratio * 2, 1.0)
                )
            
            return None
            
        except Exception as e:
            logger.error(f"时间模式检测失败: {str(e)}")
            return None
    
    async def _detect_frequency_pattern(
        self, 
        user_id: str, 
        interactions: List[UserInteraction]
    ) -> Optional[LearningPattern]:
        """检测交互频率模式"""
        try:
            if len(interactions) < 7:  # 至少需要一周的数据
                return None
            
            # 按日期分组统计
            daily_counts = defaultdict(int)
            for interaction in interactions:
                date_key = interaction.timestamp.date()
                daily_counts[date_key] += 1
            
            if len(daily_counts) < 3:
                return None
            
            # 计算平均每日交互次数
            avg_daily_interactions = np.mean(list(daily_counts.values()))
            std_daily_interactions = np.std(list(daily_counts.values()))
            
            # 判断是否有规律性
            regularity_score = 1.0 - (std_daily_interactions / max(avg_daily_interactions, 1))
            
            if regularity_score >= 0.6:  # 规律性较强
                import uuid
                return LearningPattern(
                    pattern_id=str(uuid.uuid4()),
                    user_id=user_id,
                    pattern_type="interaction_frequency",
                    pattern_data={
                        "avg_daily_interactions": avg_daily_interactions,
                        "regularity_score": regularity_score,
                        "active_days": len(daily_counts)
                    },
                    frequency=len(daily_counts),
                    confidence=regularity_score
                )
            
            return None
            
        except Exception as e:
            logger.error(f"频率模式检测失败: {str(e)}")
            return None
    
    async def _detect_content_pattern(
        self, 
        user_id: str, 
        interactions: List[UserInteraction]
    ) -> Optional[LearningPattern]:
        """检测内容偏好模式"""
        try:
            # 分析意图分布
            intent_counts = defaultdict(int)
            for interaction in interactions:
                if interaction.intent:
                    intent_counts[interaction.intent] += 1
            
            if not intent_counts:
                return None
            
            # 找出主要意图
            total_intents = sum(intent_counts.values())
            dominant_intent = max(intent_counts, key=intent_counts.get)
            dominance_ratio = intent_counts[dominant_intent] / total_intents
            
            if dominance_ratio >= 0.4:  # 主要意图占比超过40%
                import uuid
                return LearningPattern(
                    pattern_id=str(uuid.uuid4()),
                    user_id=user_id,
                    pattern_type="content_preference",
                    pattern_data={
                        "dominant_intent": dominant_intent,
                        "dominance_ratio": dominance_ratio,
                        "intent_distribution": dict(intent_counts)
                    },
                    frequency=intent_counts[dominant_intent],
                    confidence=dominance_ratio
                )
            
            return None
            
        except Exception as e:
            logger.error(f"内容模式检测失败: {str(e)}")
            return None
    
    async def _update_learning(self, user_id: str) -> None:
        """更新用户学习状态"""
        try:
            # 异步更新偏好和模式
            await asyncio.gather(
                self.learn_user_preferences(user_id),
                self.detect_patterns(user_id)
            )
        except Exception as e:
            logger.error(f"学习更新失败: {str(e)}")
    
    def get_user_preferences(self, user_id: str) -> Optional[UserPreferences]:
        """获取用户偏好"""
        return self.preferences.get(user_id)
    
    def get_user_patterns(self, user_id: str) -> List[LearningPattern]:
        """获取用户行为模式"""
        return self.patterns.get(user_id, [])
    
    def get_learning_summary(self, user_id: str) -> Dict[str, Any]:
        """获取学习摘要"""
        preferences = self.get_user_preferences(user_id)
        patterns = self.get_user_patterns(user_id)
        interactions = self.interactions.get(user_id, [])
        
        return {
            "user_id": user_id,
            "total_interactions": len(interactions),
            "preferences": preferences.to_dict() if preferences else None,
            "patterns_count": len(patterns),
            "patterns": [pattern.to_dict() for pattern in patterns],
            "last_interaction": interactions[-1].timestamp.isoformat() if interactions else None,
            "learning_confidence": preferences.confidence_score if preferences else 0.0
        }
