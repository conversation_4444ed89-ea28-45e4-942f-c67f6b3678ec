"""
指标收集器

收集系统各组件的性能指标和业务指标。
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any, List, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import threading

logger = logging.getLogger(__name__)

class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"        # 计数器（只增不减）
    GAUGE = "gauge"           # 仪表（可增可减）
    HISTOGRAM = "histogram"   # 直方图
    SUMMARY = "summary"       # 摘要
    TIMER = "timer"          # 计时器

class MetricCategory(Enum):
    """指标分类枚举"""
    SYSTEM = "system"         # 系统指标
    APPLICATION = "application"  # 应用指标
    BUSINESS = "business"     # 业务指标
    USER = "user"            # 用户指标
    PERFORMANCE = "performance"  # 性能指标

@dataclass
class Metric:
    """指标数据结构"""
    name: str
    value: Union[int, float]
    metric_type: MetricType
    category: MetricCategory
    timestamp: float
    labels: Dict[str, str] = field(default_factory=dict)
    unit: str = ""
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "value": self.value,
            "type": self.metric_type.value,
            "category": self.category.value,
            "timestamp": self.timestamp,
            "labels": self.labels,
            "unit": self.unit,
            "description": self.description
        }

@dataclass
class MetricAggregation:
    """指标聚合"""
    name: str
    count: int
    sum: float
    min: float
    max: float
    avg: float
    percentiles: Dict[str, float] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化指标收集器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.collection_interval = self.config.get("collection_interval", 60)
        self.retention_days = self.config.get("retention_days", 30)
        self.max_metrics_per_category = self.config.get("max_metrics_per_category", 10000)
        
        # 指标存储
        self.metrics_storage: Dict[MetricCategory, deque] = {
            category: deque(maxlen=self.max_metrics_per_category)
            for category in MetricCategory
        }
        
        # 实时指标缓存
        self.current_metrics: Dict[str, Metric] = {}
        
        # 聚合数据
        self.aggregated_metrics: Dict[str, Dict[str, MetricAggregation]] = {
            "minute": {},
            "hour": {},
            "day": {}
        }
        
        # 指标注册表
        self.metric_registry: Dict[str, Dict[str, Any]] = {}
        
        # 收集器状态
        self.running = False
        self.collection_task = None
        
        # 外部数据源
        self.data_sources: List[Callable[[], List[Metric]]] = []
        
        # 锁
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            "total_metrics_collected": 0,
            "metrics_by_category": {category.value: 0 for category in MetricCategory},
            "collection_errors": 0,
            "last_collection_time": None
        }
        
        logger.info("指标收集器初始化完成")
    
    async def start(self):
        """启动指标收集器"""
        try:
            if self.running:
                logger.warning("指标收集器已在运行")
                return
            
            self.running = True
            self.collection_task = asyncio.create_task(self._collection_loop())
            
            logger.info("指标收集器启动成功")
            
        except Exception as e:
            logger.error(f"指标收集器启动失败: {str(e)}")
            raise
    
    async def stop(self):
        """停止指标收集器"""
        try:
            self.running = False
            
            if self.collection_task:
                self.collection_task.cancel()
                try:
                    await self.collection_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("指标收集器停止成功")
            
        except Exception as e:
            logger.error(f"指标收集器停止失败: {str(e)}")
    
    def register_metric(
        self,
        name: str,
        metric_type: MetricType,
        category: MetricCategory,
        description: str = "",
        unit: str = "",
        labels: Optional[Dict[str, str]] = None
    ):
        """
        注册指标
        
        Args:
            name: 指标名称
            metric_type: 指标类型
            category: 指标分类
            description: 描述
            unit: 单位
            labels: 标签
        """
        try:
            self.metric_registry[name] = {
                "type": metric_type,
                "category": category,
                "description": description,
                "unit": unit,
                "labels": labels or {},
                "registered_at": time.time()
            }
            
            logger.debug(f"指标注册成功: {name}")
            
        except Exception as e:
            logger.error(f"指标注册失败: {str(e)}")
    
    def record_metric(
        self,
        name: str,
        value: Union[int, float],
        labels: Optional[Dict[str, str]] = None,
        timestamp: Optional[float] = None
    ):
        """
        记录指标
        
        Args:
            name: 指标名称
            value: 指标值
            labels: 标签
            timestamp: 时间戳
        """
        try:
            if name not in self.metric_registry:
                logger.warning(f"未注册的指标: {name}")
                return
            
            registry_info = self.metric_registry[name]
            
            # 合并标签
            combined_labels = {**registry_info["labels"], **(labels or {})}
            
            # 创建指标
            metric = Metric(
                name=name,
                value=value,
                metric_type=registry_info["type"],
                category=registry_info["category"],
                timestamp=timestamp or time.time(),
                labels=combined_labels,
                unit=registry_info["unit"],
                description=registry_info["description"]
            )
            
            # 存储指标
            with self.lock:
                self.current_metrics[name] = metric
                self.metrics_storage[metric.category].append(metric)
                
                # 更新统计
                self.stats["total_metrics_collected"] += 1
                self.stats["metrics_by_category"][metric.category.value] += 1
            
            logger.debug(f"指标记录成功: {name}={value}")
            
        except Exception as e:
            logger.error(f"指标记录失败: {str(e)}")
            self.stats["collection_errors"] += 1
    
    def add_data_source(self, data_source: Callable[[], List[Metric]]):
        """添加数据源"""
        self.data_sources.append(data_source)
        logger.info(f"数据源添加成功，当前数据源数量: {len(self.data_sources)}")
    
    def remove_data_source(self, data_source: Callable[[], List[Metric]]):
        """移除数据源"""
        if data_source in self.data_sources:
            self.data_sources.remove(data_source)
            logger.info(f"数据源移除成功，当前数据源数量: {len(self.data_sources)}")
    
    async def _collection_loop(self):
        """收集循环"""
        while self.running:
            try:
                # 收集指标
                await self._collect_metrics()
                
                # 聚合指标
                await self._aggregate_metrics()
                
                # 清理过期数据
                await self._cleanup_expired_metrics()
                
                # 更新统计
                self.stats["last_collection_time"] = time.time()
                
                # 等待下一次收集
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"指标收集循环错误: {str(e)}")
                self.stats["collection_errors"] += 1
                await asyncio.sleep(self.collection_interval)
    
    async def _collect_metrics(self):
        """收集指标"""
        try:
            # 从外部数据源收集
            for data_source in self.data_sources:
                try:
                    metrics = data_source()
                    for metric in metrics:
                        with self.lock:
                            self.current_metrics[metric.name] = metric
                            self.metrics_storage[metric.category].append(metric)
                            
                            # 更新统计
                            self.stats["total_metrics_collected"] += 1
                            self.stats["metrics_by_category"][metric.category.value] += 1
                            
                except Exception as e:
                    logger.error(f"数据源收集失败: {str(e)}")
                    self.stats["collection_errors"] += 1
            
            logger.debug(f"指标收集完成，收集到 {len(self.current_metrics)} 个指标")
            
        except Exception as e:
            logger.error(f"指标收集失败: {str(e)}")
            self.stats["collection_errors"] += 1
    
    async def _aggregate_metrics(self):
        """聚合指标"""
        try:
            current_time = time.time()
            
            # 按时间窗口聚合
            for window in ["minute", "hour", "day"]:
                window_size = self._get_window_size(window)
                window_start = current_time - (current_time % window_size)
                
                # 为每个指标名称创建聚合
                metric_groups = defaultdict(list)
                
                with self.lock:
                    for category_metrics in self.metrics_storage.values():
                        for metric in category_metrics:
                            if metric.timestamp >= window_start:
                                metric_groups[metric.name].append(metric.value)
                
                # 计算聚合统计
                for metric_name, values in metric_groups.items():
                    if values:
                        aggregation = MetricAggregation(
                            name=metric_name,
                            count=len(values),
                            sum=sum(values),
                            min=min(values),
                            max=max(values),
                            avg=sum(values) / len(values),
                            percentiles=self._calculate_percentiles(values),
                            timestamp=window_start
                        )
                        
                        self.aggregated_metrics[window][f"{metric_name}_{int(window_start)}"] = aggregation
            
            logger.debug("指标聚合完成")
            
        except Exception as e:
            logger.error(f"指标聚合失败: {str(e)}")
    
    def _get_window_size(self, window: str) -> int:
        """获取时间窗口大小（秒）"""
        if window == "minute":
            return 60
        elif window == "hour":
            return 3600
        elif window == "day":
            return 86400
        else:
            return 60
    
    def _calculate_percentiles(self, values: List[float]) -> Dict[str, float]:
        """计算百分位数"""
        try:
            sorted_values = sorted(values)
            n = len(sorted_values)
            
            percentiles = {}
            for p in [50, 90, 95, 99]:
                index = int(n * p / 100)
                if index >= n:
                    index = n - 1
                percentiles[f"p{p}"] = sorted_values[index]
            
            return percentiles
            
        except Exception as e:
            logger.error(f"百分位数计算失败: {str(e)}")
            return {}
    
    async def _cleanup_expired_metrics(self):
        """清理过期指标"""
        try:
            cutoff_time = time.time() - (self.retention_days * 24 * 3600)
            
            with self.lock:
                for category in MetricCategory:
                    metrics = self.metrics_storage[category]
                    # 移除过期指标
                    while metrics and metrics[0].timestamp < cutoff_time:
                        metrics.popleft()
            
            # 清理聚合数据
            for window in self.aggregated_metrics:
                expired_keys = [
                    key for key, agg in self.aggregated_metrics[window].items()
                    if agg.timestamp < cutoff_time
                ]
                for key in expired_keys:
                    del self.aggregated_metrics[window][key]
            
            logger.debug("过期指标清理完成")
            
        except Exception as e:
            logger.error(f"过期指标清理失败: {str(e)}")
    
    def get_current_metrics(self, category: Optional[MetricCategory] = None) -> Dict[str, Any]:
        """获取当前指标"""
        try:
            with self.lock:
                if category:
                    return {
                        name: metric.to_dict()
                        for name, metric in self.current_metrics.items()
                        if metric.category == category
                    }
                else:
                    return {
                        name: metric.to_dict()
                        for name, metric in self.current_metrics.items()
                    }
        except Exception as e:
            logger.error(f"当前指标获取失败: {str(e)}")
            return {}
    
    def get_metric_history(
        self,
        metric_name: str,
        duration_minutes: int = 60,
        category: Optional[MetricCategory] = None
    ) -> List[Dict[str, Any]]:
        """获取指标历史"""
        try:
            cutoff_time = time.time() - (duration_minutes * 60)
            history = []
            
            with self.lock:
                categories_to_search = [category] if category else list(MetricCategory)
                
                for cat in categories_to_search:
                    for metric in self.metrics_storage[cat]:
                        if (metric.name == metric_name and 
                            metric.timestamp >= cutoff_time):
                            history.append(metric.to_dict())
            
            # 按时间戳排序
            history.sort(key=lambda x: x["timestamp"])
            return history
            
        except Exception as e:
            logger.error(f"指标历史获取失败: {str(e)}")
            return []
    
    def get_aggregated_metrics(
        self,
        window: str = "hour",
        metric_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取聚合指标"""
        try:
            if window not in self.aggregated_metrics:
                return {}
            
            aggregations = self.aggregated_metrics[window]
            
            if metric_name:
                # 过滤特定指标
                filtered = {
                    key: agg for key, agg in aggregations.items()
                    if agg.name == metric_name
                }
                return {
                    key: {
                        "name": agg.name,
                        "count": agg.count,
                        "sum": agg.sum,
                        "min": agg.min,
                        "max": agg.max,
                        "avg": agg.avg,
                        "percentiles": agg.percentiles,
                        "timestamp": agg.timestamp
                    }
                    for key, agg in filtered.items()
                }
            else:
                # 返回所有聚合
                return {
                    key: {
                        "name": agg.name,
                        "count": agg.count,
                        "sum": agg.sum,
                        "min": agg.min,
                        "max": agg.max,
                        "avg": agg.avg,
                        "percentiles": agg.percentiles,
                        "timestamp": agg.timestamp
                    }
                    for key, agg in aggregations.items()
                }
                
        except Exception as e:
            logger.error(f"聚合指标获取失败: {str(e)}")
            return {}
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        try:
            with self.lock:
                summary = {
                    "total_registered_metrics": len(self.metric_registry),
                    "current_metrics_count": len(self.current_metrics),
                    "metrics_by_category": {
                        category.value: len(self.metrics_storage[category])
                        for category in MetricCategory
                    },
                    "aggregation_windows": list(self.aggregated_metrics.keys()),
                    "data_sources_count": len(self.data_sources),
                    "collection_stats": self.stats.copy(),
                    "running": self.running
                }
                
                return summary
                
        except Exception as e:
            logger.error(f"指标摘要获取失败: {str(e)}")
            return {"error": str(e)}
    
    def export_metrics(self, format_type: str = "json") -> str:
        """导出指标"""
        try:
            if format_type == "json":
                return json.dumps(self.get_current_metrics(), indent=2, ensure_ascii=False)
            elif format_type == "prometheus":
                return self._export_prometheus_format()
            elif format_type == "csv":
                return self._export_csv_format()
            else:
                raise ValueError(f"不支持的导出格式: {format_type}")
                
        except Exception as e:
            logger.error(f"指标导出失败: {str(e)}")
            return ""
    
    def _export_prometheus_format(self) -> str:
        """导出Prometheus格式"""
        lines = []
        
        with self.lock:
            for metric in self.current_metrics.values():
                # 构建标签字符串
                labels_str = ""
                if metric.labels:
                    label_pairs = [f'{k}="{v}"' for k, v in metric.labels.items()]
                    labels_str = "{" + ",".join(label_pairs) + "}"
                
                # 添加指标行
                lines.append(f"# HELP {metric.name} {metric.description}")
                lines.append(f"# TYPE {metric.name} {metric.metric_type.value}")
                lines.append(f"{metric.name}{labels_str} {metric.value} {int(metric.timestamp * 1000)}")
        
        return "\n".join(lines)
    
    def _export_csv_format(self) -> str:
        """导出CSV格式"""
        lines = ["name,value,type,category,timestamp,labels,unit,description"]
        
        with self.lock:
            for metric in self.current_metrics.values():
                labels_str = json.dumps(metric.labels)
                lines.append(
                    f"{metric.name},{metric.value},{metric.metric_type.value},"
                    f"{metric.category.value},{metric.timestamp},"
                    f'"{labels_str}",{metric.unit},"{metric.description}"'
                )
        
        return "\n".join(lines)

# 全局指标收集器实例
_global_metrics_collector: Optional[MetricsCollector] = None

def get_metrics_collector() -> MetricsCollector:
    """获取全局指标收集器"""
    global _global_metrics_collector
    if _global_metrics_collector is None:
        _global_metrics_collector = MetricsCollector()
    return _global_metrics_collector

def initialize_metrics_collector(config: Optional[Dict[str, Any]] = None) -> MetricsCollector:
    """初始化全局指标收集器"""
    global _global_metrics_collector
    _global_metrics_collector = MetricsCollector(config)
    return _global_metrics_collector
