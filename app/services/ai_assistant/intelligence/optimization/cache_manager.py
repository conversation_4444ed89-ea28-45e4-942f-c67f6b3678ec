"""
智能缓存管理器

实现多种缓存策略和智能缓存管理功能。
"""

import asyncio
import logging
import time
import hashlib
import json
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import OrderedDict
import threading

logger = logging.getLogger(__name__)

class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"          # 最近最少使用
    LFU = "lfu"          # 最少使用频率
    TTL = "ttl"          # 生存时间
    ADAPTIVE = "adaptive" # 自适应策略

class CacheStatus(Enum):
    """缓存状态枚举"""
    HIT = "hit"
    MISS = "miss"
    EXPIRED = "expired"
    EVICTED = "evicted"

@dataclass
class CacheItem:
    """缓存项"""
    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int = 0
    ttl: Optional[float] = None
    size_bytes: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    @property
    def age_seconds(self) -> float:
        """获取年龄（秒）"""
        return time.time() - self.created_at
    
    def update_access(self):
        """更新访问记录"""
        self.last_accessed = time.time()
        self.access_count += 1

@dataclass
class CacheStats:
    """缓存统计"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    expired: int = 0
    total_requests: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        if self.total_requests == 0:
            return 0.0
        return self.hits / self.total_requests
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 1.0 - self.hit_rate

class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化缓存管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.strategy = CacheStrategy(self.config.get("strategy", "adaptive"))
        self.max_size = self.config.get("max_size", 10000)
        self.default_ttl = self.config.get("default_ttl", 3600)
        self.max_memory_mb = self.config.get("max_memory_mb", 100)
        
        # 缓存存储
        self.cache: Dict[str, CacheItem] = {}
        self.access_order = OrderedDict()  # LRU 顺序
        self.frequency_counter: Dict[str, int] = {}  # LFU 计数
        
        # 统计信息
        self.stats = CacheStats()
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 自适应策略参数
        self.adaptive_weights = {
            "recency": 0.4,
            "frequency": 0.3,
            "size": 0.2,
            "ttl": 0.1
        }
        
        logger.info(f"智能缓存管理器初始化完成，策略: {self.strategy.value}")
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值或None
        """
        try:
            with self.lock:
                self.stats.total_requests += 1
                
                if key not in self.cache:
                    self.stats.misses += 1
                    logger.debug(f"缓存未命中: {key}")
                    return None
                
                item = self.cache[key]
                
                # 检查是否过期
                if item.is_expired:
                    await self._remove_item(key, CacheStatus.EXPIRED)
                    self.stats.expired += 1
                    self.stats.misses += 1
                    logger.debug(f"缓存过期: {key}")
                    return None
                
                # 更新访问记录
                item.update_access()
                await self._update_access_tracking(key)
                
                self.stats.hits += 1
                logger.debug(f"缓存命中: {key}")
                return item.value
                
        except Exception as e:
            logger.error(f"缓存获取失败: {str(e)}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        设置缓存项
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            metadata: 元数据
            
        Returns:
            设置是否成功
        """
        try:
            with self.lock:
                # 计算值大小
                size_bytes = await self._calculate_size(value)
                
                # 检查是否需要清理空间
                await self._ensure_space(size_bytes)
                
                # 创建缓存项
                item = CacheItem(
                    key=key,
                    value=value,
                    created_at=time.time(),
                    last_accessed=time.time(),
                    ttl=ttl or self.default_ttl,
                    size_bytes=size_bytes,
                    metadata=metadata or {}
                )
                
                # 如果键已存在，先移除旧项
                if key in self.cache:
                    await self._remove_item(key, CacheStatus.EVICTED)
                
                # 添加新项
                self.cache[key] = item
                await self._update_access_tracking(key)
                
                logger.debug(f"缓存设置成功: {key}, 大小: {size_bytes} bytes")
                return True
                
        except Exception as e:
            logger.error(f"缓存设置失败: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存项"""
        try:
            with self.lock:
                if key in self.cache:
                    await self._remove_item(key, CacheStatus.EVICTED)
                    logger.debug(f"缓存删除成功: {key}")
                    return True
                return False
        except Exception as e:
            logger.error(f"缓存删除失败: {str(e)}")
            return False
    
    async def clear(self) -> bool:
        """清空缓存"""
        try:
            with self.lock:
                self.cache.clear()
                self.access_order.clear()
                self.frequency_counter.clear()
                logger.info("缓存已清空")
                return True
        except Exception as e:
            logger.error(f"缓存清空失败: {str(e)}")
            return False
    
    async def _ensure_space(self, required_bytes: int):
        """确保有足够空间"""
        try:
            # 检查数量限制
            while len(self.cache) >= self.max_size:
                await self._evict_item()
            
            # 检查内存限制
            current_memory = await self._calculate_total_memory()
            max_memory_bytes = self.max_memory_mb * 1024 * 1024
            
            while current_memory + required_bytes > max_memory_bytes:
                if not await self._evict_item():
                    break  # 无法继续清理
                current_memory = await self._calculate_total_memory()
                
        except Exception as e:
            logger.error(f"空间清理失败: {str(e)}")
    
    async def _evict_item(self) -> bool:
        """根据策略驱逐缓存项"""
        try:
            if not self.cache:
                return False
            
            if self.strategy == CacheStrategy.LRU:
                key_to_evict = await self._select_lru_victim()
            elif self.strategy == CacheStrategy.LFU:
                key_to_evict = await self._select_lfu_victim()
            elif self.strategy == CacheStrategy.TTL:
                key_to_evict = await self._select_ttl_victim()
            elif self.strategy == CacheStrategy.ADAPTIVE:
                key_to_evict = await self._select_adaptive_victim()
            else:
                key_to_evict = next(iter(self.cache))  # 默认选择第一个
            
            if key_to_evict:
                await self._remove_item(key_to_evict, CacheStatus.EVICTED)
                self.stats.evictions += 1
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"缓存驱逐失败: {str(e)}")
            return False
    
    async def _select_lru_victim(self) -> Optional[str]:
        """选择LRU受害者"""
        if not self.access_order:
            return None
        return next(iter(self.access_order))
    
    async def _select_lfu_victim(self) -> Optional[str]:
        """选择LFU受害者"""
        if not self.frequency_counter:
            return None
        return min(self.frequency_counter, key=self.frequency_counter.get)
    
    async def _select_ttl_victim(self) -> Optional[str]:
        """选择TTL受害者（最早过期的）"""
        if not self.cache:
            return None
        
        # 找到最早过期的项
        earliest_expiry = None
        victim_key = None
        
        for key, item in self.cache.items():
            if item.ttl is None:
                continue
            
            expiry_time = item.created_at + item.ttl
            if earliest_expiry is None or expiry_time < earliest_expiry:
                earliest_expiry = expiry_time
                victim_key = key
        
        return victim_key or next(iter(self.cache))
    
    async def _select_adaptive_victim(self) -> Optional[str]:
        """选择自适应受害者"""
        if not self.cache:
            return None
        
        # 计算每个项的综合分数
        scores = {}
        current_time = time.time()
        
        for key, item in self.cache.items():
            # 时间因子（越旧分数越低）
            recency_score = 1.0 / (1.0 + (current_time - item.last_accessed) / 3600)
            
            # 频率因子
            frequency_score = item.access_count / max(1, max(i.access_count for i in self.cache.values()))
            
            # 大小因子（越大分数越低）
            max_size = max(1, max(i.size_bytes for i in self.cache.values()))
            size_score = 1.0 - (item.size_bytes / max_size)
            
            # TTL因子
            if item.ttl:
                remaining_ratio = max(0, (item.created_at + item.ttl - current_time) / item.ttl)
                ttl_score = remaining_ratio
            else:
                ttl_score = 1.0
            
            # 综合分数
            total_score = (
                self.adaptive_weights["recency"] * recency_score +
                self.adaptive_weights["frequency"] * frequency_score +
                self.adaptive_weights["size"] * size_score +
                self.adaptive_weights["ttl"] * ttl_score
            )
            
            scores[key] = total_score
        
        # 选择分数最低的项
        return min(scores, key=scores.get)
    
    async def _remove_item(self, key: str, status: CacheStatus):
        """移除缓存项"""
        try:
            if key in self.cache:
                del self.cache[key]
            
            if key in self.access_order:
                del self.access_order[key]
            
            if key in self.frequency_counter:
                del self.frequency_counter[key]
                
        except Exception as e:
            logger.error(f"缓存项移除失败: {str(e)}")
    
    async def _update_access_tracking(self, key: str):
        """更新访问跟踪"""
        try:
            # 更新LRU顺序
            if key in self.access_order:
                del self.access_order[key]
            self.access_order[key] = True
            
            # 更新LFU计数
            self.frequency_counter[key] = self.frequency_counter.get(key, 0) + 1
            
        except Exception as e:
            logger.error(f"访问跟踪更新失败: {str(e)}")
    
    async def _calculate_size(self, value: Any) -> int:
        """计算值的大小"""
        try:
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (int, float)):
                return 8  # 简化估算
            elif isinstance(value, (list, dict)):
                return len(json.dumps(value, ensure_ascii=False).encode('utf-8'))
            else:
                return len(str(value).encode('utf-8'))
        except Exception:
            return 1024  # 默认大小
    
    async def _calculate_total_memory(self) -> int:
        """计算总内存使用"""
        return sum(item.size_bytes for item in self.cache.values())
    
    async def cleanup_expired(self):
        """清理过期项"""
        try:
            with self.lock:
                expired_keys = []
                current_time = time.time()
                
                for key, item in self.cache.items():
                    if item.ttl and current_time - item.created_at > item.ttl:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    await self._remove_item(key, CacheStatus.EXPIRED)
                    self.stats.expired += 1
                
                if expired_keys:
                    logger.info(f"清理过期缓存项: {len(expired_keys)} 个")
                    
        except Exception as e:
            logger.error(f"过期清理失败: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            return {
                "strategy": self.strategy.value,
                "size": len(self.cache),
                "max_size": self.max_size,
                "memory_usage_mb": sum(item.size_bytes for item in self.cache.values()) / (1024 * 1024),
                "max_memory_mb": self.max_memory_mb,
                "stats": {
                    "hits": self.stats.hits,
                    "misses": self.stats.misses,
                    "evictions": self.stats.evictions,
                    "expired": self.stats.expired,
                    "total_requests": self.stats.total_requests,
                    "hit_rate": self.stats.hit_rate,
                    "miss_rate": self.stats.miss_rate
                }
            }
    
    def get_cache_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取缓存项信息"""
        with self.lock:
            if key not in self.cache:
                return None
            
            item = self.cache[key]
            return {
                "key": key,
                "size_bytes": item.size_bytes,
                "created_at": datetime.fromtimestamp(item.created_at).isoformat(),
                "last_accessed": datetime.fromtimestamp(item.last_accessed).isoformat(),
                "access_count": item.access_count,
                "age_seconds": item.age_seconds,
                "ttl": item.ttl,
                "is_expired": item.is_expired,
                "metadata": item.metadata
            }
    
    async def optimize_strategy(self):
        """优化缓存策略"""
        try:
            if self.strategy != CacheStrategy.ADAPTIVE:
                return
            
            # 基于当前性能调整权重
            hit_rate = self.stats.hit_rate
            
            if hit_rate < 0.5:
                # 命中率低，增加频率权重
                self.adaptive_weights["frequency"] = min(0.5, self.adaptive_weights["frequency"] + 0.1)
                self.adaptive_weights["recency"] = max(0.2, self.adaptive_weights["recency"] - 0.05)
            elif hit_rate > 0.8:
                # 命中率高，增加时间权重
                self.adaptive_weights["recency"] = min(0.5, self.adaptive_weights["recency"] + 0.1)
                self.adaptive_weights["frequency"] = max(0.2, self.adaptive_weights["frequency"] - 0.05)
            
            # 确保权重总和为1
            total_weight = sum(self.adaptive_weights.values())
            for key in self.adaptive_weights:
                self.adaptive_weights[key] /= total_weight
            
            logger.info(f"缓存策略优化完成，新权重: {self.adaptive_weights}")
            
        except Exception as e:
            logger.error(f"缓存策略优化失败: {str(e)}")

# 全局缓存管理器实例
_global_cache_manager: Optional[IntelligentCacheManager] = None

def get_cache_manager() -> IntelligentCacheManager:
    """获取全局缓存管理器"""
    global _global_cache_manager
    if _global_cache_manager is None:
        _global_cache_manager = IntelligentCacheManager()
    return _global_cache_manager

def initialize_cache_manager(config: Optional[Dict[str, Any]] = None) -> IntelligentCacheManager:
    """初始化全局缓存管理器"""
    global _global_cache_manager
    _global_cache_manager = IntelligentCacheManager(config)
    return _global_cache_manager
