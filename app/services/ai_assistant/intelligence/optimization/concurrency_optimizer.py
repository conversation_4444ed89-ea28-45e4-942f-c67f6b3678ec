"""
并发优化器

提升系统并发处理能力，包括任务队列、线程池和异步处理优化。
"""

import asyncio
import logging
import time
import threading
from typing import Dict, Any, List, Optional, Callable, Awaitable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue, PriorityQueue
import uuid

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class Task:
    """任务定义"""
    task_id: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[Exception] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        return self.priority.value > other.priority.value

@dataclass
class ConcurrencyStats:
    """并发统计"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    cancelled_tasks: int = 0
    active_tasks: int = 0
    queue_size: int = 0
    avg_execution_time: float = 0.0
    avg_queue_time: float = 0.0
    throughput_per_second: float = 0.0

class ConcurrencyOptimizer:
    """并发优化器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化并发优化器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.max_workers = self.config.get("max_workers", 10)
        self.queue_size = self.config.get("queue_size", 1000)
        self.default_timeout = self.config.get("default_timeout", 30)
        self.rate_limit_enabled = self.config.get("rate_limiting", True)
        self.rate_limit_per_second = self.config.get("rate_limit_per_second", 100)
        
        # 任务队列
        self.task_queue = PriorityQueue(maxsize=self.queue_size)
        self.active_tasks: Dict[str, Task] = {}
        self.completed_tasks: Dict[str, Task] = {}
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # 异步事件循环
        self.loop = None
        self.running = False
        
        # 统计信息
        self.stats = ConcurrencyStats()
        
        # 速率限制
        self.rate_limiter = RateLimiter(self.rate_limit_per_second) if self.rate_limit_enabled else None
        
        # 锁
        self.lock = threading.RLock()
        
        logger.info(f"并发优化器初始化完成，最大工作线程: {self.max_workers}")
    
    async def start(self):
        """启动并发优化器"""
        try:
            self.running = True
            self.loop = asyncio.get_event_loop()
            
            # 启动任务处理器
            asyncio.create_task(self._task_processor())
            
            # 启动统计更新器
            asyncio.create_task(self._stats_updater())
            
            logger.info("并发优化器启动成功")
            
        except Exception as e:
            logger.error(f"并发优化器启动失败: {str(e)}")
            raise
    
    async def stop(self):
        """停止并发优化器"""
        try:
            self.running = False
            
            # 等待所有任务完成
            await self._wait_for_completion()
            
            # 关闭线程池
            self.thread_pool.shutdown(wait=True)
            
            logger.info("并发优化器停止成功")
            
        except Exception as e:
            logger.error(f"并发优化器停止失败: {str(e)}")
    
    async def submit_task(
        self,
        func: Callable,
        *args,
        priority: TaskPriority = TaskPriority.NORMAL,
        timeout: Optional[float] = None,
        max_retries: int = 3,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> str:
        """
        提交任务
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            priority: 任务优先级
            timeout: 超时时间
            max_retries: 最大重试次数
            metadata: 元数据
            **kwargs: 函数关键字参数
            
        Returns:
            任务ID
        """
        try:
            # 速率限制检查
            if self.rate_limiter and not await self.rate_limiter.acquire():
                raise Exception("速率限制：请求过于频繁")
            
            # 创建任务
            task = Task(
                task_id=str(uuid.uuid4()),
                func=func,
                args=args,
                kwargs=kwargs,
                priority=priority,
                timeout=timeout or self.default_timeout,
                max_retries=max_retries,
                metadata=metadata or {}
            )
            
            # 添加到队列
            with self.lock:
                if self.task_queue.full():
                    raise Exception("任务队列已满")
                
                self.task_queue.put(task)
                self.stats.total_tasks += 1
                self.stats.queue_size = self.task_queue.qsize()
            
            logger.debug(f"任务提交成功: {task.task_id}")
            return task.task_id
            
        except Exception as e:
            logger.error(f"任务提交失败: {str(e)}")
            raise
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            with self.lock:
                # 检查活跃任务
                if task_id in self.active_tasks:
                    task = self.active_tasks[task_id]
                    return self._task_to_dict(task)
                
                # 检查已完成任务
                if task_id in self.completed_tasks:
                    task = self.completed_tasks[task_id]
                    return self._task_to_dict(task)
                
                return None
                
        except Exception as e:
            logger.error(f"任务状态获取失败: {str(e)}")
            return None
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            with self.lock:
                if task_id in self.active_tasks:
                    task = self.active_tasks[task_id]
                    task.status = TaskStatus.CANCELLED
                    self.stats.cancelled_tasks += 1
                    logger.info(f"任务取消成功: {task_id}")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"任务取消失败: {str(e)}")
            return False
    
    async def _task_processor(self):
        """任务处理器"""
        while self.running:
            try:
                # 获取任务
                if self.task_queue.empty():
                    await asyncio.sleep(0.1)
                    continue
                
                task = self.task_queue.get_nowait()
                
                # 检查是否被取消
                if task.status == TaskStatus.CANCELLED:
                    continue
                
                # 执行任务
                await self._execute_task(task)
                
            except Exception as e:
                logger.error(f"任务处理器错误: {str(e)}")
                await asyncio.sleep(1)
    
    async def _execute_task(self, task: Task):
        """执行任务"""
        try:
            with self.lock:
                task.status = TaskStatus.RUNNING
                task.started_at = time.time()
                self.active_tasks[task.task_id] = task
                self.stats.active_tasks += 1
                self.stats.queue_size = self.task_queue.qsize()
            
            # 执行任务
            if asyncio.iscoroutinefunction(task.func):
                # 异步函数
                result = await asyncio.wait_for(
                    task.func(*task.args, **task.kwargs),
                    timeout=task.timeout
                )
            else:
                # 同步函数，在线程池中执行
                future = self.thread_pool.submit(task.func, *task.args, **task.kwargs)
                result = await asyncio.wait_for(
                    asyncio.wrap_future(future),
                    timeout=task.timeout
                )
            
            # 任务成功完成
            await self._complete_task(task, result)
            
        except asyncio.TimeoutError:
            await self._fail_task(task, Exception("任务执行超时"))
        except Exception as e:
            await self._fail_task(task, e)
    
    async def _complete_task(self, task: Task, result: Any):
        """完成任务"""
        try:
            with self.lock:
                task.status = TaskStatus.COMPLETED
                task.completed_at = time.time()
                task.result = result
                
                # 移动到已完成任务
                if task.task_id in self.active_tasks:
                    del self.active_tasks[task.task_id]
                
                self.completed_tasks[task.task_id] = task
                
                # 更新统计
                self.stats.active_tasks -= 1
                self.stats.completed_tasks += 1
                
                # 限制已完成任务数量
                if len(self.completed_tasks) > 10000:
                    oldest_tasks = sorted(
                        self.completed_tasks.values(),
                        key=lambda t: t.completed_at or 0
                    )[:1000]
                    for old_task in oldest_tasks:
                        del self.completed_tasks[old_task.task_id]
            
            logger.debug(f"任务完成: {task.task_id}")
            
        except Exception as e:
            logger.error(f"任务完成处理失败: {str(e)}")
    
    async def _fail_task(self, task: Task, error: Exception):
        """任务失败处理"""
        try:
            with self.lock:
                task.error = error
                
                # 检查是否需要重试
                if task.retry_count < task.max_retries:
                    task.retry_count += 1
                    task.status = TaskStatus.PENDING
                    
                    # 重新加入队列
                    self.task_queue.put(task)
                    logger.info(f"任务重试: {task.task_id}, 第 {task.retry_count} 次")
                else:
                    # 最终失败
                    task.status = TaskStatus.FAILED
                    task.completed_at = time.time()
                    
                    # 移动到已完成任务
                    if task.task_id in self.active_tasks:
                        del self.active_tasks[task.task_id]
                    
                    self.completed_tasks[task.task_id] = task
                    
                    # 更新统计
                    self.stats.active_tasks -= 1
                    self.stats.failed_tasks += 1
                    
                    logger.warning(f"任务最终失败: {task.task_id}, 错误: {str(error)}")
                    
        except Exception as e:
            logger.error(f"任务失败处理错误: {str(e)}")
    
    async def _stats_updater(self):
        """统计信息更新器"""
        while self.running:
            try:
                await self._update_stats()
                await asyncio.sleep(60)  # 每分钟更新一次
            except Exception as e:
                logger.error(f"统计更新失败: {str(e)}")
                await asyncio.sleep(60)
    
    async def _update_stats(self):
        """更新统计信息"""
        try:
            with self.lock:
                # 计算平均执行时间
                completed_tasks = [t for t in self.completed_tasks.values() 
                                 if t.status == TaskStatus.COMPLETED and t.started_at and t.completed_at]
                
                if completed_tasks:
                    execution_times = [t.completed_at - t.started_at for t in completed_tasks]
                    self.stats.avg_execution_time = sum(execution_times) / len(execution_times)
                    
                    # 计算队列等待时间
                    queue_times = [t.started_at - t.created_at for t in completed_tasks]
                    self.stats.avg_queue_time = sum(queue_times) / len(queue_times)
                
                # 计算吞吐量（最近一分钟）
                recent_completed = [
                    t for t in completed_tasks
                    if t.completed_at and time.time() - t.completed_at < 60
                ]
                self.stats.throughput_per_second = len(recent_completed) / 60
                
                # 更新队列大小
                self.stats.queue_size = self.task_queue.qsize()
                
        except Exception as e:
            logger.error(f"统计更新失败: {str(e)}")
    
    async def _wait_for_completion(self, timeout: float = 30):
        """等待所有任务完成"""
        start_time = time.time()
        
        while self.active_tasks and time.time() - start_time < timeout:
            await asyncio.sleep(0.5)
        
        if self.active_tasks:
            logger.warning(f"仍有 {len(self.active_tasks)} 个任务未完成")
    
    def _task_to_dict(self, task: Task) -> Dict[str, Any]:
        """将任务转换为字典"""
        return {
            "task_id": task.task_id,
            "status": task.status.value,
            "priority": task.priority.value,
            "created_at": datetime.fromtimestamp(task.created_at).isoformat(),
            "started_at": datetime.fromtimestamp(task.started_at).isoformat() if task.started_at else None,
            "completed_at": datetime.fromtimestamp(task.completed_at).isoformat() if task.completed_at else None,
            "retry_count": task.retry_count,
            "max_retries": task.max_retries,
            "error": str(task.error) if task.error else None,
            "metadata": task.metadata
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            return {
                "total_tasks": self.stats.total_tasks,
                "completed_tasks": self.stats.completed_tasks,
                "failed_tasks": self.stats.failed_tasks,
                "cancelled_tasks": self.stats.cancelled_tasks,
                "active_tasks": self.stats.active_tasks,
                "queue_size": self.stats.queue_size,
                "avg_execution_time": self.stats.avg_execution_time,
                "avg_queue_time": self.stats.avg_queue_time,
                "throughput_per_second": self.stats.throughput_per_second,
                "success_rate": (
                    self.stats.completed_tasks / max(1, self.stats.total_tasks)
                    if self.stats.total_tasks > 0 else 0
                ),
                "config": {
                    "max_workers": self.max_workers,
                    "queue_size": self.queue_size,
                    "rate_limit_per_second": self.rate_limit_per_second
                }
            }

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, rate_per_second: float):
        """
        初始化速率限制器
        
        Args:
            rate_per_second: 每秒允许的请求数
        """
        self.rate = rate_per_second
        self.tokens = rate_per_second
        self.last_update = time.time()
        self.lock = threading.Lock()
    
    async def acquire(self) -> bool:
        """获取令牌"""
        with self.lock:
            now = time.time()
            elapsed = now - self.last_update
            
            # 添加令牌
            self.tokens = min(self.rate, self.tokens + elapsed * self.rate)
            self.last_update = now
            
            # 检查是否有可用令牌
            if self.tokens >= 1:
                self.tokens -= 1
                return True
            
            return False

# 全局并发优化器实例
_global_concurrency_optimizer: Optional[ConcurrencyOptimizer] = None

def get_concurrency_optimizer() -> ConcurrencyOptimizer:
    """获取全局并发优化器"""
    global _global_concurrency_optimizer
    if _global_concurrency_optimizer is None:
        _global_concurrency_optimizer = ConcurrencyOptimizer()
    return _global_concurrency_optimizer

def initialize_concurrency_optimizer(config: Optional[Dict[str, Any]] = None) -> ConcurrencyOptimizer:
    """初始化全局并发优化器"""
    global _global_concurrency_optimizer
    _global_concurrency_optimizer = ConcurrencyOptimizer(config)
    return _global_concurrency_optimizer
