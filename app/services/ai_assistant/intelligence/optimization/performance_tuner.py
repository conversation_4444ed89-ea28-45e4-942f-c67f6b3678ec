"""
性能调优器

自动分析系统性能并实施优化策略。
"""

import asyncio
import logging
import time
import statistics
from typing import Dict, Any, List, Optional, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import deque

logger = logging.getLogger(__name__)

class OptimizationStrategy(Enum):
    """优化策略枚举"""
    CACHE_TUNING = "cache_tuning"
    CONCURRENCY_TUNING = "concurrency_tuning"
    RESOURCE_TUNING = "resource_tuning"
    ALGORITHM_TUNING = "algorithm_tuning"

class PerformanceMetric(Enum):
    """性能指标枚举"""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    RESOURCE_USAGE = "resource_usage"
    CACHE_HIT_RATE = "cache_hit_rate"

@dataclass
class PerformanceTarget:
    """性能目标"""
    metric: PerformanceMetric
    target_value: float
    tolerance: float = 0.1
    weight: float = 1.0

@dataclass
class OptimizationAction:
    """优化动作"""
    action_id: str
    strategy: OptimizationStrategy
    description: str
    parameters: Dict[str, Any]
    expected_improvement: float
    risk_level: float
    timestamp: float
    applied: bool = False
    result: Optional[Dict[str, Any]] = None

@dataclass
class PerformanceSnapshot:
    """性能快照"""
    timestamp: float
    metrics: Dict[PerformanceMetric, float]
    system_state: Dict[str, Any]
    optimization_actions: List[str] = field(default_factory=list)

class PerformanceTuner:
    """性能调优器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化性能调优器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.auto_tuning_enabled = self.config.get("auto_tuning", True)
        self.tuning_interval = self.config.get("tuning_interval", 300)  # 5分钟
        self.analysis_window = self.config.get("analysis_window", 3600)  # 1小时
        self.max_risk_level = self.config.get("max_risk_level", 0.3)
        
        # 性能目标
        self.performance_targets = [
            PerformanceTarget(
                PerformanceMetric.RESPONSE_TIME,
                target_value=self.config.get("target_response_time_ms", 100),
                tolerance=0.2,
                weight=1.0
            ),
            PerformanceTarget(
                PerformanceMetric.THROUGHPUT,
                target_value=self.config.get("target_throughput_rps", 1000),
                tolerance=0.1,
                weight=0.8
            ),
            PerformanceTarget(
                PerformanceMetric.ERROR_RATE,
                target_value=self.config.get("target_error_rate", 0.01),
                tolerance=0.5,
                weight=1.2
            ),
            PerformanceTarget(
                PerformanceMetric.CACHE_HIT_RATE,
                target_value=self.config.get("target_cache_hit_rate", 0.8),
                tolerance=0.1,
                weight=0.6
            )
        ]
        
        # 数据存储
        self.performance_history: deque = deque(maxlen=1440)  # 24小时数据
        self.optimization_history: deque = deque(maxlen=1000)
        self.pending_actions: List[OptimizationAction] = []
        
        # 调优状态
        self.running = False
        self.tuning_task = None
        
        # 外部组件引用
        self.cache_manager = None
        self.concurrency_optimizer = None
        self.resource_monitor = None
        
        logger.info("性能调优器初始化完成")
    
    def set_components(
        self,
        cache_manager=None,
        concurrency_optimizer=None,
        resource_monitor=None
    ):
        """设置外部组件引用"""
        self.cache_manager = cache_manager
        self.concurrency_optimizer = concurrency_optimizer
        self.resource_monitor = resource_monitor
    
    async def start(self):
        """启动性能调优器"""
        try:
            if self.running:
                logger.warning("性能调优器已在运行")
                return
            
            self.running = True
            
            if self.auto_tuning_enabled:
                self.tuning_task = asyncio.create_task(self._tuning_loop())
            
            logger.info("性能调优器启动成功")
            
        except Exception as e:
            logger.error(f"性能调优器启动失败: {str(e)}")
            raise
    
    async def stop(self):
        """停止性能调优器"""
        try:
            self.running = False
            
            if self.tuning_task:
                self.tuning_task.cancel()
                try:
                    await self.tuning_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("性能调优器停止成功")
            
        except Exception as e:
            logger.error(f"性能调优器停止失败: {str(e)}")
    
    async def _tuning_loop(self):
        """调优循环"""
        while self.running:
            try:
                # 收集性能数据
                await self._collect_performance_data()
                
                # 分析性能
                analysis_result = await self._analyze_performance()
                
                # 生成优化建议
                if analysis_result["needs_optimization"]:
                    actions = await self._generate_optimization_actions(analysis_result)
                    
                    # 应用优化动作
                    for action in actions:
                        if action.risk_level <= self.max_risk_level:
                            await self._apply_optimization_action(action)
                
                # 等待下一次调优
                await asyncio.sleep(self.tuning_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"调优循环错误: {str(e)}")
                await asyncio.sleep(self.tuning_interval)
    
    async def _collect_performance_data(self):
        """收集性能数据"""
        try:
            current_time = time.time()
            metrics = {}
            system_state = {}
            
            # 收集缓存指标
            if self.cache_manager:
                cache_stats = self.cache_manager.get_stats()
                metrics[PerformanceMetric.CACHE_HIT_RATE] = cache_stats["stats"]["hit_rate"]
                system_state["cache"] = cache_stats
            
            # 收集并发指标
            if self.concurrency_optimizer:
                concurrency_stats = self.concurrency_optimizer.get_stats()
                metrics[PerformanceMetric.THROUGHPUT] = concurrency_stats["throughput_per_second"]
                metrics[PerformanceMetric.ERROR_RATE] = 1 - concurrency_stats["success_rate"]
                metrics[PerformanceMetric.RESPONSE_TIME] = concurrency_stats["avg_execution_time"] * 1000  # 转换为毫秒
                system_state["concurrency"] = concurrency_stats
            
            # 收集资源指标
            if self.resource_monitor:
                resource_metrics = self.resource_monitor.get_current_metrics()
                system_state["resources"] = resource_metrics
                
                # 计算资源使用率
                if "cpu" in resource_metrics and "memory" in resource_metrics:
                    cpu_usage = resource_metrics["cpu"]["value"]
                    memory_usage = resource_metrics["memory"]["value"]
                    metrics[PerformanceMetric.RESOURCE_USAGE] = (cpu_usage + memory_usage) / 2
            
            # 创建性能快照
            snapshot = PerformanceSnapshot(
                timestamp=current_time,
                metrics=metrics,
                system_state=system_state
            )
            
            self.performance_history.append(snapshot)
            
            logger.debug(f"性能数据收集完成: {len(metrics)} 个指标")
            
        except Exception as e:
            logger.error(f"性能数据收集失败: {str(e)}")
    
    async def _analyze_performance(self) -> Dict[str, Any]:
        """分析性能"""
        try:
            if len(self.performance_history) < 10:
                return {"needs_optimization": False, "reason": "数据不足"}
            
            # 获取最近的性能数据
            recent_snapshots = list(self.performance_history)[-60:]  # 最近60个数据点
            
            analysis_result = {
                "needs_optimization": False,
                "issues": [],
                "trends": {},
                "recommendations": []
            }
            
            # 分析每个性能目标
            for target in self.performance_targets:
                metric_values = [
                    snapshot.metrics.get(target.metric, 0)
                    for snapshot in recent_snapshots
                    if target.metric in snapshot.metrics
                ]
                
                if not metric_values:
                    continue
                
                current_value = statistics.mean(metric_values[-5:])  # 最近5个值的平均
                trend = self._calculate_trend(metric_values)
                
                analysis_result["trends"][target.metric.value] = {
                    "current_value": current_value,
                    "target_value": target.target_value,
                    "trend": trend,
                    "deviation": abs(current_value - target.target_value) / target.target_value
                }
                
                # 检查是否需要优化
                deviation = abs(current_value - target.target_value) / target.target_value
                if deviation > target.tolerance:
                    analysis_result["needs_optimization"] = True
                    analysis_result["issues"].append({
                        "metric": target.metric.value,
                        "current_value": current_value,
                        "target_value": target.target_value,
                        "deviation": deviation,
                        "severity": "high" if deviation > target.tolerance * 2 else "medium"
                    })
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"性能分析失败: {str(e)}")
            return {"needs_optimization": False, "error": str(e)}
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 5:
            return "stable"
        
        # 简单的线性趋势计算
        x = list(range(len(values)))
        y = values
        
        # 计算斜率
        n = len(values)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        
        if slope > 0.1:
            return "increasing"
        elif slope < -0.1:
            return "decreasing"
        else:
            return "stable"
    
    async def _generate_optimization_actions(self, analysis_result: Dict[str, Any]) -> List[OptimizationAction]:
        """生成优化动作"""
        try:
            actions = []
            
            for issue in analysis_result["issues"]:
                metric = issue["metric"]
                severity = issue["severity"]
                
                if metric == PerformanceMetric.RESPONSE_TIME.value:
                    actions.extend(await self._generate_response_time_actions(issue))
                elif metric == PerformanceMetric.THROUGHPUT.value:
                    actions.extend(await self._generate_throughput_actions(issue))
                elif metric == PerformanceMetric.CACHE_HIT_RATE.value:
                    actions.extend(await self._generate_cache_actions(issue))
                elif metric == PerformanceMetric.RESOURCE_USAGE.value:
                    actions.extend(await self._generate_resource_actions(issue))
            
            return actions
            
        except Exception as e:
            logger.error(f"优化动作生成失败: {str(e)}")
            return []
    
    async def _generate_response_time_actions(self, issue: Dict[str, Any]) -> List[OptimizationAction]:
        """生成响应时间优化动作"""
        actions = []
        
        # 缓存优化
        if self.cache_manager:
            actions.append(OptimizationAction(
                action_id=f"cache_optimize_{int(time.time())}",
                strategy=OptimizationStrategy.CACHE_TUNING,
                description="优化缓存策略以减少响应时间",
                parameters={"strategy": "adaptive", "increase_size": True},
                expected_improvement=0.2,
                risk_level=0.1,
                timestamp=time.time()
            ))
        
        # 并发优化
        if self.concurrency_optimizer:
            actions.append(OptimizationAction(
                action_id=f"concurrency_optimize_{int(time.time())}",
                strategy=OptimizationStrategy.CONCURRENCY_TUNING,
                description="增加并发处理能力",
                parameters={"increase_workers": 2},
                expected_improvement=0.15,
                risk_level=0.2,
                timestamp=time.time()
            ))
        
        return actions
    
    async def _generate_throughput_actions(self, issue: Dict[str, Any]) -> List[OptimizationAction]:
        """生成吞吐量优化动作"""
        actions = []
        
        # 并发优化
        actions.append(OptimizationAction(
            action_id=f"throughput_optimize_{int(time.time())}",
            strategy=OptimizationStrategy.CONCURRENCY_TUNING,
            description="优化并发处理以提高吞吐量",
            parameters={"optimize_queue": True, "adjust_timeout": True},
            expected_improvement=0.25,
            risk_level=0.15,
            timestamp=time.time()
        ))
        
        return actions
    
    async def _generate_cache_actions(self, issue: Dict[str, Any]) -> List[OptimizationAction]:
        """生成缓存优化动作"""
        actions = []
        
        actions.append(OptimizationAction(
            action_id=f"cache_strategy_{int(time.time())}",
            strategy=OptimizationStrategy.CACHE_TUNING,
            description="调整缓存策略以提高命中率",
            parameters={"optimize_strategy": True, "adjust_ttl": True},
            expected_improvement=0.3,
            risk_level=0.1,
            timestamp=time.time()
        ))
        
        return actions
    
    async def _generate_resource_actions(self, issue: Dict[str, Any]) -> List[OptimizationAction]:
        """生成资源优化动作"""
        actions = []
        
        actions.append(OptimizationAction(
            action_id=f"resource_optimize_{int(time.time())}",
            strategy=OptimizationStrategy.RESOURCE_TUNING,
            description="优化资源使用",
            parameters={"cleanup_memory": True, "optimize_gc": True},
            expected_improvement=0.2,
            risk_level=0.2,
            timestamp=time.time()
        ))
        
        return actions
    
    async def _apply_optimization_action(self, action: OptimizationAction):
        """应用优化动作"""
        try:
            logger.info(f"应用优化动作: {action.description}")
            
            if action.strategy == OptimizationStrategy.CACHE_TUNING:
                await self._apply_cache_optimization(action)
            elif action.strategy == OptimizationStrategy.CONCURRENCY_TUNING:
                await self._apply_concurrency_optimization(action)
            elif action.strategy == OptimizationStrategy.RESOURCE_TUNING:
                await self._apply_resource_optimization(action)
            
            action.applied = True
            action.result = {"status": "success", "applied_at": time.time()}
            
            self.optimization_history.append(action)
            
            logger.info(f"优化动作应用成功: {action.action_id}")
            
        except Exception as e:
            logger.error(f"优化动作应用失败: {str(e)}")
            action.result = {"status": "failed", "error": str(e)}
    
    async def _apply_cache_optimization(self, action: OptimizationAction):
        """应用缓存优化"""
        if not self.cache_manager:
            return
        
        parameters = action.parameters
        
        if parameters.get("optimize_strategy"):
            await self.cache_manager.optimize_strategy()
        
        if parameters.get("strategy") == "adaptive":
            # 切换到自适应策略的逻辑
            pass
    
    async def _apply_concurrency_optimization(self, action: OptimizationAction):
        """应用并发优化"""
        if not self.concurrency_optimizer:
            return
        
        parameters = action.parameters
        
        # 这里可以实现具体的并发优化逻辑
        # 例如调整工作线程数、队列大小等
        pass
    
    async def _apply_resource_optimization(self, action: OptimizationAction):
        """应用资源优化"""
        parameters = action.parameters
        
        if parameters.get("cleanup_memory"):
            # 执行内存清理
            import gc
            gc.collect()
        
        if parameters.get("optimize_gc"):
            # 优化垃圾回收设置
            pass
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        try:
            if not self.performance_history:
                return {"status": "no_data"}
            
            latest_snapshot = self.performance_history[-1]
            
            # 计算目标达成情况
            target_status = {}
            for target in self.performance_targets:
                if target.metric in latest_snapshot.metrics:
                    current_value = latest_snapshot.metrics[target.metric]
                    deviation = abs(current_value - target.target_value) / target.target_value
                    target_status[target.metric.value] = {
                        "current": current_value,
                        "target": target.target_value,
                        "deviation": deviation,
                        "status": "good" if deviation <= target.tolerance else "needs_attention"
                    }
            
            # 最近的优化动作
            recent_actions = [
                {
                    "action_id": action.action_id,
                    "strategy": action.strategy.value,
                    "description": action.description,
                    "applied": action.applied,
                    "timestamp": datetime.fromtimestamp(action.timestamp).isoformat()
                }
                for action in list(self.optimization_history)[-10:]
            ]
            
            return {
                "status": "running" if self.running else "stopped",
                "auto_tuning_enabled": self.auto_tuning_enabled,
                "target_status": target_status,
                "recent_optimizations": recent_actions,
                "performance_trends": self._get_performance_trends(),
                "last_analysis": datetime.fromtimestamp(latest_snapshot.timestamp).isoformat()
            }
            
        except Exception as e:
            logger.error(f"性能摘要获取失败: {str(e)}")
            return {"error": str(e)}
    
    def _get_performance_trends(self) -> Dict[str, str]:
        """获取性能趋势"""
        trends = {}
        
        for target in self.performance_targets:
            recent_values = [
                snapshot.metrics.get(target.metric, 0)
                for snapshot in list(self.performance_history)[-30:]
                if target.metric in snapshot.metrics
            ]
            
            if recent_values:
                trends[target.metric.value] = self._calculate_trend(recent_values)
        
        return trends
    
    def force_optimization_analysis(self) -> Dict[str, Any]:
        """强制执行优化分析"""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self._analyze_performance())
        except Exception as e:
            logger.error(f"强制优化分析失败: {str(e)}")
            return {"error": str(e)}

# 全局性能调优器实例
_global_performance_tuner: Optional[PerformanceTuner] = None

def get_performance_tuner() -> PerformanceTuner:
    """获取全局性能调优器"""
    global _global_performance_tuner
    if _global_performance_tuner is None:
        _global_performance_tuner = PerformanceTuner()
    return _global_performance_tuner

def initialize_performance_tuner(config: Optional[Dict[str, Any]] = None) -> PerformanceTuner:
    """初始化全局性能调优器"""
    global _global_performance_tuner
    _global_performance_tuner = PerformanceTuner(config)
    return _global_performance_tuner
