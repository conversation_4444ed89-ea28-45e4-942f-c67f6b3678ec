"""
资源监控器

实时监控系统资源使用情况，包括CPU、内存、磁盘和网络。
"""

import asyncio
import logging
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import deque

logger = logging.getLogger(__name__)

class ResourceType(Enum):
    """资源类型枚举"""
    CPU = "cpu"
    MEMORY = "memory"
    DISK = "disk"
    NETWORK = "network"
    PROCESS = "process"

class AlertLevel(Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"

@dataclass
class ResourceMetric:
    """资源指标"""
    resource_type: ResourceType
    timestamp: float
    value: float
    unit: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ResourceAlert:
    """资源告警"""
    alert_id: str
    resource_type: ResourceType
    level: AlertLevel
    message: str
    value: float
    threshold: float
    timestamp: float
    resolved: bool = False
    resolved_at: Optional[float] = None

@dataclass
class ResourceThreshold:
    """资源阈值"""
    resource_type: ResourceType
    warning_threshold: float
    critical_threshold: float
    enabled: bool = True

class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化资源监控器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.monitoring_interval = self.config.get("interval", 60)  # 监控间隔（秒）
        self.history_size = self.config.get("history_size", 1440)  # 历史数据大小（24小时）
        self.alert_enabled = self.config.get("alert_enabled", True)
        
        # 资源阈值配置
        self.thresholds = {
            ResourceType.CPU: ResourceThreshold(
                ResourceType.CPU,
                warning_threshold=self.config.get("cpu_warning", 70.0),
                critical_threshold=self.config.get("cpu_critical", 90.0)
            ),
            ResourceType.MEMORY: ResourceThreshold(
                ResourceType.MEMORY,
                warning_threshold=self.config.get("memory_warning", 80.0),
                critical_threshold=self.config.get("memory_critical", 95.0)
            ),
            ResourceType.DISK: ResourceThreshold(
                ResourceType.DISK,
                warning_threshold=self.config.get("disk_warning", 85.0),
                critical_threshold=self.config.get("disk_critical", 95.0)
            )
        }
        
        # 数据存储
        self.metrics_history: Dict[ResourceType, deque] = {
            resource_type: deque(maxlen=self.history_size)
            for resource_type in ResourceType
        }
        
        self.active_alerts: Dict[str, ResourceAlert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        
        # 告警回调
        self.alert_callbacks: List[Callable[[ResourceAlert], None]] = []
        
        # 监控状态
        self.running = False
        self.monitor_task = None
        
        # 锁
        self.lock = threading.RLock()
        
        logger.info("资源监控器初始化完成")
    
    async def start(self):
        """启动资源监控"""
        try:
            if self.running:
                logger.warning("资源监控器已在运行")
                return
            
            self.running = True
            self.monitor_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("资源监控器启动成功")
            
        except Exception as e:
            logger.error(f"资源监控器启动失败: {str(e)}")
            raise
    
    async def stop(self):
        """停止资源监控"""
        try:
            self.running = False
            
            if self.monitor_task:
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("资源监控器停止成功")
            
        except Exception as e:
            logger.error(f"资源监控器停止失败: {str(e)}")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 收集资源指标
                await self._collect_metrics()
                
                # 检查告警
                if self.alert_enabled:
                    await self._check_alerts()
                
                # 等待下一次监控
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环错误: {str(e)}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _collect_metrics(self):
        """收集资源指标"""
        try:
            current_time = time.time()
            
            # CPU 使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_metric = ResourceMetric(
                resource_type=ResourceType.CPU,
                timestamp=current_time,
                value=cpu_percent,
                unit="percent",
                metadata={
                    "cpu_count": psutil.cpu_count(),
                    "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                }
            )
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_metric = ResourceMetric(
                resource_type=ResourceType.MEMORY,
                timestamp=current_time,
                value=memory.percent,
                unit="percent",
                metadata={
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "free": memory.free
                }
            )
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_metric = ResourceMetric(
                resource_type=ResourceType.DISK,
                timestamp=current_time,
                value=(disk.used / disk.total) * 100,
                unit="percent",
                metadata={
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free
                }
            )
            
            # 网络统计
            network = psutil.net_io_counters()
            network_metric = ResourceMetric(
                resource_type=ResourceType.NETWORK,
                timestamp=current_time,
                value=0,  # 网络没有单一的百分比值
                unit="bytes",
                metadata={
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                }
            )
            
            # 进程信息
            current_process = psutil.Process()
            process_metric = ResourceMetric(
                resource_type=ResourceType.PROCESS,
                timestamp=current_time,
                value=current_process.cpu_percent(),
                unit="percent",
                metadata={
                    "memory_info": current_process.memory_info()._asdict(),
                    "num_threads": current_process.num_threads(),
                    "create_time": current_process.create_time()
                }
            )
            
            # 存储指标
            with self.lock:
                self.metrics_history[ResourceType.CPU].append(cpu_metric)
                self.metrics_history[ResourceType.MEMORY].append(memory_metric)
                self.metrics_history[ResourceType.DISK].append(disk_metric)
                self.metrics_history[ResourceType.NETWORK].append(network_metric)
                self.metrics_history[ResourceType.PROCESS].append(process_metric)
            
            logger.debug(f"资源指标收集完成: CPU={cpu_percent:.1f}%, Memory={memory.percent:.1f}%, Disk={disk_metric.value:.1f}%")
            
        except Exception as e:
            logger.error(f"资源指标收集失败: {str(e)}")
    
    async def _check_alerts(self):
        """检查告警"""
        try:
            current_time = time.time()
            
            # 检查各种资源的告警
            for resource_type, threshold in self.thresholds.items():
                if not threshold.enabled:
                    continue
                
                # 获取最新指标
                if not self.metrics_history[resource_type]:
                    continue
                
                latest_metric = self.metrics_history[resource_type][-1]
                value = latest_metric.value
                
                # 检查临界告警
                if value >= threshold.critical_threshold:
                    await self._trigger_alert(
                        resource_type, AlertLevel.CRITICAL, value, threshold.critical_threshold
                    )
                # 检查警告告警
                elif value >= threshold.warning_threshold:
                    await self._trigger_alert(
                        resource_type, AlertLevel.WARNING, value, threshold.warning_threshold
                    )
                else:
                    # 检查是否需要解除告警
                    await self._resolve_alerts(resource_type)
                    
        except Exception as e:
            logger.error(f"告警检查失败: {str(e)}")
    
    async def _trigger_alert(
        self, 
        resource_type: ResourceType, 
        level: AlertLevel, 
        value: float, 
        threshold: float
    ):
        """触发告警"""
        try:
            alert_key = f"{resource_type.value}_{level.value}"
            
            # 检查是否已有相同告警
            if alert_key in self.active_alerts:
                return
            
            # 创建告警
            alert = ResourceAlert(
                alert_id=f"alert_{int(time.time())}_{resource_type.value}",
                resource_type=resource_type,
                level=level,
                message=f"{resource_type.value.upper()} 使用率 {level.value}: {value:.1f}% (阈值: {threshold:.1f}%)",
                value=value,
                threshold=threshold,
                timestamp=time.time()
            )
            
            with self.lock:
                self.active_alerts[alert_key] = alert
                self.alert_history.append(alert)
            
            # 调用告警回调
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"告警回调执行失败: {str(e)}")
            
            logger.warning(f"资源告警触发: {alert.message}")
            
        except Exception as e:
            logger.error(f"告警触发失败: {str(e)}")
    
    async def _resolve_alerts(self, resource_type: ResourceType):
        """解除告警"""
        try:
            resolved_alerts = []
            
            with self.lock:
                for alert_key, alert in list(self.active_alerts.items()):
                    if alert.resource_type == resource_type and not alert.resolved:
                        alert.resolved = True
                        alert.resolved_at = time.time()
                        resolved_alerts.append(alert_key)
                
                # 移除已解除的告警
                for alert_key in resolved_alerts:
                    del self.active_alerts[alert_key]
            
            if resolved_alerts:
                logger.info(f"资源告警已解除: {resource_type.value}")
                
        except Exception as e:
            logger.error(f"告警解除失败: {str(e)}")
    
    def add_alert_callback(self, callback: Callable[[ResourceAlert], None]):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def remove_alert_callback(self, callback: Callable[[ResourceAlert], None]):
        """移除告警回调"""
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前资源指标"""
        try:
            with self.lock:
                current_metrics = {}
                
                for resource_type, metrics in self.metrics_history.items():
                    if metrics:
                        latest_metric = metrics[-1]
                        current_metrics[resource_type.value] = {
                            "value": latest_metric.value,
                            "unit": latest_metric.unit,
                            "timestamp": datetime.fromtimestamp(latest_metric.timestamp).isoformat(),
                            "metadata": latest_metric.metadata
                        }
                
                return current_metrics
                
        except Exception as e:
            logger.error(f"当前指标获取失败: {str(e)}")
            return {}
    
    def get_metrics_history(
        self, 
        resource_type: ResourceType, 
        duration_minutes: int = 60
    ) -> List[Dict[str, Any]]:
        """获取指标历史"""
        try:
            with self.lock:
                if resource_type not in self.metrics_history:
                    return []
                
                cutoff_time = time.time() - (duration_minutes * 60)
                metrics = self.metrics_history[resource_type]
                
                # 过滤时间范围内的指标
                filtered_metrics = [
                    {
                        "timestamp": datetime.fromtimestamp(metric.timestamp).isoformat(),
                        "value": metric.value,
                        "unit": metric.unit,
                        "metadata": metric.metadata
                    }
                    for metric in metrics
                    if metric.timestamp >= cutoff_time
                ]
                
                return filtered_metrics
                
        except Exception as e:
            logger.error(f"指标历史获取失败: {str(e)}")
            return []
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        try:
            with self.lock:
                return [
                    {
                        "alert_id": alert.alert_id,
                        "resource_type": alert.resource_type.value,
                        "level": alert.level.value,
                        "message": alert.message,
                        "value": alert.value,
                        "threshold": alert.threshold,
                        "timestamp": datetime.fromtimestamp(alert.timestamp).isoformat()
                    }
                    for alert in self.active_alerts.values()
                ]
        except Exception as e:
            logger.error(f"活跃告警获取失败: {str(e)}")
            return []
    
    def get_alert_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取告警历史"""
        try:
            with self.lock:
                recent_alerts = list(self.alert_history)[-limit:]
                return [
                    {
                        "alert_id": alert.alert_id,
                        "resource_type": alert.resource_type.value,
                        "level": alert.level.value,
                        "message": alert.message,
                        "value": alert.value,
                        "threshold": alert.threshold,
                        "timestamp": datetime.fromtimestamp(alert.timestamp).isoformat(),
                        "resolved": alert.resolved,
                        "resolved_at": datetime.fromtimestamp(alert.resolved_at).isoformat() if alert.resolved_at else None
                    }
                    for alert in recent_alerts
                ]
        except Exception as e:
            logger.error(f"告警历史获取失败: {str(e)}")
            return []
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """获取资源摘要"""
        try:
            current_metrics = self.get_current_metrics()
            active_alerts = self.get_active_alerts()
            
            return {
                "current_metrics": current_metrics,
                "active_alerts_count": len(active_alerts),
                "critical_alerts": [alert for alert in active_alerts if alert["level"] == "critical"],
                "warning_alerts": [alert for alert in active_alerts if alert["level"] == "warning"],
                "monitoring_status": "running" if self.running else "stopped",
                "monitoring_interval": self.monitoring_interval,
                "thresholds": {
                    resource_type.value: {
                        "warning": threshold.warning_threshold,
                        "critical": threshold.critical_threshold,
                        "enabled": threshold.enabled
                    }
                    for resource_type, threshold in self.thresholds.items()
                }
            }
            
        except Exception as e:
            logger.error(f"资源摘要获取失败: {str(e)}")
            return {"error": str(e)}
    
    def update_threshold(
        self, 
        resource_type: ResourceType, 
        warning_threshold: Optional[float] = None,
        critical_threshold: Optional[float] = None,
        enabled: Optional[bool] = None
    ) -> bool:
        """更新资源阈值"""
        try:
            if resource_type not in self.thresholds:
                return False
            
            threshold = self.thresholds[resource_type]
            
            if warning_threshold is not None:
                threshold.warning_threshold = warning_threshold
            
            if critical_threshold is not None:
                threshold.critical_threshold = critical_threshold
            
            if enabled is not None:
                threshold.enabled = enabled
            
            logger.info(f"资源阈值更新成功: {resource_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"资源阈值更新失败: {str(e)}")
            return False

# 全局资源监控器实例
_global_resource_monitor: Optional[ResourceMonitor] = None

def get_resource_monitor() -> ResourceMonitor:
    """获取全局资源监控器"""
    global _global_resource_monitor
    if _global_resource_monitor is None:
        _global_resource_monitor = ResourceMonitor()
    return _global_resource_monitor

def initialize_resource_monitor(config: Optional[Dict[str, Any]] = None) -> ResourceMonitor:
    """初始化全局资源监控器"""
    global _global_resource_monitor
    _global_resource_monitor = ResourceMonitor(config)
    return _global_resource_monitor
