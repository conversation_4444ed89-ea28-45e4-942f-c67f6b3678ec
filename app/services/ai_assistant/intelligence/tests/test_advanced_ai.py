"""
高级AI特性模块测试

测试多模态处理、长期记忆、复杂推理和上下文管理功能。
"""

import asyncio
import pytest
import sys
from datetime import datetime, timedelta
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append('/home/<USER>/backend')

from app.services.ai_assistant.intelligence.advanced_ai.multimodal_processor import (
    MultimodalProcessor, MultimodalInput, ModalityType
)
from app.services.ai_assistant.intelligence.advanced_ai.long_term_memory import (
    LongTermMemorySystem, Memory, MemoryType, MemoryImportance, MemoryQuery
)
from app.services.ai_assistant.intelligence.advanced_ai.complex_reasoning import (
    ComplexReasoningEngine, ReasoningContext, ReasoningType
)
from app.services.ai_assistant.intelligence.advanced_ai.context_manager import (
    ContextManager, ContextType, ContextScope
)

class TestMultimodalProcessor:
    """测试多模态处理器"""
    
    def test_processor_initialization(self):
        """测试处理器初始化"""
        processor = MultimodalProcessor()
        assert processor is not None
        assert processor.max_file_size_mb == 10
        assert "jpg" in processor.supported_image_formats
    
    @pytest.mark.asyncio
    async def test_text_processing(self):
        """测试文本处理"""
        processor = MultimodalProcessor()
        
        text_input = MultimodalInput(
            input_id="test_text_1",
            user_id="test_user",
            modality_type=ModalityType.TEXT,
            content="我想了解健身训练",
            metadata={},
            timestamp=datetime.now()
        )
        
        result = await processor.process_multimodal_input(text_input)
        
        assert result.output_id is not None
        assert result.confidence > 0
        assert "original_text" in result.processed_content
        assert "fitness_analysis" in result.analysis_results
    
    @pytest.mark.asyncio
    async def test_image_processing(self):
        """测试图像处理"""
        processor = MultimodalProcessor()
        
        # 模拟图像数据
        fake_image_data = b"fake_image_data"
        
        image_input = MultimodalInput(
            input_id="test_image_1",
            user_id="test_user",
            modality_type=ModalityType.IMAGE,
            content=fake_image_data,
            metadata={"format": "jpg", "dimensions": "800x600"},
            timestamp=datetime.now()
        )
        
        result = await processor.process_multimodal_input(image_input)
        
        assert result.output_id is not None
        assert "image_info" in result.processed_content
        assert "image_analysis" in result.analysis_results
    
    @pytest.mark.asyncio
    async def test_multimodal_processing(self):
        """测试多模态组合处理"""
        processor = MultimodalProcessor()
        
        multimodal_content = {
            "text": "这是我的健身照片",
            "image": "image_data_placeholder"
        }
        
        multimodal_input = MultimodalInput(
            input_id="test_multimodal_1",
            user_id="test_user",
            modality_type=ModalityType.MULTIMODAL,
            content=multimodal_content,
            metadata={},
            timestamp=datetime.now()
        )
        
        result = await processor.process_multimodal_input(multimodal_input)
        
        assert result.output_id is not None
        assert "modalities" in result.processed_content
        assert "fusion_analysis" in result.analysis_results

class TestLongTermMemorySystem:
    """测试长期记忆系统"""
    
    def test_memory_system_initialization(self):
        """测试记忆系统初始化"""
        memory_system = LongTermMemorySystem()
        assert memory_system is not None
        assert memory_system.retention_days == 30
        assert memory_system.max_memories_per_user == 1000
    
    @pytest.mark.asyncio
    async def test_store_memory(self):
        """测试存储记忆"""
        memory_system = LongTermMemorySystem()
        
        memory_id = await memory_system.store_memory(
            user_id="test_user",
            memory_type=MemoryType.WORKOUT,
            content={"exercise": "跑步", "duration": 30, "intensity": "medium"},
            importance=MemoryImportance.MEDIUM,
            tags=["cardio", "running"]
        )
        
        assert memory_id is not None
        assert memory_id in memory_system.memory_index
        
        # 验证记忆内容
        stored_memory = memory_system.memory_index[memory_id]
        assert stored_memory.user_id == "test_user"
        assert stored_memory.memory_type == MemoryType.WORKOUT
        assert "exercise" in stored_memory.content
    
    @pytest.mark.asyncio
    async def test_retrieve_memories(self):
        """测试检索记忆"""
        memory_system = LongTermMemorySystem()
        
        # 存储多个记忆
        await memory_system.store_memory(
            user_id="test_user",
            memory_type=MemoryType.WORKOUT,
            content={"exercise": "跑步"},
            importance=MemoryImportance.HIGH
        )
        
        await memory_system.store_memory(
            user_id="test_user",
            memory_type=MemoryType.GOAL,
            content={"goal": "减肥"},
            importance=MemoryImportance.MEDIUM
        )
        
        # 检索记忆
        query = MemoryQuery(
            user_id="test_user",
            memory_types=[MemoryType.WORKOUT],
            limit=5
        )
        
        memories = await memory_system.retrieve_memories(query)
        
        assert len(memories) > 0
        assert all(memory.memory_type == MemoryType.WORKOUT for memory in memories)
    
    @pytest.mark.asyncio
    async def test_memory_summary(self):
        """测试记忆摘要"""
        memory_system = LongTermMemorySystem()
        
        # 存储一些记忆
        await memory_system.store_memory(
            user_id="test_user",
            memory_type=MemoryType.ACHIEVEMENT,
            content={"achievement": "完成第一次5公里跑"},
            importance=MemoryImportance.HIGH
        )
        
        summary = await memory_system.get_user_memory_summary("test_user")
        
        assert summary["user_id"] == "test_user"
        assert summary["total_memories"] > 0
        assert "by_type" in summary
        assert "oldest_memory" in summary

class TestComplexReasoningEngine:
    """测试复杂推理引擎"""
    
    def test_reasoning_engine_initialization(self):
        """测试推理引擎初始化"""
        reasoning_engine = ComplexReasoningEngine()
        assert reasoning_engine is not None
        assert reasoning_engine.max_reasoning_depth == 5
        assert reasoning_engine.confidence_threshold == 0.7
    
    @pytest.mark.asyncio
    async def test_basic_reasoning(self):
        """测试基础推理"""
        reasoning_engine = ComplexReasoningEngine()
        
        context = ReasoningContext(
            user_profile={"fitness_level": "beginner", "available_time": 30},
            current_goals=[{"type": "weight_loss", "target": "5kg"}],
            historical_data=[],
            constraints=[{"type": "time", "value": "30min"}],
            preferences={"exercise_types": ["cardio"]}
        )
        
        reasoning_chain = await reasoning_engine.reason(
            user_id="test_user",
            query="我想减肥，应该怎么训练？",
            context=context
        )
        
        assert reasoning_chain.chain_id is not None
        assert reasoning_chain.user_id == "test_user"
        assert reasoning_chain.overall_confidence > 0
        assert len(reasoning_chain.steps) > 0
    
    @pytest.mark.asyncio
    async def test_causal_reasoning(self):
        """测试因果推理"""
        reasoning_engine = ComplexReasoningEngine()
        
        context = ReasoningContext(
            user_profile={"fitness_level": "intermediate"},
            current_goals=[{"type": "muscle_gain"}],
            historical_data=[],
            constraints=[],
            preferences={}
        )
        
        reasoning_chain = await reasoning_engine.reason(
            user_id="test_user",
            query="为什么我需要力量训练来增肌？",
            context=context
        )
        
        # 应该包含因果推理步骤
        causal_steps = [step for step in reasoning_chain.steps 
                       if step.reasoning_type == ReasoningType.CAUSAL]
        assert len(causal_steps) > 0
    
    @pytest.mark.asyncio
    async def test_temporal_reasoning(self):
        """测试时间推理"""
        reasoning_engine = ComplexReasoningEngine()
        
        context = ReasoningContext(
            user_profile={"available_time": 20},
            current_goals=[{"type": "general_fitness"}],
            historical_data=[],
            constraints=[],
            preferences={}
        )
        
        reasoning_chain = await reasoning_engine.reason(
            user_id="test_user",
            query="我每天只有20分钟，什么时候训练最好？",
            context=context
        )
        
        # 应该包含时间推理步骤
        temporal_steps = [step for step in reasoning_chain.steps 
                         if step.reasoning_type == ReasoningType.TEMPORAL]
        assert len(temporal_steps) > 0

class TestContextManager:
    """测试上下文管理器"""
    
    def test_context_manager_initialization(self):
        """测试上下文管理器初始化"""
        context_manager = ContextManager()
        assert context_manager is not None
        assert context_manager.max_context_window == 1000
        assert context_manager.context_retention_hours == 24
    
    @pytest.mark.asyncio
    async def test_create_conversation_context(self):
        """测试创建对话上下文"""
        context_manager = ContextManager()
        
        conversation_id = await context_manager.create_conversation_context("test_user")
        
        assert conversation_id is not None
        assert conversation_id in context_manager.conversations
        
        conversation = context_manager.conversations[conversation_id]
        assert conversation.user_id == "test_user"
        assert len(conversation.messages) == 0
    
    @pytest.mark.asyncio
    async def test_add_message_to_context(self):
        """测试添加消息到上下文"""
        context_manager = ContextManager()
        
        conversation_id = await context_manager.create_conversation_context("test_user")
        
        message = {
            "role": "user",
            "content": "我想了解健身计划",
            "intent": "fitness_planning"
        }
        
        success = await context_manager.add_message_to_context(conversation_id, message)
        
        assert success is True
        
        conversation = context_manager.conversations[conversation_id]
        assert len(conversation.messages) == 1
        assert "fitness_planning" in conversation.intent_history
    
    @pytest.mark.asyncio
    async def test_get_relevant_context(self):
        """测试获取相关上下文"""
        context_manager = ContextManager()
        
        conversation_id = await context_manager.create_conversation_context("test_user")
        
        # 添加一些消息
        await context_manager.add_message_to_context(conversation_id, {
            "role": "user",
            "content": "我想减肥",
            "intent": "weight_loss"
        })
        
        await context_manager.add_message_to_context(conversation_id, {
            "role": "assistant",
            "content": "建议您进行有氧运动",
            "intent": "exercise_recommendation"
        })
        
        # 获取相关上下文
        relevant_context = await context_manager.get_relevant_context(
            conversation_id, 
            "减肥运动建议",
            max_items=5
        )
        
        assert len(relevant_context) > 0
        assert any("减肥" in str(item.content) for item in relevant_context)
    
    @pytest.mark.asyncio
    async def test_conversation_summary(self):
        """测试对话摘要"""
        context_manager = ContextManager()
        
        conversation_id = await context_manager.create_conversation_context("test_user")
        
        # 添加消息
        await context_manager.add_message_to_context(conversation_id, {
            "role": "user",
            "content": "健身咨询",
            "intent": "fitness_consultation"
        })
        
        summary = await context_manager.get_conversation_summary(conversation_id)
        
        assert summary["conversation_id"] == conversation_id
        assert summary["user_id"] == "test_user"
        assert summary["message_count"] == 1
        assert "recent_messages" in summary

class TestIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_advanced_ai_pipeline(self):
        """测试完整高级AI流程"""
        # 初始化所有组件
        multimodal_processor = MultimodalProcessor()
        memory_system = LongTermMemorySystem()
        reasoning_engine = ComplexReasoningEngine()
        context_manager = ContextManager()
        
        user_id = "integration_test_user"
        
        # 1. 创建对话上下文
        conversation_id = await context_manager.create_conversation_context(user_id)
        assert conversation_id is not None
        
        # 2. 处理多模态输入
        text_input = MultimodalInput(
            input_id="integration_text",
            user_id=user_id,
            modality_type=ModalityType.TEXT,
            content="我想制定一个减肥计划",
            metadata={},
            timestamp=datetime.now()
        )
        
        multimodal_result = await multimodal_processor.process_multimodal_input(text_input)
        assert multimodal_result.confidence > 0
        
        # 3. 存储到长期记忆
        memory_id = await memory_system.store_memory(
            user_id=user_id,
            memory_type=MemoryType.GOAL,
            content={"goal": "减肥计划", "timestamp": datetime.now().isoformat()},
            importance=MemoryImportance.HIGH
        )
        assert memory_id is not None
        
        # 4. 执行复杂推理
        context = ReasoningContext(
            user_profile={"fitness_level": "beginner"},
            current_goals=[{"type": "weight_loss"}],
            historical_data=[],
            constraints=[],
            preferences={}
        )
        
        reasoning_result = await reasoning_engine.reason(
            user_id=user_id,
            query="制定减肥计划",
            context=context
        )
        assert reasoning_result.overall_confidence > 0
        
        # 5. 添加到对话上下文
        success = await context_manager.add_message_to_context(conversation_id, {
            "role": "user",
            "content": "我想制定一个减肥计划",
            "intent": "fitness_planning"
        })
        assert success is True
        
        # 6. 验证集成结果
        conversation_summary = await context_manager.get_conversation_summary(conversation_id)
        assert conversation_summary["message_count"] > 0
        
        memory_summary = await memory_system.get_user_memory_summary(user_id)
        assert memory_summary["total_memories"] > 0
        
        print("✅ 高级AI特性集成测试通过")

# 运行测试的主函数
async def run_advanced_ai_tests():
    """运行高级AI模块测试"""
    print("🧪 开始高级AI特性模块测试...")
    
    try:
        # 多模态处理器测试
        print("🎭 测试多模态处理器...")
        multimodal_test = TestMultimodalProcessor()
        multimodal_test.test_processor_initialization()
        await multimodal_test.test_text_processing()
        await multimodal_test.test_image_processing()
        await multimodal_test.test_multimodal_processing()
        print("✅ 多模态处理器测试通过")
        
        # 长期记忆系统测试
        print("🧠 测试长期记忆系统...")
        memory_test = TestLongTermMemorySystem()
        memory_test.test_memory_system_initialization()
        await memory_test.test_store_memory()
        await memory_test.test_retrieve_memories()
        await memory_test.test_memory_summary()
        print("✅ 长期记忆系统测试通过")
        
        # 复杂推理引擎测试
        print("🤔 测试复杂推理引擎...")
        reasoning_test = TestComplexReasoningEngine()
        reasoning_test.test_reasoning_engine_initialization()
        await reasoning_test.test_basic_reasoning()
        await reasoning_test.test_causal_reasoning()
        await reasoning_test.test_temporal_reasoning()
        print("✅ 复杂推理引擎测试通过")
        
        # 上下文管理器测试
        print("📝 测试上下文管理器...")
        context_test = TestContextManager()
        context_test.test_context_manager_initialization()
        await context_test.test_create_conversation_context()
        await context_test.test_add_message_to_context()
        await context_test.test_get_relevant_context()
        await context_test.test_conversation_summary()
        print("✅ 上下文管理器测试通过")
        
        # 集成测试
        print("🔗 测试完整集成...")
        integration_test = TestIntegration()
        await integration_test.test_full_advanced_ai_pipeline()
        print("✅ 集成测试通过")
        
        print("\n🎉 所有高级AI特性模块测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(run_advanced_ai_tests())
    exit(0 if result else 1)
