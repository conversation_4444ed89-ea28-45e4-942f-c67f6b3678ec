"""
性能优化模块测试

测试智能缓存、并发优化、资源监控和性能调优功能。
"""

import asyncio
import pytest
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append('/home/<USER>/backend')

from app.services.ai_assistant.intelligence.optimization.cache_manager import (
    IntelligentCacheManager, CacheStrategy
)
from app.services.ai_assistant.intelligence.optimization.concurrency_optimizer import (
    ConcurrencyOptimizer, TaskPriority
)
from app.services.ai_assistant.intelligence.optimization.resource_monitor import (
    ResourceMonitor, ResourceType, AlertLevel
)
from app.services.ai_assistant.intelligence.optimization.performance_tuner import (
    PerformanceTuner, PerformanceMetric
)

class TestIntelligentCacheManager:
    """测试智能缓存管理器"""

    def test_cache_manager_initialization(self):
        """测试缓存管理器初始化"""
        cache_manager = IntelligentCacheManager({
            "strategy": "lru",
            "max_size": 100,
            "default_ttl": 300
        })

        assert cache_manager.strategy == CacheStrategy.LRU
        assert cache_manager.max_size == 100
        assert cache_manager.default_ttl == 300

    @pytest.mark.asyncio
    async def test_cache_operations(self):
        """测试缓存基本操作"""
        cache_manager = IntelligentCacheManager()

        # 测试设置和获取
        success = await cache_manager.set("test_key", "test_value", ttl=60)
        assert success is True

        value = await cache_manager.get("test_key")
        assert value == "test_value"

        # 测试不存在的键
        value = await cache_manager.get("nonexistent_key")
        assert value is None

        # 测试删除
        success = await cache_manager.delete("test_key")
        assert success is True

        value = await cache_manager.get("test_key")
        assert value is None

    @pytest.mark.asyncio
    async def test_cache_expiration(self):
        """测试缓存过期"""
        cache_manager = IntelligentCacheManager()

        # 设置短TTL的缓存项
        await cache_manager.set("expire_key", "expire_value", ttl=0.1)

        # 立即获取应该成功
        value = await cache_manager.get("expire_key")
        assert value == "expire_value"

        # 等待过期
        await asyncio.sleep(0.2)

        # 再次获取应该返回None
        value = await cache_manager.get("expire_key")
        assert value is None

    @pytest.mark.asyncio
    async def test_cache_eviction(self):
        """测试缓存驱逐"""
        cache_manager = IntelligentCacheManager({"max_size": 3})

        # 添加超过最大容量的项
        await cache_manager.set("key1", "value1")
        await cache_manager.set("key2", "value2")
        await cache_manager.set("key3", "value3")
        await cache_manager.set("key4", "value4")  # 应该触发驱逐

        # 检查缓存大小
        stats = cache_manager.get_stats()
        assert stats["size"] <= 3

    def test_cache_stats(self):
        """测试缓存统计"""
        cache_manager = IntelligentCacheManager()
        stats = cache_manager.get_stats()

        assert "strategy" in stats
        assert "size" in stats
        assert "stats" in stats
        assert "hit_rate" in stats["stats"]

class TestConcurrencyOptimizer:
    """测试并发优化器"""

    def test_concurrency_optimizer_initialization(self):
        """测试并发优化器初始化"""
        optimizer = ConcurrencyOptimizer({
            "max_workers": 5,
            "queue_size": 100
        })

        assert optimizer.max_workers == 5
        assert optimizer.queue_size == 100

    @pytest.mark.asyncio
    async def test_task_submission(self):
        """测试任务提交"""
        optimizer = ConcurrencyOptimizer()
        await optimizer.start()

        try:
            # 定义测试函数
            def test_function(x, y):
                return x + y

            # 提交任务
            task_id = await optimizer.submit_task(
                test_function,
                1, 2,
                priority=TaskPriority.HIGH
            )

            assert task_id is not None

            # 等待任务完成
            await asyncio.sleep(1)

            # 检查任务状态
            status = await optimizer.get_task_status(task_id)
            assert status is not None

        finally:
            await optimizer.stop()

    @pytest.mark.asyncio
    async def test_async_task_submission(self):
        """测试异步任务提交"""
        optimizer = ConcurrencyOptimizer()
        await optimizer.start()

        try:
            # 定义异步测试函数
            async def async_test_function(x):
                await asyncio.sleep(0.1)
                return x * 2

            # 提交异步任务
            task_id = await optimizer.submit_task(
                async_test_function,
                5,
                priority=TaskPriority.NORMAL
            )

            assert task_id is not None

            # 等待任务完成
            await asyncio.sleep(0.5)

            # 检查任务状态
            status = await optimizer.get_task_status(task_id)
            assert status is not None

        finally:
            await optimizer.stop()

    @pytest.mark.asyncio
    async def test_task_cancellation(self):
        """测试任务取消"""
        optimizer = ConcurrencyOptimizer()
        await optimizer.start()

        try:
            def long_running_task():
                time.sleep(10)
                return "completed"

            # 提交长时间运行的任务
            task_id = await optimizer.submit_task(long_running_task)

            # 等待一小段时间确保任务开始执行
            await asyncio.sleep(0.1)

            # 尝试取消任务（可能已经开始执行，取消可能失败）
            success = await optimizer.cancel_task(task_id)
            # 不强制要求取消成功，因为任务可能已经开始执行
            print(f"任务取消结果: {success}")

        finally:
            await optimizer.stop()

    def test_concurrency_stats(self):
        """测试并发统计"""
        optimizer = ConcurrencyOptimizer()
        stats = optimizer.get_stats()

        assert "total_tasks" in stats
        assert "completed_tasks" in stats
        assert "active_tasks" in stats
        assert "success_rate" in stats

class TestResourceMonitor:
    """测试资源监控器"""

    def test_resource_monitor_initialization(self):
        """测试资源监控器初始化"""
        monitor = ResourceMonitor({
            "interval": 30,
            "cpu_warning": 75.0,
            "memory_warning": 85.0
        })

        assert monitor.monitoring_interval == 30
        assert monitor.thresholds[ResourceType.CPU].warning_threshold == 75.0
        assert monitor.thresholds[ResourceType.MEMORY].warning_threshold == 85.0

    @pytest.mark.asyncio
    async def test_resource_monitoring(self):
        """测试资源监控"""
        monitor = ResourceMonitor({"interval": 1})  # 1秒间隔用于测试

        await monitor.start()

        try:
            # 等待收集一些数据
            await asyncio.sleep(2)

            # 检查当前指标
            metrics = monitor.get_current_metrics()
            assert len(metrics) > 0
            assert "cpu" in metrics
            assert "memory" in metrics

        finally:
            await monitor.stop()

    def test_threshold_update(self):
        """测试阈值更新"""
        monitor = ResourceMonitor()

        # 更新CPU阈值
        success = monitor.update_threshold(
            ResourceType.CPU,
            warning_threshold=60.0,
            critical_threshold=80.0
        )

        assert success is True
        assert monitor.thresholds[ResourceType.CPU].warning_threshold == 60.0
        assert monitor.thresholds[ResourceType.CPU].critical_threshold == 80.0

    def test_alert_callback(self):
        """测试告警回调"""
        monitor = ResourceMonitor()

        # 记录回调调用
        callback_called = []

        def test_callback(alert):
            callback_called.append(alert)

        monitor.add_alert_callback(test_callback)

        # 验证回调已添加
        assert test_callback in monitor.alert_callbacks

        # 移除回调
        monitor.remove_alert_callback(test_callback)
        assert test_callback not in monitor.alert_callbacks

    def test_resource_summary(self):
        """测试资源摘要"""
        monitor = ResourceMonitor()
        summary = monitor.get_resource_summary()

        assert "current_metrics" in summary
        assert "active_alerts_count" in summary
        assert "monitoring_status" in summary
        assert "thresholds" in summary

class TestPerformanceTuner:
    """测试性能调优器"""

    def test_performance_tuner_initialization(self):
        """测试性能调优器初始化"""
        tuner = PerformanceTuner({
            "auto_tuning": True,
            "target_response_time_ms": 50,
            "target_throughput_rps": 2000
        })

        assert tuner.auto_tuning_enabled is True

        # 检查性能目标
        response_time_target = next(
            (target for target in tuner.performance_targets
             if target.metric == PerformanceMetric.RESPONSE_TIME),
            None
        )
        assert response_time_target is not None
        assert response_time_target.target_value == 50

    @pytest.mark.asyncio
    async def test_performance_data_collection(self):
        """测试性能数据收集"""
        tuner = PerformanceTuner()

        # 模拟组件
        class MockCacheManager:
            def get_stats(self):
                return {"stats": {"hit_rate": 0.75}}

        class MockConcurrencyOptimizer:
            def get_stats(self):
                return {
                    "throughput_per_second": 500,
                    "success_rate": 0.95,
                    "avg_execution_time": 0.05
                }

        tuner.set_components(
            cache_manager=MockCacheManager(),
            concurrency_optimizer=MockConcurrencyOptimizer()
        )

        # 收集性能数据
        await tuner._collect_performance_data()

        # 验证数据已收集
        assert len(tuner.performance_history) > 0

        latest_snapshot = tuner.performance_history[-1]
        assert PerformanceMetric.CACHE_HIT_RATE in latest_snapshot.metrics
        assert PerformanceMetric.THROUGHPUT in latest_snapshot.metrics

    @pytest.mark.asyncio
    async def test_performance_analysis(self):
        """测试性能分析"""
        tuner = PerformanceTuner()

        # 添加一些模拟性能数据
        from app.services.ai_assistant.intelligence.optimization.performance_tuner import PerformanceSnapshot

        # 创建性能不佳的快照
        poor_performance_snapshot = PerformanceSnapshot(
            timestamp=time.time(),
            metrics={
                PerformanceMetric.RESPONSE_TIME: 200,  # 超过目标100ms
                PerformanceMetric.THROUGHPUT: 500,     # 低于目标1000rps
                PerformanceMetric.CACHE_HIT_RATE: 0.5  # 低于目标0.8
            },
            system_state={}
        )

        # 添加多个相同的快照以建立趋势
        for _ in range(15):
            tuner.performance_history.append(poor_performance_snapshot)

        # 执行分析
        analysis_result = await tuner._analyze_performance()

        assert analysis_result["needs_optimization"] is True
        assert len(analysis_result["issues"]) > 0

    def test_performance_summary(self):
        """测试性能摘要"""
        tuner = PerformanceTuner()
        summary = tuner.get_performance_summary()

        # 如果没有数据，应该返回no_data状态
        if summary.get("status") == "no_data":
            assert "status" in summary
        else:
            assert "auto_tuning_enabled" in summary
            assert "target_status" in summary

class TestIntegration:
    """集成测试"""

    @pytest.mark.asyncio
    async def test_full_optimization_pipeline(self):
        """测试完整优化流程"""
        # 初始化所有组件
        cache_manager = IntelligentCacheManager()
        concurrency_optimizer = ConcurrencyOptimizer()
        resource_monitor = ResourceMonitor({"interval": 1})
        performance_tuner = PerformanceTuner()

        # 设置组件关联
        performance_tuner.set_components(
            cache_manager=cache_manager,
            concurrency_optimizer=concurrency_optimizer,
            resource_monitor=resource_monitor
        )

        try:
            # 启动所有组件
            await concurrency_optimizer.start()
            await resource_monitor.start()
            await performance_tuner.start()

            # 1. 测试缓存操作
            await cache_manager.set("integration_test", "test_value")
            value = await cache_manager.get("integration_test")
            assert value == "test_value"

            # 2. 测试任务提交
            def test_task():
                return "task_completed"

            task_id = await concurrency_optimizer.submit_task(test_task)
            assert task_id is not None

            # 3. 等待系统运行一段时间
            await asyncio.sleep(3)

            # 4. 检查各组件状态
            cache_stats = cache_manager.get_stats()
            assert cache_stats["stats"]["hits"] > 0

            concurrency_stats = concurrency_optimizer.get_stats()
            assert concurrency_stats["total_tasks"] > 0

            resource_metrics = resource_monitor.get_current_metrics()
            assert len(resource_metrics) > 0

            performance_summary = performance_tuner.get_performance_summary()
            assert "status" in performance_summary

            print("✅ 性能优化集成测试通过")

        finally:
            # 停止所有组件
            await performance_tuner.stop()
            await resource_monitor.stop()
            await concurrency_optimizer.stop()

# 运行测试的主函数
async def run_optimization_tests():
    """运行性能优化模块测试"""
    print("🧪 开始性能优化模块测试...")

    try:
        # 缓存管理器测试
        print("💾 测试智能缓存管理器...")
        cache_test = TestIntelligentCacheManager()
        cache_test.test_cache_manager_initialization()
        await cache_test.test_cache_operations()
        await cache_test.test_cache_expiration()
        await cache_test.test_cache_eviction()
        cache_test.test_cache_stats()
        print("✅ 智能缓存管理器测试通过")

        # 并发优化器测试
        print("⚡ 测试并发优化器...")
        concurrency_test = TestConcurrencyOptimizer()
        concurrency_test.test_concurrency_optimizer_initialization()
        await concurrency_test.test_task_submission()
        await concurrency_test.test_async_task_submission()
        await concurrency_test.test_task_cancellation()
        concurrency_test.test_concurrency_stats()
        print("✅ 并发优化器测试通过")

        # 资源监控器测试
        print("📊 测试资源监控器...")
        resource_test = TestResourceMonitor()
        resource_test.test_resource_monitor_initialization()
        await resource_test.test_resource_monitoring()
        resource_test.test_threshold_update()
        resource_test.test_alert_callback()
        resource_test.test_resource_summary()
        print("✅ 资源监控器测试通过")

        # 性能调优器测试
        print("🎯 测试性能调优器...")
        tuner_test = TestPerformanceTuner()
        tuner_test.test_performance_tuner_initialization()
        await tuner_test.test_performance_data_collection()
        await tuner_test.test_performance_analysis()
        tuner_test.test_performance_summary()
        print("✅ 性能调优器测试通过")

        # 集成测试
        print("🔗 测试完整集成...")
        integration_test = TestIntegration()
        await integration_test.test_full_optimization_pipeline()
        print("✅ 集成测试通过")

        print("\n🎉 所有性能优化模块测试通过！")
        return True

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(run_optimization_tests())
    exit(0 if result else 1)
