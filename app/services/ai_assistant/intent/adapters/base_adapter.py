"""
适配器基类

定义统一智能架构中适配器的基础接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class BaseAdapter(ABC):
    """适配器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.initialized = False
        logger.info(f"初始化适配器: {name}")
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化适配器"""
        pass
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据"""
        pass
    
    def is_ready(self) -> bool:
        """检查适配器是否就绪"""
        return self.initialized
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "adapter": self.name,
            "status": "healthy" if self.is_ready() else "not_ready",
            "initialized": self.initialized
        }
