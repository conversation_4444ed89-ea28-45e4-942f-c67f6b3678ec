"""
增强版意图识别器适配器

将传统系统的增强版意图识别器适配到新版AI助手系统中。
"""

import logging
from typing import Dict, Any, Optional
from app.services.ai_assistant.intent.recognition.recognizer import IntentRecognitionResult
from app.services.ai_assistant.intent.adapters.base_adapter import BaseAdapter

logger = logging.getLogger(__name__)

class EnhancedIntentRecognizerAdapter(BaseAdapter):
    """增强版意图识别器适配器"""
    
    def __init__(self, llm_proxy):
        super().__init__("EnhancedIntentRecognizerAdapter")
        self.llm_proxy = llm_proxy
        self.enhanced_recognizer = None
        
        # 意图映射表：传统系统 -> 新版系统
        self.intent_mapping = {
            "recommend_exercise": "exercise_action",
            "search_exercise": "exercise_action", 
            "daily_workout_plan": "training_plan",
            "weekly_workout_plan": "training_plan",
            "nutrition_advice": "diet_advice",
            "diet_suggestion": "diet_advice",
            "fitness_qa": "fitness_advice",
            "general_chat": "general_chat",
            "greeting": "general_chat",
            "help": "general_chat"
        }
        
        # 反向映射表：新版系统 -> 传统系统
        self.reverse_mapping = {v: k for k, v in self.intent_mapping.items()}
        
        logger.info("增强版意图识别器适配器初始化完成")
    
    async def initialize(self) -> bool:
        """初始化适配器"""
        try:
            # 动态导入传统系统的增强识别器
            from app.services.intent_recognition.enhanced_recognizer import EnhancedIntentRecognizer
            self.enhanced_recognizer = EnhancedIntentRecognizer(self.llm_proxy)
            
            self.initialized = True
            logger.info("增强版意图识别器适配器初始化成功")
            return True
            
        except ImportError as e:
            logger.error(f"无法导入增强版意图识别器: {str(e)}")
            self.initialized = False
            return False
        except Exception as e:
            logger.error(f"增强版意图识别器适配器初始化失败: {str(e)}")
            self.initialized = False
            return False
    
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理意图识别请求"""
        if not self.is_ready():
            logger.error("适配器未初始化")
            return self._create_error_result("适配器未初始化")
        
        message = input_data.get("message", "")
        context = input_data.get("context", {})
        
        if not message:
            logger.error("消息内容为空")
            return self._create_error_result("消息内容为空")
        
        try:
            # 调用增强版识别器
            intent_data = await self.enhanced_recognizer.recognize_intent(message, context)
            
            # 映射到新版系统格式
            mapped_intent = self.intent_mapping.get(intent_data.intent, intent_data.intent)
            
            result = {
                "intent_type": mapped_intent,
                "confidence": intent_data.confidence,
                "parameters": intent_data.parameters or {},
                "original_intent": intent_data.intent,
                "source": "enhanced_recognizer"
            }
            
            logger.info(f"增强版识别成功: {intent_data.intent} -> {mapped_intent} (置信度: {intent_data.confidence:.2f})")
            return result
            
        except Exception as e:
            logger.error(f"增强版意图识别失败: {str(e)}")
            return self._create_error_result(f"识别失败: {str(e)}")
    
    async def arecognize(self, message: str, context: Optional[Dict] = None) -> IntentRecognitionResult:
        """异步识别用户意图（兼容接口）"""
        input_data = {
            "message": message,
            "context": context or {}
        }
        
        result = await self.process(input_data)
        
        return IntentRecognitionResult(
            intent_type=result.get("intent_type", "unknown"),
            confidence=result.get("confidence", 0.0),
            parameters=result.get("parameters", {})
        )
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            "intent_type": "unknown",
            "confidence": 0.0,
            "parameters": {},
            "error": error_message,
            "source": "enhanced_recognizer_error"
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        base_health = await super().health_check()
        
        # 添加增强识别器特定的健康信息
        enhanced_health = {
            "enhanced_recognizer_available": self.enhanced_recognizer is not None,
            "intent_mappings_count": len(self.intent_mapping)
        }
        
        base_health.update(enhanced_health)
        return base_health
    
    def get_supported_intents(self) -> Dict[str, str]:
        """获取支持的意图映射"""
        return self.intent_mapping.copy()
    
    def add_intent_mapping(self, legacy_intent: str, new_intent: str):
        """添加意图映射"""
        self.intent_mapping[legacy_intent] = new_intent
        self.reverse_mapping[new_intent] = legacy_intent
        logger.info(f"添加意图映射: {legacy_intent} -> {new_intent}")
    
    def remove_intent_mapping(self, legacy_intent: str):
        """移除意图映射"""
        if legacy_intent in self.intent_mapping:
            new_intent = self.intent_mapping[legacy_intent]
            del self.intent_mapping[legacy_intent]
            if new_intent in self.reverse_mapping:
                del self.reverse_mapping[new_intent]
            logger.info(f"移除意图映射: {legacy_intent}")
    
    async def batch_recognize(self, messages: list, context: Optional[Dict] = None) -> list:
        """批量识别意图"""
        results = []
        
        for message in messages:
            try:
                result = await self.arecognize(message, context)
                results.append({
                    "message": message,
                    "intent_type": result.intent_type,
                    "confidence": result.confidence,
                    "parameters": result.parameters
                })
            except Exception as e:
                logger.error(f"批量识别失败 - 消息: {message}, 错误: {str(e)}")
                results.append({
                    "message": message,
                    "intent_type": "unknown",
                    "confidence": 0.0,
                    "parameters": {},
                    "error": str(e)
                })
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "adapter_name": self.name,
            "initialized": self.initialized,
            "intent_mappings_count": len(self.intent_mapping),
            "supported_legacy_intents": list(self.intent_mapping.keys()),
            "supported_new_intents": list(set(self.intent_mapping.values()))
        }
