"""
传统处理器工厂适配器

将传统系统的专业化意图处理器适配到新版AI助手系统中。
"""

import logging
from typing import Dict, Any, AsyncGenerator, List
from app.services.ai_assistant.intent.adapters.base_adapter import BaseAdapter

logger = logging.getLogger(__name__)

class LegacyHandlerFactoryAdapter(BaseAdapter):
    """传统处理器工厂适配器"""
    
    def __init__(self, db, llm_proxy):
        super().__init__("LegacyHandlerFactoryAdapter")
        self.db = db
        self.llm_proxy = llm_proxy
        self.legacy_handler = None
        
        # 支持的专业化意图
        self.specialized_intents = {
            "exercise_action", "training_plan", "diet_advice", "fitness_advice"
        }
        
        # 意图映射：新版系统 -> 传统系统
        self.intent_mapping = {
            "exercise_action": "recommend_exercise",
            "training_plan": "daily_workout_plan", 
            "diet_advice": "nutrition_advice",
            "fitness_advice": "fitness_qa"
        }
        
        logger.info("传统处理器工厂适配器初始化完成")
    
    async def initialize(self) -> bool:
        """初始化适配器"""
        try:
            # 动态导入传统系统的意图处理器
            from app.services.conversation.intent_handler import IntentHandler
            self.legacy_handler = IntentHandler(self.db, self.llm_proxy)
            
            self.initialized = True
            logger.info("传统处理器工厂适配器初始化成功")
            return True
            
        except ImportError as e:
            logger.error(f"无法导入传统意图处理器: {str(e)}")
            self.initialized = False
            return False
        except Exception as e:
            logger.error(f"传统处理器工厂适配器初始化失败: {str(e)}")
            self.initialized = False
            return False
    
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理意图请求"""
        if not self.is_ready():
            logger.error("适配器未初始化")
            return self._create_error_result("适配器未初始化")
        
        intent_type = input_data.get("intent_type", "")
        message = input_data.get("message", "")
        context = input_data.get("context", {})
        
        if not intent_type or not message:
            logger.error("意图类型或消息内容为空")
            return self._create_error_result("意图类型或消息内容为空")
        
        # 检查是否支持该意图
        if not await self.can_handle(intent_type):
            logger.warning(f"不支持的意图类型: {intent_type}")
            return self._create_error_result(f"不支持的意图类型: {intent_type}")
        
        try:
            result = await self.handle_intent(intent_type, message, context)
            return result
            
        except Exception as e:
            logger.error(f"处理意图失败: {str(e)}")
            return self._create_error_result(f"处理失败: {str(e)}")
    
    async def can_handle(self, intent_type: str) -> bool:
        """检查是否可以处理指定意图"""
        return intent_type in self.specialized_intents
    
    async def handle_intent(self, intent_type: str, message: str, context: Dict) -> Dict:
        """处理意图并返回结果"""
        try:
            # 构造传统系统数据格式
            legacy_intent = self._map_to_legacy_intent(intent_type)
            
            # 创建IntentData对象
            intent_data = self._create_intent_data(legacy_intent, context)
            
            # 准备用户数据和历史
            user_data = context.get("user_profile", {})
            history = context.get("messages", [])
            meta_info = {
                "conversation_id": context.get("conversation_id"),
                "user_id": context.get("user_id"),
                "timestamp": context.get("timestamp")
            }
            
            # 收集流式响应
            content_parts = []
            structured_data = None
            metadata = {}
            
            async for response in self.legacy_handler.handle_intent(
                intent_data, meta_info, user_data, history
            ):
                if response.get("type") == "message":
                    content_parts.append(response.get("content", ""))
                elif response.get("type") == "structured_data":
                    structured_data = response.get("data")
                elif response.get("type") == "metadata":
                    metadata.update(response.get("data", {}))
            
            # 构造结果
            result = {
                "content": "".join(content_parts),
                "intent_type": intent_type,
                "confidence": 0.9,  # 传统系统处理的置信度较高
                "source": "legacy_system",
                "legacy_intent": legacy_intent
            }
            
            if structured_data:
                result["structured_data"] = structured_data
            
            if metadata:
                result["metadata"] = metadata
            
            logger.info(f"传统系统处理意图成功: {intent_type} -> {legacy_intent}")
            return result
            
        except Exception as e:
            logger.error(f"传统系统处理意图失败: {str(e)}")
            return self._create_error_result(f"传统系统处理失败: {str(e)}")
    
    def _map_to_legacy_intent(self, new_intent: str) -> str:
        """映射新版意图到传统系统意图"""
        return self.intent_mapping.get(new_intent, new_intent)
    
    def _create_intent_data(self, intent: str, context: Dict):
        """创建IntentData对象"""
        try:
            from app.services.intent_recognizer import IntentData
            return IntentData(
                intent=intent,
                confidence=0.9,
                parameters=context.get("intent_parameters", {})
            )
        except ImportError:
            # 如果无法导入，创建简单的数据结构
            class SimpleIntentData:
                def __init__(self, intent, confidence, parameters):
                    self.intent = intent
                    self.confidence = confidence
                    self.parameters = parameters
            
            return SimpleIntentData(intent, 0.9, context.get("intent_parameters", {}))
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            "content": f"抱歉，处理您的请求时出现了问题: {error_message}",
            "intent_type": "unknown",
            "confidence": 0.1,
            "source": "legacy_system_error",
            "error": error_message
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        base_health = await super().health_check()
        
        # 添加传统处理器特定的健康信息
        legacy_health = {
            "legacy_handler_available": self.legacy_handler is not None,
            "specialized_intents_count": len(self.specialized_intents),
            "supported_intents": list(self.specialized_intents)
        }
        
        base_health.update(legacy_health)
        return base_health
    
    def get_supported_intents(self) -> set:
        """获取支持的意图类型"""
        return self.specialized_intents.copy()
    
    def add_specialized_intent(self, intent_type: str, legacy_intent: str = None):
        """添加专业化意图支持"""
        self.specialized_intents.add(intent_type)
        if legacy_intent:
            self.intent_mapping[intent_type] = legacy_intent
        logger.info(f"添加专业化意图支持: {intent_type}")
    
    def remove_specialized_intent(self, intent_type: str):
        """移除专业化意图支持"""
        self.specialized_intents.discard(intent_type)
        if intent_type in self.intent_mapping:
            del self.intent_mapping[intent_type]
        logger.info(f"移除专业化意图支持: {intent_type}")
    
    async def batch_handle(self, requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理意图请求"""
        results = []
        
        for request in requests:
            try:
                result = await self.process(request)
                results.append(result)
            except Exception as e:
                logger.error(f"批量处理失败 - 请求: {request}, 错误: {str(e)}")
                results.append(self._create_error_result(f"批量处理失败: {str(e)}"))
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "adapter_name": self.name,
            "initialized": self.initialized,
            "specialized_intents_count": len(self.specialized_intents),
            "intent_mappings_count": len(self.intent_mapping),
            "supported_intents": list(self.specialized_intents),
            "intent_mappings": self.intent_mapping.copy()
        }
    
    async def test_intent_handling(self, intent_type: str, test_message: str = "测试消息") -> Dict[str, Any]:
        """测试意图处理功能"""
        if not await self.can_handle(intent_type):
            return {"error": f"不支持的意图类型: {intent_type}"}
        
        test_context = {
            "conversation_id": "test_conv",
            "user_id": "test_user",
            "user_profile": {"name": "测试用户"},
            "messages": [],
            "intent_parameters": {}
        }
        
        try:
            result = await self.handle_intent(intent_type, test_message, test_context)
            return {
                "success": True,
                "intent_type": intent_type,
                "test_message": test_message,
                "result": result
            }
        except Exception as e:
            return {
                "success": False,
                "intent_type": intent_type,
                "test_message": test_message,
                "error": str(e)
            }
