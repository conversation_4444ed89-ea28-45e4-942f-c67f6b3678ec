"""
运动动作处理器模块

该模块提供处理与运动动作相关的用户意图的功能，包括运动动作查询、
动作指导和运动动作修正等。
"""

import logging
from typing import Dict, Any, Optional, List

from app.services.ai_assistant.intent.handlers.base import BaseIntentHandler
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever

logger = logging.getLogger(__name__)

class ExerciseActionHandler(BaseIntentHandler):
    """处理与运动动作相关的用户意图的处理器"""

    def __init__(
        self,
        llm_proxy: LLMProxy,
        knowledge_retriever: Optional[KnowledgeRetriever] = None
    ):
        """
        初始化运动动作处理器

        Args:
            llm_proxy: 语言模型代理，用于生成运动动作指导
            knowledge_retriever: 知识库检索器，用于检索相关运动知识
        """
        self.llm_proxy = llm_proxy
        self.knowledge_retriever = knowledge_retriever
        logger.info("运动动作处理器已初始化")

    def handle(self, intent: str, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理运动动作相关的意图

        Args:
            intent: 具体的运动动作意图类型
            user_message: 用户消息内容
            user_context: 用户上下文信息

        Returns:
            包含响应内容的字典
        """
        logger.info(f"处理运动动作意图: {intent}")

        if intent == "exercise_query":
            return self._handle_exercise_query(user_message, user_context)
        elif intent == "exercise_guidance":
            return self._handle_exercise_guidance(user_message, user_context)
        elif intent == "exercise_correction":
            return self._handle_exercise_correction(user_message, user_context)
        elif intent == "equipment_usage":
            return self._handle_equipment_usage(user_message, user_context)
        else:
            return self._handle_default(user_message, user_context)

    def _handle_exercise_query(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理运动动作查询"""
        logger.info("处理运动动作查询")

        # 提取用户查询的运动动作
        exercise_name = user_message.get("exercise_name", "")

        if not exercise_name:
            return {
                "response_type": "clarification",
                "content": "请告诉我您想了解哪个运动动作？"
            }

        # 尝试从知识库检索相关信息
        exercise_info = None
        if self.knowledge_retriever:
            exercise_info = self.knowledge_retriever.retrieve(exercise_name, "exercise")

        # 构建提示
        prompt = f"""
        提供关于"{exercise_name}"运动动作的详细信息:

        {f'参考以下知识: {exercise_info}' if exercise_info else ''}

        请包括以下内容:
        1. 动作简介及目标肌群
        2. 正确的动作执行步骤
        3. 常见错误和避免方法
        4. 适合的重量/阻力选择指南
        5. 对初学者的建议
        6. 进阶变体（如适用）
        7. 注意事项和安全警告
        """

        # 使用LLM生成运动动作信息
        import asyncio
        exercise_description = asyncio.run(self.llm_proxy.generate_text(prompt, temperature=0.3))

        return {
            "response_type": "exercise_query",
            "content": exercise_description,
            "suggested_next_intents": ["exercise_guidance", "equipment_usage"]
        }

    def _handle_exercise_guidance(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理运动动作指导"""
        logger.info("提供运动动作指导")

        # 提取用户需要指导的运动动作
        exercise_name = user_message.get("exercise_name", "")
        user_level = user_message.get("level", user_context.get("fitness_level", "beginner"))

        if not exercise_name:
            return {
                "response_type": "clarification",
                "content": "请告诉我您需要哪个运动动作的指导？"
            }

        # 尝试从知识库检索相关信息
        exercise_guidance = None
        if self.knowledge_retriever:
            exercise_guidance = self.knowledge_retriever.retrieve(f"{exercise_name} 指导 {user_level}", "exercise")

        # 构建提示
        prompt = f"""
        为{user_level}级别的用户提供"{exercise_name}"的详细指导:

        {f'参考以下知识: {exercise_guidance}' if exercise_guidance else ''}

        请以教练的语气，提供:
        1. 详细的预备姿势说明
        2. 动作分解，包括每个关节的正确位置和移动方向
        3. 呼吸节奏指导
        4. 应感受到的肌肉发力位置
        5. 常见错误的具体修正方法
        6. 适合的组数和重复次数建议
        7. 进步指标：如何判断可以增加难度
        """

        # 使用LLM生成运动指导
        import asyncio
        guidance_response = asyncio.run(self.llm_proxy.generate_text(prompt, temperature=0.4))

        return {
            "response_type": "exercise_guidance",
            "content": guidance_response,
            "suggested_next_intents": ["exercise_correction", "equipment_usage"]
        }

    def _handle_exercise_correction(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理运动动作修正"""
        logger.info("提供运动动作修正建议")

        # 提取用户描述的问题
        exercise_name = user_message.get("exercise_name", "")
        issue_description = user_message.get("issue", "")

        if not exercise_name or not issue_description:
            return {
                "response_type": "clarification",
                "content": "请告诉我您在哪个运动动作中遇到了什么问题，以便我提供修正建议。"
            }

        # 尝试从知识库检索相关信息
        correction_info = None
        if self.knowledge_retriever:
            correction_info = self.knowledge_retriever.retrieve(f"{exercise_name} {issue_description} 修正", "exercise")

        # 构建提示
        prompt = f"""
        为用户在"{exercise_name}"动作中遇到的以下问题提供修正建议:

        问题描述: {issue_description}

        {f'参考以下知识: {correction_info}' if correction_info else ''}

        请提供:
        1. 问题的可能原因分析
        2. 具体的修正步骤和建议
        3. 可能需要的辅助练习
        4. 如何判断问题是否已解决
        5. 预防问题再次发生的方法
        """

        # 使用LLM生成修正建议
        import asyncio
        correction_advice = asyncio.run(self.llm_proxy.generate_text(prompt, temperature=0.4))

        return {
            "response_type": "exercise_correction",
            "content": correction_advice,
            "suggested_next_intents": ["exercise_guidance", "equipment_usage"]
        }

    def _handle_equipment_usage(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理器械使用指导"""
        logger.info("提供器械使用指导")

        # 提取用户询问的器械
        equipment_name = user_message.get("equipment_name", "")

        if not equipment_name:
            return {
                "response_type": "clarification",
                "content": "请告诉我您想了解哪种健身器械的使用方法？"
            }

        # 尝试从知识库检索相关信息
        equipment_info = None
        if self.knowledge_retriever:
            equipment_info = self.knowledge_retriever.retrieve(f"{equipment_name} 使用方法", "equipment")

        # 构建提示
        prompt = f"""
        提供关于"{equipment_name}"器械的详细使用指南:

        {f'参考以下知识: {equipment_info}' if equipment_info else ''}

        请包括:
        1. 器械简介和主要锻炼肌群
        2. 安全设置和调整方法
        3. 正确的使用姿势和技巧
        4. 常见错误和避免方法
        5. 初学者适合的重量/阻力范围
        6. 可以在该器械上执行的不同动作变化
        7. 安全注意事项
        """

        # 使用LLM生成器械使用指南
        import asyncio
        equipment_guide = asyncio.run(self.llm_proxy.generate_text(prompt, temperature=0.3))

        return {
            "response_type": "equipment_usage",
            "content": equipment_guide,
            "suggested_next_intents": ["exercise_query", "exercise_guidance"]
        }

    def _handle_default(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理未识别的运动动作相关意图"""
        logger.info("处理默认运动动作相关查询")

        query = user_message.get("text", "")

        # 尝试从知识库检索相关信息
        relevant_knowledge = None
        if self.knowledge_retriever:
            relevant_knowledge = self.knowledge_retriever.retrieve(query, "exercise")

        # 构建提示
        prompt = f"""
        用户查询: {query}

        {f'参考以下相关知识: {relevant_knowledge}' if relevant_knowledge else ''}

        这是一个与运动动作相关的查询。请提供专业、安全和有帮助的回答。
        在回答中强调正确的动作技术和安全注意事项。
        """

        # 使用LLM生成回答
        import asyncio
        response = asyncio.run(self.llm_proxy.generate_text(prompt, temperature=0.4))

        return {
            "response_type": "general_exercise_advice",
            "content": response,
            "suggested_next_intents": ["exercise_query", "exercise_guidance", "exercise_correction"]
        }