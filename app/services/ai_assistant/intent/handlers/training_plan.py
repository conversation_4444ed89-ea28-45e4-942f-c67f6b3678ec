"""
训练计划处理器模块

该模块提供处理与训练计划相关的用户意图的功能，包括生成个性化训练计划、
修改现有计划和提供训练建议等。
"""

import logging
from typing import Dict, Any, Optional, List

from app.services.ai_assistant.intent.handlers.base import BaseIntentHandler
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever

logger = logging.getLogger(__name__)

class TrainingPlanHandler(BaseIntentHandler):
    """处理与训练计划相关的用户意图的处理器"""

    def __init__(
        self,
        llm_proxy: LLMProxy,
        knowledge_retriever: Optional[KnowledgeRetriever] = None
    ):
        """
        初始化训练计划处理器

        Args:
            llm_proxy: 语言模型代理，用于生成训练计划
            knowledge_retriever: 知识库检索器，用于检索相关训练知识
        """
        self.llm_proxy = llm_proxy
        self.knowledge_retriever = knowledge_retriever
        logger.info("训练计划处理器已初始化")

    def handle(self, intent: str, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理训练计划相关的意图

        Args:
            intent: 具体的训练计划意图类型
            user_message: 用户消息内容
            user_context: 用户上下文信息

        Returns:
            包含响应内容的字典
        """
        logger.info(f"处理训练计划意图: {intent}")

        if intent == "generate_plan":
            return self._handle_generate_plan(user_message, user_context)
        elif intent == "modify_plan":
            return self._handle_modify_plan(user_message, user_context)
        elif intent == "training_advice":
            return self._handle_training_advice(user_message, user_context)
        elif intent == "plan_progress":
            return self._handle_plan_progress(user_message, user_context)
        else:
            return self._handle_default(user_message, user_context)

    def _handle_generate_plan(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理生成训练计划请求"""
        logger.info("生成个性化训练计划")

        # 提取用户的训练目标和偏好
        goal = user_message.get("goal", "")
        duration = user_message.get("duration", "4周")
        frequency = user_message.get("frequency", user_context.get("workout_frequency", "每周3次"))
        equipment = user_message.get("equipment", [])

        # 获取用户的基本信息
        fitness_level = user_context.get("fitness_level", "beginner")
        age = user_context.get("age", 0)
        gender = user_context.get("gender", "")
        health_conditions = user_context.get("health_conditions", [])

        if not goal:
            return {
                "response_type": "clarification",
                "content": "请告诉我您的训练目标是什么？例如增肌、减脂、提高耐力或改善整体健康等。"
            }

        # 构建提示
        prompt = f"""
        生成一个个性化的{duration}训练计划，基于以下信息:
        - 训练目标: {goal}
        - 训练频率: {frequency}
        - 可用器材: {', '.join(equipment) if equipment else '无特定器材限制'}
        - 健身水平: {fitness_level}
        - 年龄: {age if age else '未知'}岁
        - 性别: {gender if gender else '未知'}
        - 健康状况: {', '.join(health_conditions) if health_conditions else '无特殊健康问题'}

        请提供:
        1. 整体计划概述，包括训练分割和循环结构
        2. 每周的具体训练安排，包含以下内容:
           - 每次训练的具体动作、组数和重复次数
           - 休息时间建议
           - 训练强度指导
        3. 进阶建议，说明如何随着能力提高调整计划
        4. 热身和拉伸建议
        5. 训练注意事项
        """

        # 使用LLM生成训练计划
        import asyncio
        training_plan = asyncio.run(self.llm_proxy.generate_text(prompt, temperature=0.7))

        return {
            "response_type": "training_plan",
            "content": training_plan,
            "suggested_next_intents": ["modify_plan", "training_advice"]
        }

    def _handle_modify_plan(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理修改训练计划请求"""
        logger.info("修改现有训练计划")

        # 提取用户的修改请求
        current_plan = user_message.get("current_plan", user_context.get("current_training_plan", ""))
        modification_request = user_message.get("modification", "")
        reason = user_message.get("reason", "")

        if not current_plan or not modification_request:
            return {
                "response_type": "clarification",
                "content": "请提供您当前的训练计划以及您想如何修改它。例如，您可能想增加训练强度、更换特定动作或调整训练频率。"
            }

        # 构建提示
        prompt = f"""
        修改以下训练计划:

        当前计划:
        {current_plan}

        修改请求:
        {modification_request}

        修改原因:
        {reason if reason else '未提供'}

        请提供:
        1. 修改后的完整训练计划
        2. 对所做修改的解释和理由
        3. 实施修改后计划的建议
        4. 可能的替代方案(如适用)
        """

        # 使用LLM生成修改后的训练计划
        modified_plan = self.llm_proxy.generate(prompt, temperature=0.6)

        return {
            "response_type": "modified_training_plan",
            "content": modified_plan,
            "suggested_next_intents": ["training_advice", "plan_progress"]
        }

    def _handle_training_advice(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理训练建议请求"""
        logger.info("提供训练建议")

        # 提取用户的问题
        question = user_message.get("question", "")
        goal = user_message.get("goal", user_context.get("training_goal", ""))
        current_plan = user_context.get("current_training_plan", "")

        if not question:
            return {
                "response_type": "clarification",
                "content": "请详细描述您的训练相关问题，以便我能提供针对性的建议。"
            }

        # 尝试从知识库检索相关信息
        training_knowledge = None
        if self.knowledge_retriever:
            training_knowledge = self.knowledge_retriever.retrieve(question, "training")

        # 构建提示
        prompt = f"""
        回答以下训练相关问题:
        {question}

        {f'训练目标: {goal}' if goal else ''}
        {f'当前训练计划: {current_plan}' if current_plan else ''}

        {f'参考以下知识: {training_knowledge}' if training_knowledge else ''}

        提供科学、实用的训练建议，考虑用户的具体情况和目标。
        建议应包括:
        1. 对问题的直接回答
        2. 科学原理解释
        3. 实用的实施建议
        4. 安全注意事项(如适用)
        """

        # 使用LLM生成训练建议
        import asyncio
        advice = asyncio.run(self.llm_proxy.generate_text(prompt, temperature=0.5))

        return {
            "response_type": "training_advice",
            "content": advice,
            "suggested_next_intents": ["generate_plan", "modify_plan"]
        }

    def _handle_plan_progress(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理训练计划进度跟踪"""
        logger.info("评估训练计划进度")

        # 提取用户的进度信息
        current_plan = user_message.get("current_plan", user_context.get("current_training_plan", ""))
        progress_details = user_message.get("progress", "")
        challenges = user_message.get("challenges", "")
        duration_followed = user_message.get("duration_followed", "")

        if not current_plan or not progress_details:
            return {
                "response_type": "clarification",
                "content": "请提供您当前的训练计划和您的进展情况，包括您已经遵循该计划多长时间、已经取得的成果以及遇到的任何挑战。"
            }

        # 构建提示
        prompt = f"""
        评估以下训练计划的进度:

        当前计划:
        {current_plan}

        进度详情:
        {progress_details}

        遇到的挑战:
        {challenges if challenges else '未提及'}

        已遵循计划时间:
        {duration_followed if duration_followed else '未提及'}

        请提供:
        1. 进度评估和成就肯定
        2. 解决挑战的具体建议
        3. 针对当前阶段的调整建议
        4. 保持动力的策略
        5. 下一步目标设定
        """

        # 使用LLM生成进度评估
        import asyncio
        progress_assessment = asyncio.run(self.llm_proxy.generate_text(prompt, temperature=0.5))

        return {
            "response_type": "plan_progress",
            "content": progress_assessment,
            "suggested_next_intents": ["modify_plan", "training_advice"]
        }

    def _handle_default(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理未识别的训练计划相关意图"""
        logger.info("处理默认训练计划相关查询")

        query = user_message.get("text", "")

        # 尝试从知识库检索相关信息
        relevant_knowledge = None
        if self.knowledge_retriever:
            relevant_knowledge = self.knowledge_retriever.retrieve(query, "training")

        # 构建提示
        prompt = f"""
        用户查询: {query}

        {f'参考以下相关知识: {relevant_knowledge}' if relevant_knowledge else ''}

        这是一个与训练计划相关的查询。请提供专业、科学和有帮助的回答。
        在回答中考虑训练原则、循序渐进和个性化的重要性。
        """

        # 使用LLM生成回答
        response = self.llm_proxy.generate(prompt, temperature=0.5)

        return {
            "response_type": "general_training_advice",
            "content": response,
            "suggested_next_intents": ["generate_plan", "training_advice", "modify_plan"]
        }