"""
状态转换适配器

确保UnifiedFitnessState与现有ConversationState的兼容性，
提供双向转换和状态同步功能。
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils

logger = logging.getLogger(__name__)

class StateAdapter:
    """状态转换适配器"""

    @staticmethod
    def to_unified_state(
        conversation_state: Optional[Dict[str, Any]] = None,
        message: Optional[str] = None,
        user_info: Optional[Dict[str, Any]] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> UnifiedFitnessState:
        """
        将现有的对话状态转换为UnifiedFitnessState

        Args:
            conversation_state: 现有的对话状态字典
            message: 用户消息
            user_info: 用户信息
            conversation_id: 对话ID
            session_id: 会话ID

        Returns:
            UnifiedFitnessState实例
        """
        try:
            # 创建基础状态
            if conversation_state:
                state = StateUtils.from_conversation_state(conversation_state)
            else:
                state = StateUtils.create_initial_state(
                    conversation_id=conversation_id or "unknown",
                    user_id=user_info.get("user_id", "unknown") if user_info else "unknown",
                    session_id=session_id or "unknown",
                    message=message or ""
                )

            # 更新基础信息
            if conversation_id:
                state["conversation_id"] = conversation_id
            if session_id:
                state["session_id"] = session_id
            if user_info:
                # 正确处理用户信息
                state["user_id"] = user_info.get("user_id", state["user_id"])

                # 只更新用户档案字段，不包括user_id等系统字段
                user_profile = user_info.get("user_profile", {})
                if user_profile:
                    if "user_profile" not in state:
                        state["user_profile"] = {}
                    state["user_profile"].update(user_profile)

                # 如果user_info直接包含档案字段（向后兼容）
                profile_fields = ["gender", "age", "height", "weight", "fitness_goal", "fitness_level", "name", "activity_level"]
                for field in profile_fields:
                    if field in user_info:
                        if "user_profile" not in state:
                            state["user_profile"] = {}
                        state["user_profile"][field] = user_info[field]

            # 设置处理系统标识
            state["processing_system"] = "langgraph"
            StateUtils.add_processing_step(state, "state_adapter_conversion")

            logger.info(f"状态转换完成: {state['conversation_id']}")
            return state

        except Exception as e:
            logger.error(f"状态转换失败: {str(e)}")
            # 返回默认状态
            default_state = StateUtils.create_initial_state(
                conversation_id=conversation_id or "unknown",
                user_id=user_info.get("user_id", "unknown") if user_info else "unknown",
                session_id=session_id or "unknown",
                message=message or ""
            )

            StateUtils.set_error(default_state, f"状态转换失败: {str(e)}")
            return default_state

    @staticmethod
    def from_unified_state(state: UnifiedFitnessState) -> Dict[str, Any]:
        """
        将UnifiedFitnessState转换为现有系统兼容的格式

        Args:
            state: UnifiedFitnessState实例

        Returns:
            兼容的状态字典
        """
        try:
            # 基础转换
            result = StateUtils.to_conversation_state_dict(state)

            # 添加LangGraph特定信息
            result.update({
                "processing_system": state["processing_system"],
                "processing_path": state["processing_path"],
                "node_execution_times": state["node_execution_times"],
                "total_processing_time": state["total_processing_time"],
                "graph_execution_id": state["graph_execution_id"],
                "parallel_results_count": len(state["parallel_results"]),
                "error_count": state["error_count"],
                "retry_count": state["retry_count"]
            })

            # 提取最新的AI响应
            if state["messages"]:
                for msg in reversed(state["messages"]):
                    if hasattr(msg, 'type') and msg.type == "ai":
                        result["response_content"] = msg.content
                        break

            # 如果没有从消息中找到响应，使用状态中的响应内容
            if not result.get("response_content") and state["response_content"]:
                result["response_content"] = state["response_content"]

            logger.debug(f"状态转换完成: {state['conversation_id']}")
            return result

        except Exception as e:
            logger.error(f"状态转换失败: {str(e)}")
            return {
                "conversation_id": state.get("conversation_id", "unknown"),
                "user_id": state.get("user_id", "unknown"),
                "current_state": "error",
                "response_content": f"状态转换失败: {str(e)}",
                "success": False,
                "error": str(e)
            }

    @staticmethod
    def sync_conversation_history(
        state: UnifiedFitnessState,
        conversation_history: List[Dict[str, Any]]
    ) -> UnifiedFitnessState:
        """
        同步对话历史到状态中

        Args:
            state: UnifiedFitnessState实例
            conversation_history: 对话历史列表

        Returns:
            更新后的状态
        """
        try:
            # 更新对话历史
            state.conversation_history = conversation_history

            # 转换为LangChain消息格式
            messages = []
            for entry in conversation_history:
                if entry.get("role") == "user":
                    messages.append(HumanMessage(content=entry.get("content", "")))
                elif entry.get("role") == "assistant":
                    messages.append(AIMessage(content=entry.get("content", "")))
                elif entry.get("role") == "system":
                    messages.append(SystemMessage(content=entry.get("content", "")))

            # 更新消息历史（保留现有消息）
            existing_messages = state.messages or []
            state.messages = existing_messages + messages

            logger.debug(f"同步对话历史完成，消息数: {len(state.messages)}")
            return state

        except Exception as e:
            logger.error(f"同步对话历史失败: {str(e)}")
            state.set_error(f"同步对话历史失败: {str(e)}")
            return state

    @staticmethod
    def extract_response_content(state: UnifiedFitnessState) -> str:
        """
        从状态中提取响应内容

        Args:
            state: UnifiedFitnessState实例

        Returns:
            响应内容字符串
        """
        try:
            # 优先从选中的结果中获取
            selected_result = state.get('selected_result')
            if selected_result:
                return selected_result.get("content", "")

            # 从响应内容字段获取
            response_content = state.get('response_content')
            if response_content:
                return response_content

            # 从消息历史中获取最新的AI响应
            messages = state.get('messages', [])
            if messages:
                for msg in reversed(messages):
                    if hasattr(msg, 'type') and msg.type == "ai":
                        return msg.content

            # 从并行结果中获取（如果有）
            parallel_results = state.get('parallel_results', [])
            if parallel_results:
                best_result = max(
                    parallel_results,
                    key=lambda x: x.get("confidence", 0.0)
                )
                return best_result.get("content", "")

            return ""

        except Exception as e:
            logger.error(f"提取响应内容失败: {str(e)}")
            return f"提取响应失败: {str(e)}"

    @staticmethod
    def create_api_response(state: UnifiedFitnessState) -> Dict[str, Any]:
        """
        创建API响应格式

        Args:
            state: UnifiedFitnessState实例

        Returns:
            API响应字典
        """
        try:
            # state现在是字典，直接使用字典访问
            response_content = StateAdapter.extract_response_content(state)

            api_response = {
                "success": state.get('error_count', 0) == 0,
                "response": response_content,
                "intent_type": state.get('intent', ''),
                "confidence": state.get('confidence', 0.0),
                "current_state": state.get('current_state_name', 'unknown'),
                "conversation_id": state.get('conversation_id', ''),
                "session_id": state.get('session_id', ''),
                "processing_info": {
                    "system": state.get('processing_system', ''),
                    "path": state.get('processing_path', []),
                    "total_time": state.get('total_processing_time', 0.0),
                    "node_times": state.get('node_execution_times', {}),
                    "graph_execution_id": state.get('graph_execution_id', '')
                }
            }

            # 添加结构化数据（如果有）
            structured_data = state.get('structured_data', {})
            if structured_data:
                api_response["structured_data"] = structured_data

            # 添加选中结果的元数据（如果有）
            selected_result = state.get('selected_result', None)
            if selected_result:
                api_response["selected_result_info"] = {
                    "source": selected_result.get("source", "unknown"),
                    "confidence": selected_result.get("confidence", 0.0),
                    "selection_reason": selected_result.get("selection_reason", "")
                }

            # 添加并行处理信息（如果有）
            parallel_results = state.get('parallel_results', [])
            if parallel_results:
                api_response["parallel_info"] = {
                    "results_count": len(parallel_results),
                    "sources": [r.get("source", "unknown") for r in parallel_results]
                }

            # 添加错误信息（如果有）
            last_error = state.get('last_error', None)
            if last_error:
                api_response["error"] = last_error
                api_response["error_count"] = state.get('error_count', 0)
                api_response["retry_count"] = state.get('retry_count', 0)

            return api_response

        except Exception as e:
            logger.error(f"创建API响应失败: {str(e)}")
            return {
                "success": False,
                "response": f"创建响应失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "current_state": "error",
                "conversation_id": state.get('conversation_id', '') if isinstance(state, dict) else '',
                "error": str(e)
            }

    @staticmethod
    def validate_state_transition(
        from_state: UnifiedFitnessState,
        to_state: UnifiedFitnessState
    ) -> bool:
        """
        验证状态转换的有效性

        Args:
            from_state: 源状态
            to_state: 目标状态

        Returns:
            转换是否有效
        """
        try:
            # 检查基础字段一致性
            if from_state.conversation_id != to_state.conversation_id:
                logger.warning("conversation_id不一致")
                return False

            if from_state.user_id != to_state.user_id:
                logger.warning("user_id不一致")
                return False

            # 检查时间戳递增
            if (from_state.timestamp and to_state.timestamp and
                to_state.timestamp < from_state.timestamp):
                logger.warning("时间戳倒退")
                return False

            # 检查错误计数不减少
            if to_state.error_count < from_state.error_count:
                logger.warning("错误计数减少")
                return False

            return True

        except Exception as e:
            logger.error(f"状态转换验证失败: {str(e)}")
            return False

    @staticmethod
    def merge_states(
        primary_state: UnifiedFitnessState,
        secondary_state: UnifiedFitnessState
    ) -> UnifiedFitnessState:
        """
        合并两个状态，以主状态为准，补充次要状态的信息

        Args:
            primary_state: 主状态
            secondary_state: 次要状态

        Returns:
            合并后的状态
        """
        try:
            merged_state = primary_state

            # 合并用户信息
            if secondary_state.user_profile:
                merged_state.user_profile.update(secondary_state.user_profile)

            # 合并训练参数
            if secondary_state.training_params:
                merged_state.training_params.update(secondary_state.training_params)

            # 合并结构化数据
            if secondary_state.structured_data:
                merged_state.structured_data.update(secondary_state.structured_data)

            # 合并处理路径
            for step in secondary_state.processing_path:
                if step not in merged_state.processing_path:
                    merged_state.processing_path.append(step)

            # 合并节点执行时间
            merged_state.node_execution_times.update(secondary_state.node_execution_times)

            # 合并并行结果
            for result in secondary_state.parallel_results:
                if result not in merged_state.parallel_results:
                    merged_state.parallel_results.append(result)

            logger.debug("状态合并完成")
            return merged_state

        except Exception as e:
            logger.error(f"状态合并失败: {str(e)}")
            primary_state.set_error(f"状态合并失败: {str(e)}")
            return primary_state
