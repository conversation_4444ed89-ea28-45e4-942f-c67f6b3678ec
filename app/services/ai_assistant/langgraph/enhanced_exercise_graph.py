"""
增强版运动动作处理图模块

实现专门的LangGraph节点来处理运动动作意图，包括：
1. 参数收集节点 - 收集训练参数（body_part, scenario, equipment等）
2. 用户信息验证节点 - 检查并收集缺失的用户档案信息
3. 数据库查询节点 - 调用get_candidate_exercises从exercises表检索动作
4. AI筛选节点 - 使用personalized_filtering筛选出最匹配的5个动作
5. 响应生成节点 - 生成结构化的动作推荐
"""

import logging
import time
import json
from typing import Dict, Any, Literal, List, Optional, Tuple
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
from app.services.ai_assistant.langgraph.adapters.state_adapter import StateAdapter
from app.services.user_profile_manager import UserProfileManager

logger = logging.getLogger(__name__)

# ===== 节点实现 =====

async def exercise_intent_router_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """运动动作意图路由节点"""
    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 运动动作关键词检测
        exercise_keywords = [
            "胸肌", "腹肌", "背肌", "腿部", "肩膀", "手臂", "臀部",
            "怎么练", "如何训练", "动作", "锻炼", "训练方法",
            "卧推", "深蹲", "硬拉", "俯卧撑", "仰卧起坐", "引体向上"
        ]

        # 检查是否是运动动作相关查询
        is_exercise_query = any(keyword in user_message for keyword in exercise_keywords)

        if is_exercise_query:
            # 初始化参数收集状态
            state["flow_state"] = {
                "stage": "parameter_collection",
                "collected_params": {},
                "missing_user_info": [],
                "needs_user_info": True,
                "needs_training_params": True
            }

            # 提取初始参数
            initial_params = {}

            # 简单的关键词提取（更可靠）
            if "胸肌" in user_message:
                initial_params["body_part"] = "胸部"
            elif "腹肌" in user_message:
                initial_params["body_part"] = "腹部"
            elif "背" in user_message:
                initial_params["body_part"] = "背部"
            elif "腿" in user_message:
                initial_params["body_part"] = "腿部"
            elif "肩" in user_message:
                initial_params["body_part"] = "肩部"
            elif "手臂" in user_message:
                initial_params["body_part"] = "手臂"

            # 尝试使用参数提取器作为补充
            try:
                from app.services.conversation.parameter_extractor import ParameterExtractor
                extracted_params = await ParameterExtractor.extract_all_parameters(None, user_message)
                logger.info(f"参数提取器结果: {extracted_params}")

                # 转换body_parts列表为body_part字符串
                if "body_parts" in extracted_params and extracted_params["body_parts"]:
                    if not initial_params.get("body_part"):
                        initial_params["body_part"] = extracted_params["body_parts"][0]

                # 合并其他有用的参数
                if "training_goal" in extracted_params and extracted_params["training_goal"]:
                    initial_params["training_goal"] = extracted_params["training_goal"]

            except Exception as param_error:
                logger.warning(f"参数提取器失败: {str(param_error)}")

            state["flow_state"]["collected_params"] = initial_params
            logger.info(f"最终收集的初始参数: {initial_params}")

            route = "user_info_verification"
            confidence = 0.9
            reasoning = "检测到运动动作查询，开始参数收集流程"
        else:
            route = "general_response"
            confidence = 0.7
            reasoning = "非运动动作查询，使用通用响应"

        # 更新状态
        state["current_node"] = "exercise_intent_router"
        state["routing_decision"] = {
            "route": route,
            "confidence": confidence,
            "reasoning": reasoning
        }
        state["intent"] = "exercise_action" if is_exercise_query else "general_chat"

        logger.info(f"运动意图路由: {route} (置信度: {confidence:.2f})")
        return state

    except Exception as e:
        logger.error(f"运动意图路由失败: {str(e)}")
        state["current_node"] = "exercise_intent_router"
        state["routing_decision"] = {
            "route": "general_response",
            "confidence": 0.5,
            "reasoning": f"路由失败: {str(e)}"
        }
        StateUtils.set_error(state, f"运动意图路由失败: {str(e)}")
        return state

async def user_info_verification_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """用户信息验证节点"""
    try:
        # 获取当前用户信息
        user_profile = state.get("user_profile", {})

        # 检查缺失的必要信息
        missing_fields = UserProfileManager.get_missing_fields(user_profile)

        logger.info(f"用户档案: {user_profile}")
        logger.info(f"缺失字段: {missing_fields}")

        if missing_fields:
            # 需要收集用户信息
            next_field = missing_fields[0]  # 获取第一个缺失字段

            # 更新流程状态
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["missing_user_info"] = missing_fields
            state["flow_state"]["current_collecting_field"] = next_field
            state["flow_state"]["stage"] = "collecting_user_info"

            # 生成询问提示
            field_prompt = UserProfileManager.get_field_prompt(next_field)
            response_content = f"为了给您提供更个性化的训练建议，我需要了解一些基本信息。\n\n{field_prompt}"

            # 更新状态
            state["current_node"] = "user_info_verification"
            state["response_content"] = response_content
            state["confidence"] = 0.9

            # 添加AI响应
            ai_message = AIMessage(content=response_content)
            if "messages" not in state:
                state["messages"] = []
            state["messages"].append(ai_message)

            logger.info(f"需要收集用户信息: {next_field}")
            return state
        else:
            # 用户信息完整，继续参数收集
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["needs_user_info"] = False
            state["flow_state"]["stage"] = "parameter_collection"

            logger.info("用户信息完整，继续参数收集")
            return state

    except Exception as e:
        logger.error(f"用户信息验证失败: {str(e)}")
        state["current_node"] = "user_info_verification"
        state["response_content"] = "抱歉，用户信息验证过程中出现问题，我将使用默认设置为您提供建议。"
        StateUtils.set_error(state, f"用户信息验证失败: {str(e)}")

        # 跳过用户信息收集，继续流程
        if "flow_state" not in state:
            state["flow_state"] = {}
        state["flow_state"]["needs_user_info"] = False
        state["flow_state"]["stage"] = "parameter_collection"
        return state

async def parameter_collection_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """参数收集节点"""
    try:
        flow_state = state.get("flow_state", {})
        collected_params = flow_state.get("collected_params", {})

        logger.info(f"参数收集节点 - 当前收集的参数: {collected_params}")

        # 检查必要的训练参数
        required_params = ["body_part", "scenario"]
        missing_params = []

        for param in required_params:
            if param not in collected_params or not collected_params[param]:
                missing_params.append(param)

        logger.info(f"缺失的参数: {missing_params}")

        if missing_params:
            # 需要收集参数
            next_param = missing_params[0]

            # 更新流程状态
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["current_collecting_param"] = next_param
            state["flow_state"]["stage"] = "collecting_training_params"

            # 生成询问提示
            if next_param == "scenario":
                response_content = "请告诉我您想在哪里锻炼？\n\n🏠 居家训练\n🏋️ 健身房训练\n🏃 户外运动"
            elif next_param == "body_part":
                response_content = "请告诉我您想训练哪个部位？\n\n💪 胸部\n🏃 腿部\n🔙 背部\n🤲 手臂\n🏋️ 肩部\n💯 腹部"
            elif next_param == "equipment":
                response_content = "您希望使用什么器械？\n\n💪 徒手训练\n🏋️ 哑铃/杠铃\n🔧 健身器械\n🎾 其他器材"
            else:
                response_content = f"请提供您的{next_param}信息。"

            # 更新状态
            state["current_node"] = "parameter_collection"
            state["response_content"] = response_content
            state["confidence"] = 0.9

            # 添加AI响应
            ai_message = AIMessage(content=response_content)
            if "messages" not in state:
                state["messages"] = []
            state["messages"].append(ai_message)

            logger.info(f"需要收集训练参数: {next_param}")
            return state
        else:
            # 参数收集完成，进入数据库查询
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["needs_training_params"] = False
            state["flow_state"]["stage"] = "database_query"
            state["training_params"] = collected_params

            logger.info("参数收集完成，进入数据库查询")
            return state

    except Exception as e:
        logger.error(f"参数收集失败: {str(e)}")
        state["current_node"] = "parameter_collection"
        state["response_content"] = "抱歉，参数收集过程中出现问题，我将使用默认参数为您提供建议。"
        StateUtils.set_error(state, f"参数收集失败: {str(e)}")

        # 使用默认参数继续流程
        if "flow_state" not in state:
            state["flow_state"] = {}
        state["flow_state"]["stage"] = "database_query"
        return state

async def database_query_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """数据库查询节点"""
    try:
        training_params = state.get("training_params", {})
        user_profile = state.get("user_profile", {})

        # 获取查询参数
        body_part = training_params.get("body_part", ["胸部"])[0] if isinstance(training_params.get("body_part"), list) else training_params.get("body_part", "胸部")
        scenario = training_params.get("scenario", "健身房")

        # 模拟数据库查询（在实际环境中会调用真实的数据库）
        try:
            # 这里应该调用真实的get_candidate_exercises函数
            # from app.services.conversation.exercise_helper import get_candidate_exercises
            # candidate_exercises = await get_candidate_exercises(service, body_part, scenario, limit=20)

            # 模拟候选动作数据
            candidate_exercises = [
                {
                    "id": 1,
                    "name": "标准俯卧撑",
                    "description": "基础胸肌训练动作，适合各个水平的训练者",
                    "body_parts": ["胸部"],
                    "equipment": ["徒手"],
                    "level": 1,
                    "instructions": ["双手撑地，身体保持直线", "下降至胸部接近地面", "推起至起始位置"]
                },
                {
                    "id": 2,
                    "name": "哑铃卧推",
                    "description": "经典胸肌力量训练动作",
                    "body_parts": ["胸部"],
                    "equipment": ["哑铃"],
                    "level": 2,
                    "instructions": ["仰卧在卧推凳上", "双手持哑铃推起", "控制下降至胸部两侧"]
                },
                {
                    "id": 3,
                    "name": "哑铃飞鸟",
                    "description": "胸肌塑形和拉伸动作",
                    "body_parts": ["胸部"],
                    "equipment": ["哑铃"],
                    "level": 2,
                    "instructions": ["仰卧持哑铃", "双臂张开呈弧形", "感受胸肌拉伸和收缩"]
                }
            ]

            # 保存候选动作到状态
            state["flow_state"]["candidate_exercises"] = candidate_exercises
            state["flow_state"]["stage"] = "ai_filtering"

            logger.info(f"数据库查询完成，找到 {len(candidate_exercises)} 个候选动作")

        except Exception as query_error:
            logger.warning(f"数据库查询失败，使用默认动作: {str(query_error)}")
            # 使用默认动作
            default_exercises = [
                {
                    "id": 1,
                    "name": "俯卧撑",
                    "description": "基础胸肌训练",
                    "body_parts": ["胸部"],
                    "equipment": ["徒手"],
                    "level": 1
                }
            ]
            state["flow_state"]["candidate_exercises"] = default_exercises
            state["flow_state"]["stage"] = "ai_filtering"

        # 更新状态
        state["current_node"] = "database_query"
        state["confidence"] = 0.9

        return state

    except Exception as e:
        logger.error(f"数据库查询失败: {str(e)}")
        state["current_node"] = "database_query"
        state["response_content"] = "抱歉，动作查询过程中出现问题。"
        StateUtils.set_error(state, f"数据库查询失败: {str(e)}")
        return state

async def ai_filtering_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """AI筛选节点"""
    try:
        flow_state = state.get("flow_state", {})
        candidate_exercises = flow_state.get("candidate_exercises", [])
        user_profile = state.get("user_profile", {})
        training_params = state.get("training_params", {})

        if not candidate_exercises:
            logger.warning("没有候选动作可供筛选")
            state["flow_state"]["filtered_exercises"] = []
            state["flow_state"]["stage"] = "response_generation"
            return state

        try:
            # 这里应该调用真实的personalized_filtering函数
            # from app.services.conversation.exercise_helper import personalized_filtering
            # filtered_exercises = await personalized_filtering(service, candidate_exercises, user_profile, training_params, limit=5)

            # 模拟AI筛选逻辑
            filtered_exercises = []

            # 根据用户水平筛选
            user_level = user_profile.get("fitness_level", "初级")
            level_mapping = {"初级": 1, "中级": 2, "高级": 3}
            max_level = level_mapping.get(user_level, 1)

            # 筛选适合用户水平的动作
            suitable_exercises = [ex for ex in candidate_exercises if ex.get("level", 1) <= max_level]

            # 选择前5个动作
            selected_exercises = suitable_exercises[:5]

            # 为每个动作添加个性化训练参数
            for exercise in selected_exercises:
                # 根据用户目标设置训练参数
                fitness_goal = user_profile.get("fitness_goal", "增肌")

                if fitness_goal in ["增肌", "muscle gain"]:
                    sets = 4
                    reps = "8-12"
                    rest_seconds = 90
                elif fitness_goal in ["减脂", "fat loss"]:
                    sets = 3
                    reps = "12-15"
                    rest_seconds = 60
                elif fitness_goal in ["力量", "strength"]:
                    sets = 5
                    reps = "3-6"
                    rest_seconds = 120
                else:
                    sets = 3
                    reps = "10-12"
                    rest_seconds = 75

                exercise.update({
                    "sets": sets,
                    "reps": reps,
                    "rest_seconds": rest_seconds,
                    "notes": f"根据您的{fitness_goal}目标定制"
                })

            filtered_exercises = selected_exercises

            logger.info(f"AI筛选完成，选出 {len(filtered_exercises)} 个动作")

        except Exception as filtering_error:
            logger.warning(f"AI筛选失败，使用简单筛选: {str(filtering_error)}")
            # 简单筛选：取前3个动作
            filtered_exercises = candidate_exercises[:3]
            for exercise in filtered_exercises:
                exercise.update({
                    "sets": 3,
                    "reps": "10-12",
                    "rest_seconds": 75,
                    "notes": "默认训练参数"
                })

        # 保存筛选结果
        state["flow_state"]["filtered_exercises"] = filtered_exercises
        state["flow_state"]["stage"] = "response_generation"

        # 更新状态
        state["current_node"] = "ai_filtering"
        state["confidence"] = 0.9

        return state

    except Exception as e:
        logger.error(f"AI筛选失败: {str(e)}")
        state["current_node"] = "ai_filtering"
        state["response_content"] = "抱歉，动作筛选过程中出现问题。"
        StateUtils.set_error(state, f"AI筛选失败: {str(e)}")
        return state

async def response_generation_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """响应生成节点"""
    try:
        flow_state = state.get("flow_state", {})
        filtered_exercises = flow_state.get("filtered_exercises", [])
        user_profile = state.get("user_profile", {})
        training_params = state.get("training_params", {})

        if not filtered_exercises:
            response_content = "抱歉，没有找到适合您的训练动作。请尝试调整您的需求或联系我们的专业教练。"
        else:
            # 生成结构化的动作推荐
            body_part = training_params.get("body_part", "目标部位")
            scenario = training_params.get("scenario", "训练场景")
            user_name = user_profile.get("name", "")

            response_parts = []

            # 个性化开头
            if user_name:
                response_parts.append(f"您好{user_name}！")

            response_parts.append(f"根据您的{body_part}训练需求和{scenario}场景，我为您推荐以下{len(filtered_exercises)}个动作：")
            response_parts.append("")

            # 添加每个动作的详细信息
            for i, exercise in enumerate(filtered_exercises, 1):
                exercise_info = []
                exercise_info.append(f"**{i}. {exercise['name']}**")
                exercise_info.append(f"   📝 {exercise.get('description', '专业训练动作')}")

                # 训练参数
                sets = exercise.get('sets', 3)
                reps = exercise.get('reps', '10-12')
                rest = exercise.get('rest_seconds', 75)
                exercise_info.append(f"   💪 训练参数：{sets}组 × {reps}次，组间休息{rest}秒")

                # 动作要点
                if 'instructions' in exercise and exercise['instructions']:
                    exercise_info.append("   🎯 动作要点：")
                    for instruction in exercise['instructions'][:3]:  # 最多显示3个要点
                        exercise_info.append(f"      • {instruction}")

                # 个性化建议
                if 'notes' in exercise and exercise['notes']:
                    exercise_info.append(f"   💡 {exercise['notes']}")

                response_parts.append("\n".join(exercise_info))
                response_parts.append("")

            # 添加总体建议
            response_parts.append("**训练建议：**")
            response_parts.append("• 训练前充分热身，训练后进行拉伸")
            response_parts.append("• 注意动作标准，循序渐进增加强度")
            response_parts.append("• 保证充足休息，每周训练2-3次")

            # 根据用户目标添加特定建议
            fitness_goal = user_profile.get("fitness_goal", "")
            if fitness_goal == "增肌":
                response_parts.append("• 增肌期间注意蛋白质摄入，建议每公斤体重1.6-2.2g")
            elif fitness_goal == "减脂":
                response_parts.append("• 减脂期间控制热量摄入，结合有氧运动效果更佳")
            elif fitness_goal == "力量":
                response_parts.append("• 力量训练注重重量递增，确保动作质量")

            response_content = "\n".join(response_parts)

        # 准备结构化数据
        structured_data = {
            "exercise_recommendations": filtered_exercises,
            "user_profile_summary": user_profile,
            "training_parameters": training_params,
            "recommendation_count": len(filtered_exercises)
        }

        # 更新状态
        state["current_node"] = "response_generation"
        state["processing_system"] = "enhanced_exercise"
        state["response_content"] = response_content
        state["structured_data"] = structured_data
        state["confidence"] = 0.95
        state["intent"] = "exercise_action"

        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state["messages"].append(ai_message)

        logger.info(f"响应生成完成，推荐了 {len(filtered_exercises)} 个动作")
        return state

    except Exception as e:
        logger.error(f"响应生成失败: {str(e)}")
        error_response = "抱歉，生成训练建议时出现问题。请稍后再试或联系我们的专业教练。"

        state["current_node"] = "response_generation"
        state["response_content"] = error_response
        state["confidence"] = 0.5
        StateUtils.set_error(state, f"响应生成失败: {str(e)}")

        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)

        return state

async def general_response_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """通用响应节点"""
    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 生成通用健身助手响应
        if "你好" in user_message or "hello" in user_message.lower():
            response_content = """您好！我是您的专业健身AI助手，很高兴为您服务！

我可以帮助您：
🏋️ 制定个性化训练计划
💪 推荐具体的运动动作
🥗 提供营养饮食建议
📊 追踪健身进度
❓ 解答健身相关问题

请告诉我您想了解什么，比如"胸肌怎么练"、"如何制定训练计划"等，我会为您提供专业的建议！"""
        elif "谢谢" in user_message or "thank" in user_message.lower():
            response_content = "不客气！很高兴能帮助您。如果您还有其他健身相关的问题，随时可以问我。祝您健身愉快，身体健康！💪"
        elif "再见" in user_message or "bye" in user_message.lower():
            response_content = "再见！记得坚持锻炼，保持健康的生活方式。期待下次为您服务！🌟"
        else:
            response_content = f"""我理解您说的是"{user_message}"。

作为您的健身AI助手，我专注于为您提供健身相关的帮助。如果您有任何关于：
- 运动训练动作（如"胸肌怎么练"）
- 健身计划制定
- 营养饮食建议
- 健康生活方式

等方面的问题，我都很乐意为您解答！请告诉我您想了解什么。"""

        # 更新状态
        state["current_node"] = "general_response"
        state["processing_system"] = "general"
        state["response_content"] = response_content
        state["confidence"] = 0.8
        state["intent"] = "general_chat"

        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state["messages"].append(ai_message)

        logger.info("通用响应生成完成")
        return state

    except Exception as e:
        logger.error(f"通用响应失败: {str(e)}")
        error_response = "抱歉，我暂时无法处理您的请求。请稍后再试。"

        state["current_node"] = "general_response"
        state["response_content"] = error_response
        state["confidence"] = 0.5
        StateUtils.set_error(state, f"通用响应失败: {str(e)}")

        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)

        return state

# ===== 条件路由函数 =====

def route_from_router(state: UnifiedFitnessState) -> str:
    """从路由节点的条件路由"""
    try:
        routing_decision = state.get("routing_decision", {})
        route = routing_decision.get("route", "general_response")

        valid_routes = ["user_info_verification", "general_response"]
        if route not in valid_routes:
            logger.warning(f"无效路由: {route}，使用默认")
            return "general_response"

        logger.info(f"路由到: {route}")
        return route
    except Exception as e:
        logger.error(f"路由条件失败: {str(e)}")
        return "general_response"

def route_from_user_info(state: UnifiedFitnessState) -> str:
    """从用户信息验证节点的条件路由"""
    try:
        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        if stage == "collecting_user_info":
            # 需要等待用户输入
            return END
        elif stage == "parameter_collection":
            # 用户信息完整，继续参数收集
            return "parameter_collection"
        else:
            # 异常情况，结束流程
            return END
    except Exception as e:
        logger.error(f"用户信息路由失败: {str(e)}")
        return END

def route_from_parameter_collection(state: UnifiedFitnessState) -> str:
    """从参数收集节点的条件路由"""
    try:
        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        if stage == "collecting_training_params":
            # 需要等待用户输入
            return END
        elif stage == "database_query":
            # 参数收集完成，进入数据库查询
            return "database_query"
        else:
            # 异常情况，结束流程
            return END
    except Exception as e:
        logger.error(f"参数收集路由失败: {str(e)}")
        return END

def route_from_database_query(state: UnifiedFitnessState) -> str:
    """从数据库查询节点的条件路由"""
    try:
        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        if stage == "ai_filtering":
            return "ai_filtering"
        else:
            return END
    except Exception as e:
        logger.error(f"数据库查询路由失败: {str(e)}")
        return END

def route_from_ai_filtering(state: UnifiedFitnessState) -> str:
    """从AI筛选节点的条件路由"""
    try:
        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        if stage == "response_generation":
            return "response_generation"
        else:
            return END
    except Exception as e:
        logger.error(f"AI筛选路由失败: {str(e)}")
        return END

# ===== 用户输入处理函数 =====

async def handle_user_input_for_collection(state: UnifiedFitnessState, user_input: str) -> UnifiedFitnessState:
    """处理参数收集过程中的用户输入"""
    try:
        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        if stage == "collecting_user_info":
            # 处理用户信息收集
            current_field = flow_state.get("current_collecting_field", "")

            # 验证并更新用户信息
            is_valid, feedback, normalized_value = await UserProfileManager.process_user_info_input(current_field, user_input)

            if is_valid:
                # 更新用户档案
                if "user_profile" not in state:
                    state["user_profile"] = {}
                state["user_profile"][current_field] = normalized_value

                # 检查是否还有缺失字段
                missing_fields = UserProfileManager.get_missing_fields(state["user_profile"])
                if missing_fields:
                    # 继续收集下一个字段
                    next_field = missing_fields[0]
                    state["flow_state"]["current_collecting_field"] = next_field
                    field_prompt = UserProfileManager.get_field_prompt(next_field)
                    response_content = f"谢谢！{feedback}\n\n{field_prompt}"
                else:
                    # 用户信息收集完成
                    state["flow_state"]["needs_user_info"] = False
                    state["flow_state"]["stage"] = "parameter_collection"
                    response_content = f"谢谢！{feedback}\n\n现在让我们继续收集训练参数..."
            else:
                # 输入无效，重新询问
                field_prompt = UserProfileManager.get_field_prompt(current_field)
                response_content = f"抱歉，{feedback}\n\n{field_prompt}"

            # 更新响应
            state["response_content"] = response_content
            ai_message = AIMessage(content=response_content)
            state["messages"].append(ai_message)

        elif stage == "collecting_training_params":
            # 处理训练参数收集
            current_param = flow_state.get("current_collecting_param", "")
            collected_params = flow_state.get("collected_params", {})

            # 解析用户输入
            if current_param == "scenario":
                if any(keyword in user_input for keyword in ["健身房", "gym", "器械"]):
                    collected_params["scenario"] = "健身房"
                elif any(keyword in user_input for keyword in ["居家", "家", "home", "徒手"]):
                    collected_params["scenario"] = "居家"
                elif any(keyword in user_input for keyword in ["户外", "outdoor", "公园"]):
                    collected_params["scenario"] = "户外"
                else:
                    collected_params["scenario"] = "健身房"  # 默认
            elif current_param == "equipment":
                if any(keyword in user_input for keyword in ["徒手", "无器械", "bodyweight"]):
                    collected_params["equipment"] = "徒手"
                elif any(keyword in user_input for keyword in ["哑铃", "dumbbell"]):
                    collected_params["equipment"] = "哑铃"
                elif any(keyword in user_input for keyword in ["杠铃", "barbell"]):
                    collected_params["equipment"] = "杠铃"
                else:
                    collected_params["equipment"] = "综合"  # 默认

            # 更新收集的参数
            state["flow_state"]["collected_params"] = collected_params

            # 检查是否还有缺失参数
            required_params = ["body_part", "scenario"]
            missing_params = [p for p in required_params if p not in collected_params or not collected_params[p]]

            if missing_params:
                # 继续收集下一个参数
                next_param = missing_params[0]
                state["flow_state"]["current_collecting_param"] = next_param
                if next_param == "scenario":
                    response_content = "请告诉我您想在哪里锻炼？\n\n🏠 居家训练\n🏋️ 健身房训练\n🏃 户外运动"
                else:
                    response_content = f"请提供您的{next_param}信息。"
            else:
                # 参数收集完成
                state["flow_state"]["needs_training_params"] = False
                state["flow_state"]["stage"] = "database_query"
                state["training_params"] = collected_params
                response_content = "好的，参数收集完成！正在为您查找合适的训练动作..."

            # 更新响应
            state["response_content"] = response_content
            ai_message = AIMessage(content=response_content)
            state["messages"].append(ai_message)

        return state

    except Exception as e:
        logger.error(f"处理用户输入失败: {str(e)}")
        error_response = "抱歉，处理您的输入时出现问题。让我们重新开始。"
        state["response_content"] = error_response
        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)
        return state

# ===== 图类定义 =====

class EnhancedExerciseGraph:
    """增强版运动动作处理图"""

    def __init__(self):
        self.graph = None
        self.compiled_graph = None
        self.checkpointer = None
        self._initialized = False

    def initialize(self) -> bool:
        """初始化图"""
        try:
            # 创建状态图
            self.graph = StateGraph(UnifiedFitnessState)

            # 添加节点
            self.graph.add_node("exercise_intent_router", exercise_intent_router_node)
            self.graph.add_node("user_info_verification", user_info_verification_node)
            self.graph.add_node("parameter_collection", parameter_collection_node)
            self.graph.add_node("database_query", database_query_node)
            self.graph.add_node("ai_filtering", ai_filtering_node)
            self.graph.add_node("response_generation", response_generation_node)
            self.graph.add_node("general_response", general_response_node)

            # 设置入口点
            self.graph.set_entry_point("exercise_intent_router")

            # 添加条件边
            self.graph.add_conditional_edges(
                "exercise_intent_router",
                route_from_router,
                {
                    "user_info_verification": "user_info_verification",
                    "general_response": "general_response"
                }
            )

            self.graph.add_conditional_edges(
                "user_info_verification",
                route_from_user_info,
                {
                    "parameter_collection": "parameter_collection",
                    END: END
                }
            )

            self.graph.add_conditional_edges(
                "parameter_collection",
                route_from_parameter_collection,
                {
                    "database_query": "database_query",
                    END: END
                }
            )

            self.graph.add_conditional_edges(
                "database_query",
                route_from_database_query,
                {
                    "ai_filtering": "ai_filtering",
                    END: END
                }
            )

            self.graph.add_conditional_edges(
                "ai_filtering",
                route_from_ai_filtering,
                {
                    "response_generation": "response_generation",
                    END: END
                }
            )

            # 添加结束边
            self.graph.add_edge("response_generation", END)
            self.graph.add_edge("general_response", END)

            # 使用内存检查点
            self.checkpointer = MemorySaver()

            # 编译图
            self.compiled_graph = self.graph.compile(checkpointer=self.checkpointer)

            self._initialized = True
            logger.info("增强版运动动作处理图初始化成功")
            return True

        except Exception as e:
            logger.error(f"增强版运动动作处理图初始化失败: {str(e)}")
            return False

    async def process_message(self, message: str, conversation_id: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理消息"""
        try:
            if not self._initialized:
                if not self.initialize():
                    raise Exception("图初始化失败")

            # 创建初始状态
            initial_state = StateAdapter.to_unified_state(
                message=message,
                conversation_id=conversation_id,
                user_info=user_info
            )

            # 配置
            config = {
                "configurable": {
                    "thread_id": conversation_id,
                    "checkpoint_ns": "enhanced_exercise"
                }
            }

            # 执行图
            result = await self.compiled_graph.ainvoke(initial_state, config=config)

            # 转换为API响应
            api_response = StateAdapter.create_api_response(result)

            logger.info(f"增强版运动动作图执行完成: {conversation_id}")
            return api_response

        except Exception as e:
            logger.error(f"增强版运动动作图执行失败: {str(e)}")
            return {
                "success": False,
                "response": f"处理失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "conversation_id": conversation_id,
                "error": str(e)
            }

    async def continue_conversation(self, message: str, conversation_id: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """继续对话（用于多轮参数收集）"""
        try:
            if not self._initialized:
                if not self.initialize():
                    raise Exception("图初始化失败")

            # 配置
            config = {
                "configurable": {
                    "thread_id": conversation_id,
                    "checkpoint_ns": "enhanced_exercise"
                }
            }

            try:
                # 获取当前状态
                current_state = await self.compiled_graph.aget_state(config)

                if current_state and current_state.values:
                    # 更新状态中的用户消息
                    state = current_state.values

                    # 添加新的用户消息
                    human_message = HumanMessage(content=message)
                    if "messages" not in state:
                        state["messages"] = []
                    state["messages"].append(human_message)

                    # 处理用户输入
                    updated_state = await handle_user_input_for_collection(state, message)

                    # 检查是否需要继续流程
                    flow_state = updated_state.get("flow_state", {})
                    stage = flow_state.get("stage", "")

                    if stage == "database_query":
                        # 手动执行后续节点
                        # 数据库查询
                        db_state = await database_query_node(updated_state)
                        # AI筛选
                        ai_state = await ai_filtering_node(db_state)
                        # 响应生成
                        final_state = await response_generation_node(ai_state)
                        result = final_state
                    else:
                        # 返回当前状态（等待更多用户输入）
                        result = updated_state

                    # 转换为API响应
                    api_response = StateAdapter.create_api_response(result)

                    logger.info(f"继续对话完成: {conversation_id}")
                    return api_response
                else:
                    # 没有找到现有状态，开始新对话
                    logger.info(f"没有找到现有状态，开始新对话: {conversation_id}")
                    return await self.process_message(message, conversation_id, user_info)

            except Exception as state_error:
                logger.warning(f"状态获取失败，开始新对话: {str(state_error)}")
                # 如果状态获取失败，开始新对话
                return await self.process_message(message, conversation_id, user_info)

        except Exception as e:
            logger.error(f"继续对话失败: {str(e)}")
            return {
                "success": False,
                "response": f"继续对话失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "conversation_id": conversation_id,
                "error": str(e)
            }

# 全局实例
enhanced_exercise_graph = EnhancedExerciseGraph()