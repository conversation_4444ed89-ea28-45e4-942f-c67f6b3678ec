"""
增强版运动动作处理图 - 重构版

基于LangGraph框架实现的专业运动动作AI助手，使用模块化节点设计
"""

import logging
from typing import Dict, Any

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.adapters.state_adapter import StateAdapter

# 导入模块化的节点
from app.services.ai_assistant.langgraph.nodes.intent_router import (
    exercise_intent_router_node,
    route_from_router,
    general_response_node
)
from app.services.ai_assistant.langgraph.nodes.user_verification import (
    user_info_verification_node,
    route_from_user_info
)
from app.services.ai_assistant.langgraph.nodes.parameter_collection import (
    parameter_collection_node,
    route_from_parameter_collection,
    handle_user_input_for_collection
)
from app.services.ai_assistant.langgraph.nodes.database_query import (
    database_query_node,
    route_from_database_query
)
from app.services.ai_assistant.langgraph.nodes.ai_filtering import (
    ai_filtering_node,
    route_from_ai_filtering
)
from app.services.ai_assistant.langgraph.nodes.response_generation import (
    response_generation_node
)

logger = logging.getLogger(__name__)

class EnhancedExerciseGraph:
    """增强版运动动作处理图 - 重构版"""

    def __init__(self):
        self.graph = None
        self.compiled_graph = None
        self.checkpointer = None
        self._initialized = False

    def initialize(self) -> bool:
        """初始化图"""
        try:
            # 创建状态图
            self.graph = StateGraph(UnifiedFitnessState)

            # 添加节点
            self.graph.add_node("exercise_intent_router", exercise_intent_router_node)
            self.graph.add_node("user_info_verification", user_info_verification_node)
            self.graph.add_node("parameter_collection", parameter_collection_node)
            self.graph.add_node("database_query", database_query_node)
            self.graph.add_node("ai_filtering", ai_filtering_node)
            self.graph.add_node("response_generation", response_generation_node)
            self.graph.add_node("general_response", general_response_node)

            # 设置入口点
            self.graph.set_entry_point("exercise_intent_router")

            # 添加条件边
            self.graph.add_conditional_edges(
                "exercise_intent_router",
                route_from_router,
                {
                    "user_info_verification": "user_info_verification",
                    "general_response": "general_response"
                }
            )

            self.graph.add_conditional_edges(
                "user_info_verification",
                route_from_user_info,
                {
                    "parameter_collection": "parameter_collection",
                    END: END
                }
            )

            self.graph.add_conditional_edges(
                "parameter_collection",
                route_from_parameter_collection,
                {
                    "database_query": "database_query",
                    END: END
                }
            )

            self.graph.add_conditional_edges(
                "database_query",
                route_from_database_query,
                {
                    "ai_filtering": "ai_filtering",
                    END: END
                }
            )

            self.graph.add_conditional_edges(
                "ai_filtering",
                route_from_ai_filtering,
                {
                    "response_generation": "response_generation",
                    END: END
                }
            )

            # 添加结束边
            self.graph.add_edge("response_generation", END)
            self.graph.add_edge("general_response", END)

            # 使用内存检查点
            self.checkpointer = MemorySaver()

            # 编译图
            self.compiled_graph = self.graph.compile(checkpointer=self.checkpointer)

            self._initialized = True
            logger.info("增强版运动动作处理图（重构版）初始化成功")
            return True

        except Exception as e:
            logger.error(f"增强版运动动作处理图（重构版）初始化失败: {str(e)}")
            return False

    async def process_message(self, message: str, conversation_id: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理消息"""
        try:
            if not self._initialized:
                if not self.initialize():
                    raise Exception("图初始化失败")

            # 创建初始状态
            initial_state = StateAdapter.to_unified_state(
                message=message,
                conversation_id=conversation_id,
                user_info=user_info
            )

            # 配置
            config = {
                "configurable": {
                    "thread_id": conversation_id
                }
            }

            # 执行图
            result = await self.compiled_graph.ainvoke(initial_state, config=config)

            # 转换为API响应
            api_response = StateAdapter.create_api_response(result)

            logger.info(f"增强版运动动作图（重构版）执行完成: {conversation_id}")
            return api_response

        except Exception as e:
            logger.error(f"增强版运动动作图（重构版）执行失败: {str(e)}")
            return {
                "success": False,
                "response": f"处理失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "conversation_id": conversation_id,
                "error": str(e)
            }

    async def continue_conversation(self, message: str, conversation_id: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """继续对话（用于多轮参数收集）"""
        try:
            if not self._initialized:
                if not self.initialize():
                    raise Exception("图初始化失败")

            # 配置
            config = {
                "configurable": {
                    "thread_id": conversation_id
                }
            }

            try:
                # 获取当前状态
                current_state = await self.compiled_graph.aget_state(config)
                
                if current_state and current_state.values:
                    # 更新状态中的用户消息
                    state = current_state.values
                    
                    # 添加新的用户消息
                    from langchain_core.messages import HumanMessage
                    human_message = HumanMessage(content=message)
                    if "messages" not in state:
                        state["messages"] = []
                    state["messages"].append(human_message)
                    
                    # 处理用户输入
                    updated_state = await handle_user_input_for_collection(state, message)
                    
                    # 检查是否需要继续流程
                    flow_state = updated_state.get("flow_state", {})
                    stage = flow_state.get("stage", "")
                    
                    if stage == "database_query":
                        # 手动执行后续节点
                        # 数据库查询
                        db_state = await database_query_node(updated_state)
                        # AI筛选
                        ai_state = await ai_filtering_node(db_state)
                        # 响应生成
                        final_state = await response_generation_node(ai_state)
                        result = final_state
                    else:
                        # 返回当前状态（等待更多用户输入）
                        result = updated_state

                    # 转换为API响应
                    api_response = StateAdapter.create_api_response(result)
                    
                    logger.info(f"继续对话完成: {conversation_id}")
                    return api_response
                else:
                    # 没有找到现有状态，开始新对话
                    logger.info(f"没有找到现有状态，开始新对话: {conversation_id}")
                    return await self.process_message(message, conversation_id, user_info)
                    
            except Exception as state_error:
                logger.warning(f"状态获取失败，开始新对话: {str(state_error)}")
                # 如果状态获取失败，开始新对话
                return await self.process_message(message, conversation_id, user_info)

        except Exception as e:
            logger.error(f"继续对话失败: {str(e)}")
            return {
                "success": False,
                "response": f"继续对话失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "conversation_id": conversation_id,
                "error": str(e)
            }

# 全局实例
enhanced_exercise_graph_refactored = EnhancedExerciseGraph()
