"""
健身AI助手LangGraph图定义

定义完整的工作流图结构，包括节点间的条件路由和边连接。
"""

import logging
from typing import Dict, Any, Optional, Literal
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.nodes.router_node import intelligent_router_node
from app.services.ai_assistant.langgraph.nodes.processor_nodes import (
    enhanced_processor_node,
    legacy_processor_node,
    state_machine_processor_node
)
from app.services.ai_assistant.langgraph.nodes.hybrid_node import hybrid_processor_node
from app.services.ai_assistant.langgraph.nodes.result_selector import result_selector_node
from app.services.ai_assistant.langgraph.utils.checkpoint_config import get_checkpoint_saver
from app.core.unified_config import unified_settings

logger = logging.getLogger(__name__)

class FitnessAIGraph:
    """健身AI助手LangGraph图"""

    def __init__(self):
        self.graph: Optional[StateGraph] = None
        self.compiled_graph = None
        self.checkpointer = None
        self._initialized = False

    def initialize(self) -> bool:
        """
        初始化图结构

        Returns:
            初始化是否成功
        """
        try:
            if self._initialized:
                logger.info("FitnessAIGraph已初始化")
                return True

            # 创建状态图
            self.graph = StateGraph(UnifiedFitnessState)

            # 添加节点
            self._add_nodes()

            # 添加边和条件路由
            self._add_edges()

            # 设置入口点
            self.graph.set_entry_point("intelligent_router")

            # 获取检查点存储器
            self.checkpointer = get_checkpoint_saver()
            if not self.checkpointer:
                logger.warning("使用内存检查点存储器")
                self.checkpointer = MemorySaver()

            # 编译图
            self.compiled_graph = self.graph.compile(
                checkpointer=self.checkpointer,
                interrupt_before=[],  # 可以在这里添加需要中断的节点
                interrupt_after=[]    # 可以在这里添加需要中断的节点
            )

            self._initialized = True
            logger.info("FitnessAIGraph初始化成功")
            return True

        except Exception as e:
            logger.error(f"FitnessAIGraph初始化失败: {str(e)}")
            return False

    def _add_nodes(self):
        """添加所有节点"""
        # 智能路由节点
        self.graph.add_node("intelligent_router", intelligent_router_node)

        # 处理器节点
        self.graph.add_node("enhanced_processor", enhanced_processor_node)
        self.graph.add_node("legacy_processor", legacy_processor_node)
        self.graph.add_node("state_machine_processor", state_machine_processor_node)

        # 混合处理节点
        self.graph.add_node("hybrid_processor", hybrid_processor_node)

        # 结果选择节点
        self.graph.add_node("result_selector", result_selector_node)

        # 错误处理节点
        self.graph.add_node("error_handler", self._error_handler_node)

        logger.debug("所有节点已添加到图中")

    def _add_edges(self):
        """添加边和条件路由"""
        # 从智能路由器到处理器的条件路由
        self.graph.add_conditional_edges(
            "intelligent_router",
            self._route_to_processor,
            {
                "enhanced": "enhanced_processor",
                "legacy": "legacy_processor",
                "state_machine": "state_machine_processor",
                "hybrid": "hybrid_processor",
                "error": "error_handler"
            }
        )

        # 单一处理器直接到结果选择器
        self.graph.add_edge("enhanced_processor", "result_selector")
        self.graph.add_edge("legacy_processor", "result_selector")
        self.graph.add_edge("state_machine_processor", "result_selector")

        # 混合处理器直接到结果选择器
        self.graph.add_edge("hybrid_processor", "result_selector")

        # 结果选择器到结束
        self.graph.add_edge("result_selector", END)

        # 错误处理器到结束
        self.graph.add_edge("error_handler", END)

        logger.debug("所有边已添加到图中")

    def _route_to_processor(self, state: UnifiedFitnessState) -> str:
        """
        路由到处理器的条件函数

        Args:
            state: 当前状态

        Returns:
            下一个节点名称
        """
        try:
            # 检查是否有错误
            if state.error_count > 0 and not state.can_retry():
                logger.warning("错误次数过多，路由到错误处理器")
                return "error"

            # 获取路由决策
            routing_decision = state.routing_decision
            if not routing_decision:
                logger.warning("没有路由决策，使用默认路由")
                return "state_machine"

            route = routing_decision.get("route", "state_machine")
            confidence = routing_decision.get("confidence", 0.5)

            # 如果置信度太低，使用混合模式
            if confidence < 0.6:
                logger.info(f"置信度过低({confidence:.2f})，使用混合模式")
                return "hybrid"

            # 验证路由有效性
            valid_routes = ["enhanced", "legacy", "state_machine", "hybrid"]
            if route not in valid_routes:
                logger.warning(f"无效路由: {route}，使用默认路由")
                return "state_machine"

            logger.info(f"路由到: {route} (置信度: {confidence:.2f})")
            return route

        except Exception as e:
            logger.error(f"路由决策失败: {str(e)}")
            return "error"

    def _error_handler_node(self, state: UnifiedFitnessState) -> Dict[str, Any]:
        """
        错误处理节点

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        try:
            error_message = state.last_error or "处理过程中出现未知错误"

            # 生成用户友好的错误响应
            if "timeout" in error_message.lower():
                response = "抱歉，处理您的请求超时了，请稍后重试。"
            elif "network" in error_message.lower():
                response = "网络连接出现问题，请检查网络后重试。"
            elif "database" in error_message.lower():
                response = "数据库连接出现问题，请稍后重试。"
            else:
                response = "抱歉，处理您的请求时出现了错误，请稍后重试。"

            logger.error(f"错误处理节点: {error_message}")

            return {
                "current_node": "error_handler",
                "response_content": response,
                "confidence": 0.0,
                "processing_system": "error_handler"
            }

        except Exception as e:
            logger.error(f"错误处理节点失败: {str(e)}")
            return {
                "current_node": "error_handler",
                "response_content": "系统出现严重错误，请联系技术支持。",
                "confidence": 0.0,
                "processing_system": "error_handler"
            }

    async def process_message(
        self,
        message: str,
        conversation_id: str,
        user_info: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        处理用户消息

        Args:
            message: 用户消息
            conversation_id: 对话ID
            user_info: 用户信息
            config: 配置信息

        Returns:
            处理结果
        """
        try:
            if not self._initialized:
                if not self.initialize():
                    raise Exception("图初始化失败")

            # 创建初始状态
            from app.services.ai_assistant.langgraph.adapters.state_adapter import StateAdapter

            initial_state = StateAdapter.to_unified_state(
                message=message,
                conversation_id=conversation_id,
                user_info=user_info
            )

            # 设置配置
            run_config = {
                "configurable": {
                    "thread_id": conversation_id,
                    "checkpoint_ns": "fitness_ai"
                }
            }

            if config:
                run_config.update(config)

            # 执行图
            if unified_settings.LANGGRAPH_ENABLE_STREAMING:
                # 流式执行
                result_state = None
                async for chunk in self.compiled_graph.astream(
                    initial_state,
                    config=run_config
                ):
                    result_state = chunk

                if not result_state:
                    raise Exception("流式执行没有返回结果")

                # 获取最终状态
                final_state = list(result_state.values())[0] if result_state else initial_state
            else:
                # 非流式执行
                result = await self.compiled_graph.ainvoke(
                    initial_state,
                    config=run_config
                )
                final_state = result

            # 确保final_state是UnifiedFitnessState实例
            if isinstance(final_state, dict):
                # 如果是字典，需要转换为UnifiedFitnessState
                temp_state = UnifiedFitnessState()
                for key, value in final_state.items():
                    if hasattr(temp_state, key):
                        try:
                            setattr(temp_state, key, value)
                        except Exception as e:
                            logger.warning(f"设置最终状态字段{key}失败: {str(e)}")
                            continue
                # 确保基本字段存在
                if not temp_state.conversation_id:
                    temp_state.conversation_id = conversation_id
                if not temp_state.user_id and user_info:
                    temp_state.user_id = user_info.get("user_id", "unknown")
                # 调用__post_init__确保字段初始化
                temp_state.__post_init__()
                final_state = temp_state
            elif not isinstance(final_state, UnifiedFitnessState):
                # 如果不是预期类型，使用初始状态
                final_state = initial_state
                final_state.response_content = "处理完成，但状态格式异常"
                final_state.confidence = 0.5

            # 转换为API响应格式
            api_response = StateAdapter.create_api_response(final_state)

            logger.info(f"图执行完成: {conversation_id}")
            return api_response

        except Exception as e:
            logger.error(f"图执行失败: {str(e)}")
            return {
                "success": False,
                "response": f"处理失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "conversation_id": conversation_id,
                "error": str(e)
            }

    def get_graph_info(self) -> Dict[str, Any]:
        """
        获取图信息

        Returns:
            图信息字典
        """
        try:
            if not self._initialized:
                return {"initialized": False, "error": "图未初始化"}

            # 获取节点和边信息
            nodes = list(self.graph.nodes.keys()) if self.graph else []

            return {
                "initialized": self._initialized,
                "nodes_count": len(nodes),
                "nodes": nodes,
                "checkpointer_type": type(self.checkpointer).__name__ if self.checkpointer else None,
                "streaming_enabled": unified_settings.LANGGRAPH_ENABLE_STREAMING,
                "max_recursion": unified_settings.LANGGRAPH_MAX_RECURSION
            }

        except Exception as e:
            logger.error(f"获取图信息失败: {str(e)}")
            return {"error": str(e)}

    def visualize_graph(self) -> Optional[str]:
        """
        可视化图结构（返回Mermaid格式）

        Returns:
            Mermaid图定义字符串
        """
        try:
            if not self._initialized:
                return None

            mermaid = ["graph TD"]

            # 添加节点
            node_labels = {
                "intelligent_router": "智能路由器",
                "enhanced_processor": "增强处理器",
                "legacy_processor": "传统处理器",
                "state_machine_processor": "状态机处理器",
                "hybrid_processor": "混合处理器",
                "result_selector": "结果选择器",
                "error_handler": "错误处理器"
            }

            for node_id, label in node_labels.items():
                mermaid.append(f"    {node_id}[{label}]")

            # 添加边
            edges = [
                ("intelligent_router", "enhanced_processor", "增强"),
                ("intelligent_router", "legacy_processor", "传统"),
                ("intelligent_router", "state_machine_processor", "状态机"),
                ("intelligent_router", "hybrid_processor", "混合"),
                ("intelligent_router", "error_handler", "错误"),
                ("enhanced_processor", "result_selector", ""),
                ("legacy_processor", "result_selector", ""),
                ("state_machine_processor", "result_selector", ""),
                ("hybrid_processor", "result_selector", ""),
                ("result_selector", "END", ""),
                ("error_handler", "END", "")
            ]

            for source, target, label in edges:
                if label:
                    mermaid.append(f"    {source} -->|{label}| {target}")
                else:
                    mermaid.append(f"    {source} --> {target}")

            return "\n".join(mermaid)

        except Exception as e:
            logger.error(f"可视化图失败: {str(e)}")
            return None

# 全局图实例
fitness_ai_graph = FitnessAIGraph()

def get_fitness_ai_graph() -> FitnessAIGraph:
    """
    获取全局图实例

    Returns:
        FitnessAIGraph实例
    """
    if not fitness_ai_graph._initialized:
        fitness_ai_graph.initialize()
    return fitness_ai_graph

async def test_graph_basic_functionality():
    """测试图的基本功能"""
    try:
        graph = get_fitness_ai_graph()

        # 测试消息处理
        test_message = "你好，我想了解健身"
        test_user_info = {
            "user_id": "test_user",
            "nickname": "测试用户"
        }

        result = await graph.process_message(
            message=test_message,
            conversation_id="test_conversation",
            user_info=test_user_info
        )

        print("图测试结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  响应: {result.get('response', '')[:100]}...")
        print(f"  意图: {result.get('intent_type', 'unknown')}")
        print(f"  置信度: {result.get('confidence', 0.0):.2f}")

        return result.get('success', False)

    except Exception as e:
        print(f"图测试失败: {str(e)}")
        return False
