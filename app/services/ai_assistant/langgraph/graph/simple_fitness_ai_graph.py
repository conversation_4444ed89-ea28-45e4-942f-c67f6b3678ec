"""
简化的健身AI助手LangGraph图

用于测试和验证LangGraph基本功能的简化版本。
"""

import logging
from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
from app.services.ai_assistant.langgraph.nodes.router_node import intelligent_router_node
from app.services.ai_assistant.langgraph.nodes.simple_processor_nodes import (
    simple_enhanced_processor_node,
    simple_legacy_processor_node,
    simple_state_machine_processor_node,
    simple_hybrid_processor_node,
    simple_result_selector_node
)
from app.services.ai_assistant.langgraph.adapters.state_adapter import StateAdapter

logger = logging.getLogger(__name__)

class SimpleFitnessAIGraph:
    """简化的健身AI助手LangGraph图"""

    def __init__(self):
        self.graph: Optional[StateGraph] = None
        self.compiled_graph = None
        self.checkpointer = None
        self._initialized = False

    def initialize(self) -> bool:
        """初始化图结构"""
        try:
            if self._initialized:
                logger.info("SimpleFitnessAIGraph已初始化")
                return True

            # 创建状态图
            self.graph = StateGraph(UnifiedFitnessState)

            # 添加节点
            self._add_nodes()

            # 添加边和条件路由
            self._add_edges()

            # 设置入口点
            self.graph.set_entry_point("intelligent_router")

            # 使用内存检查点存储器
            self.checkpointer = MemorySaver()

            # 编译图
            self.compiled_graph = self.graph.compile(
                checkpointer=self.checkpointer
            )

            self._initialized = True
            logger.info("SimpleFitnessAIGraph初始化成功")
            return True

        except Exception as e:
            logger.error(f"SimpleFitnessAIGraph初始化失败: {str(e)}")
            return False

    def _add_nodes(self):
        """添加所有节点"""
        # 智能路由节点
        self.graph.add_node("intelligent_router", intelligent_router_node)

        # 处理器节点
        self.graph.add_node("enhanced_processor", simple_enhanced_processor_node)
        self.graph.add_node("legacy_processor", simple_legacy_processor_node)
        self.graph.add_node("state_machine_processor", simple_state_machine_processor_node)
        self.graph.add_node("hybrid_processor", simple_hybrid_processor_node)

        # 结果选择节点
        self.graph.add_node("result_selector", simple_result_selector_node)

        # 错误处理节点
        self.graph.add_node("error_handler", self._error_handler_node)

        logger.debug("所有节点已添加到简化图中")

    def _add_edges(self):
        """添加边和条件路由"""
        # 从智能路由器到处理器的条件路由
        self.graph.add_conditional_edges(
            "intelligent_router",
            self._route_to_processor,
            {
                "enhanced": "enhanced_processor",
                "legacy": "legacy_processor",
                "state_machine": "state_machine_processor",
                "hybrid": "hybrid_processor",
                "error": "error_handler"
            }
        )

        # 所有处理器都到结果选择器
        self.graph.add_edge("enhanced_processor", "result_selector")
        self.graph.add_edge("legacy_processor", "result_selector")
        self.graph.add_edge("state_machine_processor", "result_selector")
        self.graph.add_edge("hybrid_processor", "result_selector")

        # 结果选择器到结束
        self.graph.add_edge("result_selector", END)

        # 错误处理器到结束
        self.graph.add_edge("error_handler", END)

        logger.debug("所有边已添加到简化图中")

    def _route_to_processor(self, state: UnifiedFitnessState) -> str:
        """路由到处理器的条件函数"""
        try:
            # 检查是否有错误
            error_count = state.get("error_count", 0)
            if error_count > 0 and not StateUtils.can_retry(state):
                logger.warning("错误次数过多，路由到错误处理器")
                return "error"

            # 获取路由决策
            routing_decision = state.get('routing_decision', {})
            if not routing_decision:
                logger.warning("没有路由决策，使用默认路由")
                return "state_machine"

            route = routing_decision.get("route", "state_machine")
            confidence = routing_decision.get("confidence", 0.5)

            # 如果置信度太低，使用混合模式
            if confidence < 0.6:
                logger.info(f"置信度过低({confidence:.2f})，使用混合模式")
                return "hybrid"

            # 验证路由有效性
            valid_routes = ["enhanced", "legacy", "state_machine", "hybrid"]
            if route not in valid_routes:
                logger.warning(f"无效路由: {route}，使用默认路由")
                return "state_machine"

            logger.info(f"路由到: {route} (置信度: {confidence:.2f})")
            return route

        except Exception as e:
            logger.error(f"路由决策失败: {str(e)}")
            return "error"

    def _error_handler_node(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """错误处理节点"""
        try:
            error_message = state.get('last_error') or "处理过程中出现未知错误"

            # 生成用户友好的错误响应
            if "timeout" in error_message.lower():
                response = "抱歉，处理您的请求超时了，请稍后重试。"
            elif "network" in error_message.lower():
                response = "网络连接出现问题，请检查网络后重试。"
            else:
                response = "抱歉，处理您的请求时出现了错误，请稍后重试。"

            # 更新状态
            state["current_node"] = "error_handler"
            state["response_content"] = response
            state["confidence"] = 0.0
            state["processing_system"] = "error_handler"

            logger.error(f"错误处理节点: {error_message}")
            return state

        except Exception as e:
            logger.error(f"错误处理节点失败: {str(e)}")
            state["current_node"] = "error_handler"
            state["response_content"] = "系统出现严重错误，请联系技术支持。"
            state["confidence"] = 0.0
            state["processing_system"] = "error_handler"
            return state

    async def process_message(
        self,
        message: str,
        conversation_id: str,
        user_info: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理用户消息"""
        try:
            if not self._initialized:
                if not self.initialize():
                    raise Exception("图初始化失败")

            # 创建初始状态
            initial_state = StateAdapter.to_unified_state(
                message=message,
                conversation_id=conversation_id,
                user_info=user_info
            )

            # 设置配置
            run_config = {
                "configurable": {
                    "thread_id": conversation_id,
                    "checkpoint_ns": "simple_fitness_ai"
                }
            }

            if config:
                run_config.update(config)

            # 执行图（非流式）
            result = await self.compiled_graph.ainvoke(
                initial_state,
                config=run_config
            )

            # 确保result是UnifiedFitnessState实例
            if isinstance(result, dict):
                # 如果是字典，需要转换为UnifiedFitnessState
                temp_state = UnifiedFitnessState()
                for key, value in result.items():
                    if hasattr(temp_state, key):
                        try:
                            setattr(temp_state, key, value)
                        except Exception as e:
                            logger.warning(f"设置最终状态字段{key}失败: {str(e)}")
                            continue
                # 确保基本字段存在
                if not temp_state.conversation_id:
                    temp_state.conversation_id = conversation_id
                if not temp_state.user_id and user_info:
                    temp_state.user_id = user_info.get("user_id", "unknown")
                temp_state.__post_init__()
                final_state = temp_state
            else:
                final_state = result

            # 转换为API响应格式
            api_response = StateAdapter.create_api_response(final_state)

            logger.info(f"简化图执行完成: {conversation_id}")
            return api_response

        except Exception as e:
            logger.error(f"简化图执行失败: {str(e)}")
            return {
                "success": False,
                "response": f"处理失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "conversation_id": conversation_id,
                "error": str(e)
            }

    def get_graph_info(self) -> Dict[str, Any]:
        """获取图信息"""
        try:
            if not self._initialized:
                return {"initialized": False, "error": "图未初始化"}

            nodes = list(self.graph.nodes.keys()) if self.graph else []

            return {
                "initialized": self._initialized,
                "nodes_count": len(nodes),
                "nodes": nodes,
                "checkpointer_type": type(self.checkpointer).__name__ if self.checkpointer else None,
                "graph_type": "simplified"
            }

        except Exception as e:
            logger.error(f"获取简化图信息失败: {str(e)}")
            return {"error": str(e)}

# 全局简化图实例
simple_fitness_ai_graph = SimpleFitnessAIGraph()

def get_simple_fitness_ai_graph() -> SimpleFitnessAIGraph:
    """获取全局简化图实例"""
    if not simple_fitness_ai_graph._initialized:
        simple_fitness_ai_graph.initialize()
    return simple_fitness_ai_graph

async def test_simple_graph_basic_functionality():
    """测试简化图的基本功能"""
    try:
        graph = get_simple_fitness_ai_graph()

        # 测试消息处理
        test_message = "你好，我想了解健身"
        test_user_info = {
            "user_id": "test_user",
            "nickname": "测试用户"
        }

        result = await graph.process_message(
            message=test_message,
            conversation_id="test_conversation",
            user_info=test_user_info
        )

        print("简化图测试结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  响应: {result.get('response', '')[:100]}...")
        print(f"  意图: {result.get('intent_type', 'unknown')}")
        print(f"  置信度: {result.get('confidence', 0.0):.2f}")
        print(f"  处理系统: {result.get('processing_info', {}).get('system', 'unknown')}")

        return result.get('success', False)

    except Exception as e:
        print(f"简化图测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
