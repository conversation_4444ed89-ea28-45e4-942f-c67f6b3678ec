"""
AI筛选节点

基于用户档案对候选动作进行智能筛选和个性化定制
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
from app.services.llm_proxy_service import LLMProxyService

logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

async def ai_filtering_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """
    AI筛选节点

    基于用户档案对候选动作进行智能筛选和个性化定制
    """
    try:
        flow_state = state.get("flow_state", {})
        candidate_exercises = flow_state.get("candidate_exercises", [])
        user_profile = state.get("user_profile", {})
        training_params = state.get("training_params", {})

        logger.info(f"AI筛选 - 候选动作数量: {len(candidate_exercises)}")

        if not candidate_exercises:
            logger.warning("没有候选动作可供筛选")
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["filtered_exercises"] = []
            state["flow_state"]["stage"] = "response_generation"
            return state

        # 执行AI筛选
        filtered_exercises = await filter_exercises_with_ai(
            candidate_exercises, user_profile, training_params
        )

        if filtered_exercises:
            # 更新流程状态
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["filtered_exercises"] = filtered_exercises
            state["flow_state"]["stage"] = "response_generation"

            logger.info(f"AI筛选完成，筛选出 {len(filtered_exercises)} 个动作")
            return state
        else:
            # 筛选失败，使用基础筛选
            logger.warning("AI筛选失败，使用基础筛选")
            basic_filtered = basic_filter_exercises(candidate_exercises, user_profile)

            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["filtered_exercises"] = basic_filtered
            state["flow_state"]["stage"] = "response_generation"

            return state

    except Exception as e:
        logger.error(f"AI筛选失败: {str(e)}")

        # 错误处理，使用基础筛选
        flow_state = state.get("flow_state", {})
        candidate_exercises = flow_state.get("candidate_exercises", [])
        user_profile = state.get("user_profile", {})

        basic_filtered = basic_filter_exercises(candidate_exercises, user_profile)

        if "flow_state" not in state:
            state["flow_state"] = {}
        state["flow_state"]["filtered_exercises"] = basic_filtered
        state["flow_state"]["stage"] = "response_generation"

        StateUtils.set_error(state, f"AI筛选失败，使用基础筛选: {str(e)}")
        return state

def route_from_ai_filtering(state: UnifiedFitnessState) -> str:
    """从AI筛选节点的条件路由函数"""
    try:
        from langgraph.graph import END

        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        if stage == "response_generation":
            return "response_generation"
        else:
            return END

    except Exception as e:
        logger.error(f"AI筛选路由条件失败: {str(e)}")
        from langgraph.graph import END
        return END

async def filter_exercises_with_ai(
    candidate_exercises: List[Dict[str, Any]],
    user_profile: Dict[str, Any],
    training_params: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    使用AI对候选动作进行智能筛选

    Args:
        candidate_exercises: 候选动作列表
        user_profile: 用户档案
        training_params: 训练参数

    Returns:
        筛选后的动作列表
    """
    try:
        # 构建AI筛选提示词
        prompt = build_filtering_prompt(candidate_exercises, user_profile, training_params)

        # 调用LLM进行筛选
        response = await llm_service.aget_chat_response(
            messages=[
                {"role": "system", "content": "你是一个专业的健身教练，负责根据用户档案筛选最适合的运动动作。"},
                {"role": "user", "content": prompt}
            ],
            model="agent-app",
            temperature=0.3
        )

        # 解析AI响应
        filtered_exercises = parse_ai_filtering_response(response, candidate_exercises, user_profile)

        logger.info(f"AI筛选成功，从 {len(candidate_exercises)} 个动作中筛选出 {len(filtered_exercises)} 个")
        return filtered_exercises

    except Exception as e:
        logger.error(f"AI筛选异常: {str(e)}")
        return []

def build_filtering_prompt(
    candidate_exercises: List[Dict[str, Any]],
    user_profile: Dict[str, Any],
    training_params: Dict[str, Any]
) -> str:
    """构建AI筛选提示词"""

    # 用户档案信息
    profile_info = []
    if user_profile.get("fitness_level"):
        profile_info.append(f"健身水平: {user_profile['fitness_level']}")
    if user_profile.get("fitness_goal"):
        profile_info.append(f"健身目标: {user_profile['fitness_goal']}")
    if user_profile.get("age"):
        profile_info.append(f"年龄: {user_profile['age']}岁")

    profile_str = "、".join(profile_info) if profile_info else "未提供详细信息"

    # 训练参数
    body_part = training_params.get("body_part", "未指定")
    scenario = training_params.get("scenario", "未指定")

    # 候选动作信息
    exercises_info = []
    for i, exercise in enumerate(candidate_exercises[:10], 1):  # 限制最多10个
        exercises_info.append(
            f"{i}. {exercise.get('name', '未知动作')} - "
            f"难度: {exercise.get('difficulty_level', 1)}/5, "
            f"描述: {exercise.get('description', '无描述')[:50]}..."
        )

    exercises_str = "\n".join(exercises_info)

    prompt = f"""
请根据用户档案从候选动作中筛选出最适合的1-3个动作，并为每个动作设置个性化的训练参数。

用户档案：{profile_str}
训练目标：{body_part}训练
训练场景：{scenario}

候选动作：
{exercises_str}

请按以下格式返回筛选结果：

选择动作：[动作编号]
动作名称：[动作名称]
推荐组数：[数字]
推荐次数：[数字或范围]
组间休息：[秒数]
个性化建议：[针对用户的具体建议]

---

选择动作：[动作编号]
...

筛选标准：
1. 根据用户健身水平选择合适难度的动作
2. 根据健身目标调整训练参数
3. 考虑年龄因素确保安全性
4. 优先选择效果好、安全性高的动作
"""

    return prompt

def parse_ai_filtering_response(
    ai_response: str,
    candidate_exercises: List[Dict[str, Any]],
    user_profile: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """解析AI筛选响应"""

    try:
        filtered_exercises = []

        # 简单解析AI响应
        lines = ai_response.split('\n')
        current_exercise = {}

        for line in lines:
            line = line.strip()
            if not line or line == "---":
                if current_exercise and "name" in current_exercise:
                    filtered_exercises.append(current_exercise)
                    current_exercise = {}
                continue

            if line.startswith("选择动作："):
                try:
                    exercise_num = int(line.split("：")[1]) - 1
                    if 0 <= exercise_num < len(candidate_exercises):
                        current_exercise = candidate_exercises[exercise_num].copy()
                except (ValueError, IndexError):
                    continue

            elif line.startswith("推荐组数："):
                try:
                    sets = int(line.split("：")[1])
                    current_exercise["sets"] = sets
                except ValueError:
                    current_exercise["sets"] = 3

            elif line.startswith("推荐次数："):
                reps_str = line.split("：")[1]
                current_exercise["reps"] = reps_str

            elif line.startswith("组间休息："):
                try:
                    rest = int(line.split("：")[1].replace("秒", ""))
                    current_exercise["rest_seconds"] = rest
                except ValueError:
                    current_exercise["rest_seconds"] = 60

            elif line.startswith("个性化建议："):
                advice = line.split("：")[1]
                current_exercise["personalized_advice"] = advice

        # 添加最后一个动作
        if current_exercise and "name" in current_exercise:
            filtered_exercises.append(current_exercise)

        # 如果AI解析失败，使用基础筛选
        if not filtered_exercises:
            logger.warning("AI响应解析失败，使用基础筛选")
            return basic_filter_exercises(candidate_exercises, user_profile)

        return filtered_exercises

    except Exception as e:
        logger.error(f"AI响应解析失败: {str(e)}")
        return basic_filter_exercises(candidate_exercises, user_profile)

def basic_filter_exercises(
    candidate_exercises: List[Dict[str, Any]],
    user_profile: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """基础筛选逻辑（当AI筛选失败时使用）"""

    if not candidate_exercises:
        return []

    # 根据用户健身水平筛选
    fitness_level = user_profile.get("fitness_level", "初级")
    fitness_goal = user_profile.get("fitness_goal", "健康")

    # 难度级别映射
    level_mapping = {
        "初级": [1, 2],
        "中级": [2, 3],
        "高级": [3, 4, 5]
    }

    suitable_levels = level_mapping.get(fitness_level, [1, 2])

    # 筛选合适难度的动作
    suitable_exercises = [
        ex for ex in candidate_exercises
        if ex.get("difficulty_level", 1) in suitable_levels
    ]

    # 如果没有合适的，选择最简单的
    if not suitable_exercises:
        suitable_exercises = sorted(
            candidate_exercises,
            key=lambda x: x.get("difficulty_level", 1)
        )[:2]

    # 选择前1-2个动作
    selected = suitable_exercises[:2] if len(suitable_exercises) > 1 else suitable_exercises[:1]

    # 添加基础训练参数
    for exercise in selected:
        if fitness_level == "初级":
            exercise["sets"] = 3
            exercise["reps"] = "8-12"
            exercise["rest_seconds"] = 90
        elif fitness_level == "中级":
            exercise["sets"] = 4
            exercise["reps"] = "10-15"
            exercise["rest_seconds"] = 75
        else:  # 高级
            exercise["sets"] = 4
            exercise["reps"] = "12-20"
            exercise["rest_seconds"] = 60

        # 添加基础建议
        exercise["personalized_advice"] = f"根据您的{fitness_level}水平和{fitness_goal}目标定制"

        # 添加注意事项
        if not exercise.get("notes"):
            exercise["notes"] = f"适合{fitness_level}水平，注意动作标准"

    logger.info(f"基础筛选完成，选择了 {len(selected)} 个动作")
    return selected
