"""
数据库查询节点

负责从真实数据库查询运动动作数据
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils

logger = logging.getLogger(__name__)

async def database_query_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """
    数据库查询节点

    根据训练参数从数据库查询候选运动动作
    """
    try:
        training_params = state.get("training_params", {})
        body_part = training_params.get("body_part", "")
        scenario = training_params.get("scenario", "")

        logger.info(f"数据库查询 - 身体部位: {body_part}, 训练场景: {scenario}")

        # 查询数据库获取候选动作
        candidate_exercises = await query_exercises_from_database(body_part, scenario)

        if candidate_exercises:
            # 更新流程状态
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["candidate_exercises"] = candidate_exercises
            state["flow_state"]["stage"] = "ai_filtering"

            logger.info(f"数据库查询成功，获得 {len(candidate_exercises)} 个候选动作")
            return state
        else:
            # 没有找到合适的动作，使用默认推荐
            logger.warning(f"数据库查询未找到匹配动作，使用默认推荐")

            # 生成默认推荐
            default_exercises = get_default_exercises(body_part, scenario)

            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["candidate_exercises"] = default_exercises
            state["flow_state"]["stage"] = "ai_filtering"

            return state

    except Exception as e:
        logger.error(f"数据库查询失败: {str(e)}")

        # 错误处理，使用默认推荐
        training_params = state.get("training_params", {})
        body_part = training_params.get("body_part", "胸部")
        scenario = training_params.get("scenario", "健身房")

        default_exercises = get_default_exercises(body_part, scenario)

        if "flow_state" not in state:
            state["flow_state"] = {}
        state["flow_state"]["candidate_exercises"] = default_exercises
        state["flow_state"]["stage"] = "ai_filtering"

        StateUtils.set_error(state, f"数据库查询失败，使用默认推荐: {str(e)}")
        return state

def route_from_database_query(state: UnifiedFitnessState) -> str:
    """从数据库查询节点的条件路由函数"""
    try:
        from langgraph.graph import END

        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")
        candidate_exercises = flow_state.get("candidate_exercises", [])

        if stage == "ai_filtering" and candidate_exercises:
            return "ai_filtering"
        else:
            return END

    except Exception as e:
        logger.error(f"数据库查询路由条件失败: {str(e)}")
        from langgraph.graph import END
        return END

async def query_exercises_from_database(body_part: str, scenario: str) -> List[Dict[str, Any]]:
    """
    从数据库查询运动动作

    Args:
        body_part: 身体部位
        scenario: 训练场景

    Returns:
        候选动作列表
    """
    try:
        # 导入数据库相关模块
        from app.crud.crud_exercise import CRUDExercise
        from app.models.exercise import Exercise
        from app.db.session import get_db

        # 身体部位映射
        body_part_mapping = {
            "胸部": ["chest", "胸"],
            "腹部": ["abs", "abdominal", "腹"],
            "背部": ["back", "背"],
            "腿部": ["legs", "leg", "腿"],
            "肩部": ["shoulders", "shoulder", "肩"],
            "手臂": ["arms", "arm", "手臂", "biceps", "triceps"]
        }

        # 训练场景映射
        scenario_mapping = {
            "健身房": ["gym", "健身房", "器械"],
            "居家": ["home", "居家", "徒手"],
            "户外": ["outdoor", "户外", "公园"]
        }

        # 获取数据库会话
        db = next(get_db())
        exercise_crud = CRUDExercise(Exercise)

        # 构建查询条件
        search_terms = []

        # 添加身体部位搜索词
        if body_part in body_part_mapping:
            search_terms.extend(body_part_mapping[body_part])

        # 添加场景搜索词
        if scenario in scenario_mapping:
            search_terms.extend(scenario_mapping[scenario])

        # 执行数据库查询
        exercises = []

        # 使用search方法进行搜索
        for term in search_terms:
            # 搜索包含关键词的动作
            search_results = exercise_crud.search(db, keyword=term, limit=10)
            exercises.extend(search_results)

        # 去重并转换为字典格式
        unique_exercises = {}
        for exercise in exercises:
            if exercise.id not in unique_exercises:
                # 获取详细信息
                instructions = []
                if hasattr(exercise, 'details') and exercise.details:
                    instructions = exercise.details.ex_instructions or []

                unique_exercises[exercise.id] = {
                    "id": exercise.id,
                    "name": exercise.name,
                    "description": exercise.description or "",
                    "instructions": instructions,
                    "muscle_groups": exercise.body_part_id or [],
                    "equipment": exercise.equipment_id or [],
                    "difficulty_level": exercise.level or 1,
                    "category": exercise.exercise_type or "",
                    "body_parts": exercise.body_part_id or []
                }

        result = list(unique_exercises.values())

        # 限制返回数量
        if len(result) > 20:
            result = result[:20]

        logger.info(f"数据库查询成功，找到 {len(result)} 个动作")
        return result

    except Exception as e:
        logger.error(f"数据库查询异常: {str(e)}")
        return []

def get_default_exercises(body_part: str, scenario: str) -> List[Dict[str, Any]]:
    """
    获取默认推荐动作（当数据库查询失败时使用）

    Args:
        body_part: 身体部位
        scenario: 训练场景

    Returns:
        默认动作列表
    """
    default_exercises = {
        "胸部": {
            "健身房": [
                {
                    "id": "default_chest_gym_1",
                    "name": "杠铃卧推",
                    "description": "经典胸肌训练动作，使用杠铃进行卧推训练",
                    "instructions": ["躺在卧推凳上", "双手握住杠铃", "缓慢下降至胸部", "用力推起至起始位置"],
                    "muscle_groups": ["胸大肌", "三角肌前束", "肱三头肌"],
                    "equipment": ["杠铃", "卧推凳"],
                    "difficulty_level": 2,
                    "category": "力量训练"
                },
                {
                    "id": "default_chest_gym_2",
                    "name": "哑铃飞鸟",
                    "description": "胸肌塑形动作，使用哑铃进行飞鸟训练",
                    "instructions": ["躺在卧推凳上", "双手持哑铃", "手臂微弯向两侧打开", "收缩胸肌将哑铃合拢"],
                    "muscle_groups": ["胸大肌"],
                    "equipment": ["哑铃", "卧推凳"],
                    "difficulty_level": 2,
                    "category": "力量训练"
                }
            ],
            "居家": [
                {
                    "id": "default_chest_home_1",
                    "name": "标准俯卧撑",
                    "description": "基础胸肌训练动作，适合各个水平的训练者",
                    "instructions": ["双手撑地，身体保持直线", "下降至胸部接近地面", "推起至起始位置"],
                    "muscle_groups": ["胸大肌", "三角肌前束", "肱三头肌"],
                    "equipment": [],
                    "difficulty_level": 1,
                    "category": "徒手训练"
                }
            ]
        },
        "腹部": {
            "健身房": [
                {
                    "id": "default_abs_gym_1",
                    "name": "悬垂举腿",
                    "description": "高强度腹肌训练动作",
                    "instructions": ["悬挂在单杠上", "收紧腹肌", "将腿举起至水平位置", "缓慢放下"],
                    "muscle_groups": ["腹直肌", "腹斜肌"],
                    "equipment": ["单杠"],
                    "difficulty_level": 3,
                    "category": "力量训练"
                }
            ],
            "居家": [
                {
                    "id": "default_abs_home_1",
                    "name": "卷腹",
                    "description": "基础腹肌训练动作",
                    "instructions": ["仰卧，膝盖弯曲", "双手放在头后", "收缩腹肌抬起上身", "缓慢放下"],
                    "muscle_groups": ["腹直肌"],
                    "equipment": [],
                    "difficulty_level": 1,
                    "category": "徒手训练"
                }
            ]
        }
    }

    # 获取对应的默认动作
    if body_part in default_exercises and scenario in default_exercises[body_part]:
        return default_exercises[body_part][scenario]
    else:
        # 返回通用默认动作
        return [
            {
                "id": "default_general_1",
                "name": "标准俯卧撑",
                "description": "基础全身训练动作",
                "instructions": ["双手撑地", "身体保持直线", "推起至起始位置"],
                "muscle_groups": ["胸大肌", "三角肌", "肱三头肌"],
                "equipment": [],
                "difficulty_level": 1,
                "category": "徒手训练"
            }
        ]
