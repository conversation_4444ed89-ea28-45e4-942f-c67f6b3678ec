"""
并行混合处理节点

使用Send对象支持并行调用多个处理器，实现混合处理策略。
"""

import logging
import time
import asyncio
from typing import Dict, Any, List
try:
    from langgraph.graph import Send
except ImportError:
    try:
        from langgraph import Send
    except ImportError:
        # 如果Send不可用，创建一个简单的替代
        class Send:
            def __init__(self, node: str, state):
                self.node = node
                self.state = state

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState

logger = logging.getLogger(__name__)

async def hybrid_processor_node(state: UnifiedFitnessState) -> Dict[str, Any]:
    """
    并行混合处理节点

    根据路由决策并行调用多个处理器

    Args:
        state: 当前状态

    Returns:
        更新后的状态
    """
    start_time = time.time()

    try:
        from app.services.ai_assistant.langgraph.nodes.processor_nodes import (
            enhanced_processor_node,
            legacy_processor_node,
            state_machine_processor_node
        )

        # 获取路由决策
        routing_decision = state.routing_decision
        if not routing_decision:
            logger.warning("没有路由决策，使用默认并行策略")
            processors = ["state_machine", "enhanced"]
        else:
            route = routing_decision.get("route", "hybrid")
            confidence = routing_decision.get("confidence", 0.5)

            if route == "hybrid" or confidence < 0.7:
                # 混合模式：选择多个处理器
                processors = _select_processors_for_hybrid(state, routing_decision)
            else:
                # 单一路由模式
                processors = [route]

        # 并行执行处理器
        results = []
        tasks = []

        for processor in processors:
            if processor == "enhanced":
                tasks.append(enhanced_processor_node(state))
            elif processor == "legacy":
                tasks.append(legacy_processor_node(state))
            elif processor == "state_machine":
                tasks.append(state_machine_processor_node(state))

        # 等待所有任务完成
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        parallel_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"处理器{processors[i]}执行失败: {str(result)}")
                continue

            # 转换为并行结果格式
            parallel_result = {
                "source": result.get("processing_system", processors[i]),
                "content": result.get("response_content", ""),
                "confidence": result.get("confidence", 0.0),
                "intent": result.get("intent", ""),
                "structured_data": result.get("structured_data", {}),
                "execution_time": result.get("node_execution_times", {}).get(result.get("current_node", ""), 0.0),
                "timestamp": time.time()
            }
            parallel_results.append(parallel_result)

        # 记录处理时间
        execution_time = time.time() - start_time

        logger.info(f"混合处理完成，获得{len(parallel_results)}个结果")

        return {
            "current_node": "hybrid_processor",
            "processing_system": "hybrid",
            "parallel_results": parallel_results,
            "node_execution_times": {
                **state.node_execution_times,
                "hybrid_processor": execution_time
            }
        }

    except Exception as e:
        logger.error(f"并行混合处理节点失败: {str(e)}")
        execution_time = time.time() - start_time

        return {
            "current_node": "hybrid_processor",
            "processing_system": "hybrid",
            "last_error": f"混合处理失败: {str(e)}",
            "error_count": state.error_count + 1,
            "response_content": "混合处理失败，请稍后重试。",
            "node_execution_times": {
                **state.node_execution_times,
                "hybrid_processor": execution_time
            }
        }

def _select_processors_for_hybrid(state: UnifiedFitnessState, routing_decision: Dict[str, Any]) -> List[str]:
    """
    为混合模式选择处理器

    Args:
        state: 当前状态
        routing_decision: 路由决策

    Returns:
        处理器列表
    """
    try:
        # 分析路由评分，选择最佳的2-3个处理器
        scores = routing_decision.get("scores", {})

        # 按评分排序
        sorted_routes = sorted(scores.items(), key=lambda x: x[1], reverse=True)

        # 选择前2-3个评分较高的处理器
        selected_routes = []
        for route, score in sorted_routes:
            if score > 0.2:  # 只选择评分超过阈值的处理器
                selected_routes.append(route)
            if len(selected_routes) >= 3:  # 最多3个处理器
                break

        # 确保至少有一个处理器
        if not selected_routes:
            selected_routes = ["state_machine"]

        logger.info(f"混合模式选择了处理器: {selected_routes}")
        return selected_routes

    except Exception as e:
        logger.error(f"选择混合处理器失败: {str(e)}")
        return ["state_machine"]

# 删除不需要的Send相关函数，因为我们使用直接并行执行

async def collect_parallel_results(state: UnifiedFitnessState) -> Dict[str, Any]:
    """
    收集并行处理结果

    这个函数会在所有并行处理器完成后被调用

    Args:
        state: 包含并行结果的状态

    Returns:
        更新后的状态
    """
    start_time = time.time()

    try:
        # 检查是否有并行结果
        if not state.parallel_results:
            logger.warning("没有并行处理结果")
            return {
                "current_node": "collect_results",
                "response_content": "没有收到处理结果，请稍后重试。",
                "confidence": 0.0
            }

        logger.info(f"收集到{len(state.parallel_results)}个并行处理结果")

        # 记录所有结果的详细信息
        for i, result in enumerate(state.parallel_results):
            source = result.get("source", "unknown")
            confidence = result.get("confidence", 0.0)
            logger.debug(f"结果{i+1}: 来源={source}, 置信度={confidence:.2f}")

        # 记录处理时间
        execution_time = time.time() - start_time

        return {
            "current_node": "collect_results",
            "processing_system": "hybrid_collection",
            "node_execution_times": {
                **state.node_execution_times,
                "collect_results": execution_time
            }
        }

    except Exception as e:
        logger.error(f"收集并行结果失败: {str(e)}")
        execution_time = time.time() - start_time

        return {
            "current_node": "collect_results",
            "last_error": f"收集结果失败: {str(e)}",
            "error_count": state.error_count + 1,
            "response_content": "收集处理结果时出现错误。",
            "node_execution_times": {
                **state.node_execution_times,
                "collect_results": execution_time
            }
        }

def add_parallel_result(state: UnifiedFitnessState, result: Dict[str, Any]) -> Dict[str, Any]:
    """
    添加并行处理结果到状态中

    这个函数会被各个处理器节点调用来添加它们的结果

    Args:
        state: 当前状态
        result: 处理结果

    Returns:
        更新后的状态
    """
    try:
        # 准备结果数据
        parallel_result = {
            "source": result.get("processing_system", "unknown"),
            "content": result.get("response_content", ""),
            "confidence": result.get("confidence", 0.0),
            "intent": result.get("intent", ""),
            "structured_data": result.get("structured_data", {}),
            "execution_time": result.get("node_execution_times", {}).get(result.get("current_node", ""), 0.0),
            "timestamp": time.time()
        }

        # 添加到并行结果列表
        updated_results = state.parallel_results + [parallel_result]

        logger.debug(f"添加并行结果: 来源={parallel_result['source']}, 置信度={parallel_result['confidence']:.2f}")

        return {
            "parallel_results": updated_results
        }

    except Exception as e:
        logger.error(f"添加并行结果失败: {str(e)}")
        return {
            "last_error": f"添加并行结果失败: {str(e)}",
            "error_count": state.error_count + 1
        }

def create_hybrid_strategy(
    complexity_analysis: Dict[str, Any],
    domain_analysis: Dict[str, Any],
    context_analysis: Dict[str, Any]
) -> Dict[str, Any]:
    """
    创建混合处理策略

    Args:
        complexity_analysis: 复杂度分析
        domain_analysis: 领域分析
        context_analysis: 上下文分析

    Returns:
        混合策略配置
    """
    strategy = {
        "processors": [],
        "weights": {},
        "timeout": 30,
        "min_confidence": 0.5
    }

    # 基于分析结果选择处理器
    complexity = complexity_analysis.get("complexity", "medium")
    domain = domain_analysis.get("primary_domain", "general")
    dependency = context_analysis.get("overall_dependency", "medium")

    # 复杂查询：增强处理器 + 状态机
    if complexity == "complex":
        strategy["processors"] = ["enhanced", "state_machine"]
        strategy["weights"] = {"enhanced": 0.7, "state_machine": 0.3}

    # 专业领域查询：传统处理器 + 增强处理器
    elif domain in ["fitness_specific", "nutrition"]:
        strategy["processors"] = ["legacy", "enhanced"]
        strategy["weights"] = {"legacy": 0.6, "enhanced": 0.4}

    # 简单查询：状态机 + 传统处理器
    elif complexity == "simple":
        strategy["processors"] = ["state_machine", "legacy"]
        strategy["weights"] = {"state_machine": 0.7, "legacy": 0.3}

    # 默认策略：全部处理器
    else:
        strategy["processors"] = ["enhanced", "legacy", "state_machine"]
        strategy["weights"] = {"enhanced": 0.4, "legacy": 0.3, "state_machine": 0.3}

    # 调整超时时间
    if complexity == "complex":
        strategy["timeout"] = 45
    elif complexity == "simple":
        strategy["timeout"] = 15

    logger.info(f"创建混合策略: 处理器={strategy['processors']}, 权重={strategy['weights']}")

    return strategy
