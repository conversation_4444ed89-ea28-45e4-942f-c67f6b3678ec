"""
意图路由节点

负责识别运动动作意图并提取初始参数
"""

import logging
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils

logger = logging.getLogger(__name__)

async def exercise_intent_router_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """
    运动意图路由节点
    
    识别运动动作查询意图并提取初始参数
    """
    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        logger.info(f"处理用户消息: {user_message}")

        # 初始化流程状态
        if "flow_state" not in state:
            state["flow_state"] = {}
        
        state["flow_state"].update({
            "stage": "intent_routing",
            "needs_user_info": True,
            "needs_training_params": True
        })

        # 提取初始参数
        initial_params = {}
        
        # 简单的关键词提取（更可靠）
        if "胸肌" in user_message:
            initial_params["body_part"] = "胸部"
        elif "腹肌" in user_message:
            initial_params["body_part"] = "腹部"
        elif "背" in user_message:
            initial_params["body_part"] = "背部"
        elif "腿" in user_message:
            initial_params["body_part"] = "腿部"
        elif "肩" in user_message:
            initial_params["body_part"] = "肩部"
        elif "手臂" in user_message:
            initial_params["body_part"] = "手臂"
        
        # 尝试使用参数提取器作为补充
        try:
            from app.services.conversation.parameter_extractor import ParameterExtractor
            extracted_params = await ParameterExtractor.extract_all_parameters(None, user_message)
            logger.info(f"参数提取器结果: {extracted_params}")
            
            # 转换body_parts列表为body_part字符串
            if "body_parts" in extracted_params and extracted_params["body_parts"]:
                if not initial_params.get("body_part"):
                    initial_params["body_part"] = extracted_params["body_parts"][0]
            
            # 合并其他有用的参数
            if "training_goal" in extracted_params and extracted_params["training_goal"]:
                initial_params["training_goal"] = extracted_params["training_goal"]
                
        except Exception as param_error:
            logger.warning(f"参数提取器失败: {str(param_error)}")
        
        state["flow_state"]["collected_params"] = initial_params
        logger.info(f"最终收集的初始参数: {initial_params}")

        # 运动动作关键词检测
        exercise_keywords = [
            "胸肌", "腹肌", "背肌", "腿部", "肩膀", "手臂", "臀部",
            "怎么练", "如何训练", "动作", "锻炼", "训练方法",
            "卧推", "深蹲", "硬拉", "俯卧撑", "仰卧起坐", "引体向上"
        ]
        
        # 检查是否是运动动作相关查询
        is_exercise_query = any(keyword in user_message for keyword in exercise_keywords)
        
        if is_exercise_query:
            route = "user_info_verification"
            confidence = 0.9
            reasoning = "检测到运动动作查询，开始参数收集流程"
        else:
            route = "general_response"
            confidence = 0.7
            reasoning = "未检测到运动动作查询，路由到通用响应"

        # 更新路由决策
        state["routing_decision"] = {
            "route": route,
            "confidence": confidence,
            "reasoning": reasoning
        }
        
        # 设置意图和置信度
        state["intent"] = "exercise_action" if is_exercise_query else "general_chat"
        state["confidence"] = confidence
        state["current_node"] = "exercise_intent_router"
        
        # 添加处理步骤
        StateUtils.add_processing_step(state, "exercise_intent_routing")
        
        logger.info(f"运动意图路由: {route} (置信度: {confidence:.2f})")
        return state

    except Exception as e:
        logger.error(f"运动意图路由失败: {str(e)}")
        state["current_node"] = "exercise_intent_router"
        state["routing_decision"] = {
            "route": "general_response",
            "confidence": 0.5,
            "reasoning": f"路由失败: {str(e)}"
        }
        StateUtils.set_error(state, f"运动意图路由失败: {str(e)}")
        return state

def route_from_router(state: UnifiedFitnessState) -> str:
    """从路由器节点的条件路由函数"""
    try:
        routing_decision = state.get("routing_decision", {})
        route = routing_decision.get("route", "general_response")
        
        logger.info(f"路由到: {route}")
        return route
        
    except Exception as e:
        logger.error(f"路由条件失败: {str(e)}")
        return "general_response"

async def general_response_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """通用响应节点"""
    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 生成通用响应
        if "健身房" in user_message or "居家" in user_message or "户外" in user_message:
            response_content = f"我理解您说的是\"{user_message}\"。\n\n作为您的健身AI助手，我专注于为您提供健身相关的帮助。如果您有任何关于：\n- 运动训练动作（如\"胸肌怎么练\"）\n- 健身计划制定\n- 营养饮食建议\n- 健康生活方式\n\n等方面的问题，我都很乐意为您解答！请告诉我您想了解什么。"
        else:
            response_content = "您好！我是您的专业健身AI助手，很高兴为您服务！\n\n我可以帮助您：\n🏋️ 制定个性化训练计划\n💪 推荐具体的运动动作\n🥗 提供营养饮食建议\n📊 追踪健身进度\n❓ 解答健身相关问题\n\n请告诉我您想了解什么健身相关的内容？"

        # 更新状态
        state["current_node"] = "general_response"
        state["response_content"] = response_content
        state["confidence"] = 0.8
        state["intent"] = "general_chat"

        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        if "messages" not in state:
            state["messages"] = []
        state["messages"].append(ai_message)

        logger.info("通用响应生成完成")
        return state

    except Exception as e:
        logger.error(f"通用响应生成失败: {str(e)}")
        error_response = "抱歉，我暂时无法处理您的请求。请稍后再试。"
        state["current_node"] = "general_response"
        state["response_content"] = error_response
        StateUtils.set_error(state, f"通用响应生成失败: {str(e)}")

        ai_message = AIMessage(content=error_response)
        if "messages" not in state:
            state["messages"] = []
        state["messages"].append(ai_message)

        return state
