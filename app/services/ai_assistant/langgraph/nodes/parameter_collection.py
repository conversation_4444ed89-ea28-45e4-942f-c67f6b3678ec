"""
参数收集节点

负责收集训练参数（body_part, scenario等）
"""

import logging
from typing import Dict, Any
from langchain_core.messages import AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils

logger = logging.getLogger(__name__)

async def parameter_collection_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """
    参数收集节点

    收集必要的训练参数：body_part, scenario
    """
    try:
        flow_state = state.get("flow_state", {})
        collected_params = flow_state.get("collected_params", {})

        logger.info(f"参数收集节点 - 当前收集的参数: {collected_params}")

        # 检查必要的训练参数
        required_params = ["body_part", "scenario"]
        missing_params = []

        for param in required_params:
            if param not in collected_params or not collected_params[param]:
                missing_params.append(param)

        logger.info(f"缺失的参数: {missing_params}")

        if missing_params:
            # 需要收集参数
            next_param = missing_params[0]

            # 更新流程状态
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["current_collecting_param"] = next_param
            state["flow_state"]["stage"] = "collecting_training_params"

            # 生成询问提示
            if next_param == "scenario":
                response_content = "请告诉我您想在哪里锻炼？\n\n🏠 居家训练\n🏋️ 健身房训练\n🏃 户外运动"
            elif next_param == "body_part":
                response_content = "请告诉我您想训练哪个部位？\n\n💪 胸部\n🏃 腿部\n🔙 背部\n🤲 手臂\n🏋️ 肩部\n💯 腹部"
            elif next_param == "equipment":
                response_content = "您希望使用什么器械？\n\n💪 徒手训练\n🏋️ 哑铃/杠铃\n🔧 健身器械\n🎾 其他器材"
            else:
                response_content = f"请提供您的{next_param}信息。"

            # 更新状态
            state["current_node"] = "parameter_collection"
            state["response_content"] = response_content
            state["confidence"] = 0.9

            # 添加AI响应
            ai_message = AIMessage(content=response_content)
            if "messages" not in state:
                state["messages"] = []
            state["messages"].append(ai_message)

            logger.info(f"需要收集训练参数: {next_param}")
            return state
        else:
            # 参数收集完成，进入数据库查询
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["needs_training_params"] = False
            state["flow_state"]["stage"] = "database_query"
            state["training_params"] = collected_params

            logger.info("参数收集完成，进入数据库查询")
            return state

    except Exception as e:
        logger.error(f"参数收集失败: {str(e)}")
        state["current_node"] = "parameter_collection"
        state["response_content"] = "抱歉，参数收集过程中出现问题，我将使用默认参数为您提供建议。"
        StateUtils.set_error(state, f"参数收集失败: {str(e)}")

        # 使用默认参数继续流程
        if "flow_state" not in state:
            state["flow_state"] = {}
        state["flow_state"]["stage"] = "database_query"
        return state

def route_from_parameter_collection(state: UnifiedFitnessState) -> str:
    """从参数收集节点的条件路由函数"""
    try:
        from langgraph.graph import END

        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        if stage == "collecting_training_params":
            # 需要等待用户输入，结束当前流程
            return END
        elif stage == "database_query":
            # 参数收集完成，进入数据库查询
            return "database_query"
        else:
            # 默认结束
            return END

    except Exception as e:
        logger.error(f"参数收集路由条件失败: {str(e)}")
        from langgraph.graph import END
        return END

async def handle_user_input_for_collection(state: UnifiedFitnessState, user_input: str) -> UnifiedFitnessState:
    """
    处理用户输入以收集参数

    Args:
        state: 当前状态
        user_input: 用户输入

    Returns:
        更新后的状态
    """
    try:
        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        # 检查是否在用户信息收集阶段
        if stage == "collecting_user_info":
            from app.services.ai_assistant.langgraph.nodes.user_verification import handle_user_info_input
            return await handle_user_info_input(state, user_input)

        # 检查是否在训练参数收集阶段
        elif stage == "collecting_training_params":
            return await handle_training_param_input(state, user_input)

        else:
            logger.warning(f"未知的收集阶段: {stage}")
            return state

    except Exception as e:
        logger.error(f"处理用户输入失败: {str(e)}")
        return state

async def handle_training_param_input(state: UnifiedFitnessState, user_input: str) -> UnifiedFitnessState:
    """
    处理训练参数输入

    Args:
        state: 当前状态
        user_input: 用户输入

    Returns:
        更新后的状态
    """
    try:
        flow_state = state.get("flow_state", {})
        current_param = flow_state.get("current_collecting_param", "")
        collected_params = flow_state.get("collected_params", {})

        if not current_param:
            logger.warning("没有当前收集参数，跳过处理")
            return state

        # 解析用户输入
        param_value = parse_training_param(current_param, user_input)

        if param_value:
            # 更新收集的参数
            collected_params[current_param] = param_value
            state["flow_state"]["collected_params"] = collected_params

            # 检查是否还有缺失参数
            required_params = ["body_part", "scenario"]
            missing_params = [p for p in required_params if p not in collected_params or not collected_params[p]]

            if missing_params:
                # 继续收集下一个参数
                next_param = missing_params[0]
                state["flow_state"]["current_collecting_param"] = next_param

                # 生成下一个参数的询问
                if next_param == "scenario":
                    response_content = f"好的，已记录您要训练{param_value}。\n\n请告诉我您想在哪里锻炼？\n\n🏠 居家训练\n🏋️ 健身房训练\n🏃 户外运动"
                elif next_param == "body_part":
                    response_content = f"好的，已记录训练场景为{param_value}。\n\n请告诉我您想训练哪个部位？\n\n💪 胸部\n🏃 腿部\n🔙 背部\n🤲 手臂\n🏋️ 肩部\n💯 腹部"
                else:
                    response_content = f"好的，已记录{current_param}为{param_value}。\n\n请提供您的{next_param}信息。"

                state["response_content"] = response_content
                ai_message = AIMessage(content=response_content)
                state["messages"].append(ai_message)

                logger.info(f"继续收集下一个参数: {next_param}")
            else:
                # 所有参数收集完成
                state["flow_state"]["stage"] = "database_query"
                state["flow_state"]["needs_training_params"] = False
                state["training_params"] = collected_params

                logger.info(f"训练参数收集完成: {collected_params}")
        else:
            # 输入无效，重新询问
            if current_param == "scenario":
                response_content = "抱歉，我没有理解您的训练场景。请选择以下选项之一：\n\n🏠 居家训练\n🏋️ 健身房训练\n🏃 户外运动"
            elif current_param == "body_part":
                response_content = "抱歉，我没有理解您要训练的部位。请选择以下选项之一：\n\n💪 胸部\n🏃 腿部\n🔙 背部\n🤲 手臂\n🏋️ 肩部\n💯 腹部"
            else:
                response_content = f"抱歉，我没有理解您的{current_param}信息。请重新输入。"

            state["response_content"] = response_content
            ai_message = AIMessage(content=response_content)
            state["messages"].append(ai_message)

            logger.info(f"用户输入无效，重新询问: {current_param}")

        return state

    except Exception as e:
        logger.error(f"处理训练参数输入失败: {str(e)}")

        # 错误处理，跳过当前参数
        error_response = "抱歉，处理您的输入时出现问题。让我们继续其他参数的收集。"
        state["response_content"] = error_response
        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)

        return state

def parse_training_param(param_name: str, user_input: str) -> str:
    """
    解析训练参数

    Args:
        param_name: 参数名称
        user_input: 用户输入

    Returns:
        解析后的参数值
    """
    user_input = user_input.strip().lower()

    if param_name == "scenario":
        if any(keyword in user_input for keyword in ["居家", "家", "家里", "在家"]):
            return "居家"
        elif any(keyword in user_input for keyword in ["健身房", "gym", "器械"]):
            return "健身房"
        elif any(keyword in user_input for keyword in ["户外", "公园", "室外"]):
            return "户外"
        else:
            return ""

    elif param_name == "body_part":
        if any(keyword in user_input for keyword in ["胸", "胸部", "胸肌"]):
            return "胸部"
        elif any(keyword in user_input for keyword in ["腹", "腹部", "腹肌", "肚子"]):
            return "腹部"
        elif any(keyword in user_input for keyword in ["背", "背部", "背肌"]):
            return "背部"
        elif any(keyword in user_input for keyword in ["腿", "腿部", "大腿", "小腿"]):
            return "腿部"
        elif any(keyword in user_input for keyword in ["肩", "肩部", "肩膀"]):
            return "肩部"
        elif any(keyword in user_input for keyword in ["手臂", "臂", "二头肌", "三头肌"]):
            return "手臂"
        else:
            return ""

    else:
        return user_input
