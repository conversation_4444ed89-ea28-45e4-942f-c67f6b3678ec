"""
处理器节点包装器

包装现有的处理器（增强版、传统、状态机）为LangGraph节点。
"""

import logging
import time
from typing import Dict, Any
from langchain_core.messages import AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState

logger = logging.getLogger(__name__)

async def enhanced_processor_node(state: UnifiedFitnessState) -> Dict[str, Any]:
    """
    增强处理器节点包装器

    Args:
        state: 当前状态

    Returns:
        更新后的状态
    """
    start_time = time.time()

    try:
        from app.services.ai_assistant.intent.adapters.enhanced_recognizer_adapter import EnhancedIntentRecognizerAdapter
        from app.services.ai_assistant.llm.factory import get_llm_proxy

        # 获取LLM代理
        llm_proxy = get_llm_proxy()

        # 创建增强识别器适配器
        adapter = EnhancedIntentRecognizerAdapter(llm_proxy)
        await adapter.initialize()

        # 获取用户消息
        user_message = ""
        if state.messages:
            for msg in reversed(state.messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 执行增强识别
        result = await adapter.arecognize(user_message)

        # 记录处理时间
        execution_time = time.time() - start_time

        # 创建响应内容
        response_content = f"通过增强处理器分析，我识别出您的意图是：{result.intent_type}。让我为您提供相应的帮助。"

        # 添加AI响应到消息历史
        ai_message = AIMessage(content=response_content)

        logger.info(f"增强处理器完成: {result.intent_type} (置信度: {result.confidence:.2f})")

        # 更新状态
        state.current_node = "enhanced_processor"
        state.processing_system = "enhanced"
        state.intent = result.intent_type
        state.confidence = result.confidence
        state.intent_parameters = result.parameters
        state.enhanced_intent_result = {
            "intent": result.intent_type,
            "confidence": result.confidence,
            "parameters": result.parameters,
            "source": "enhanced_recognizer"
        }
        state.response_content = response_content
        state.messages.append(ai_message)
        state.node_execution_times["enhanced_processor"] = execution_time
        state.add_processing_step("enhanced_processor_execution")

        return state

    except Exception as e:
        logger.error(f"增强处理器节点执行失败: {str(e)}")
        execution_time = time.time() - start_time

        error_response = "抱歉，增强处理器暂时无法处理您的请求，请稍后重试。"
        ai_message = AIMessage(content=error_response)

        # 更新状态
        state.current_node = "enhanced_processor"
        state.processing_system = "enhanced"
        state.set_error(f"增强处理器失败: {str(e)}")
        state.response_content = error_response
        state.messages.append(ai_message)
        state.node_execution_times["enhanced_processor"] = execution_time

        return state


async def legacy_processor_node(state: UnifiedFitnessState) -> Dict[str, Any]:
    """
    传统处理器节点包装器

    Args:
        state: 当前状态

    Returns:
        更新后的状态
    """
    start_time = time.time()

    try:
        from app.services.ai_assistant.intent.adapters.handler_factory_adapter import LegacyHandlerFactoryAdapter
        from app.services.ai_assistant.llm.factory import get_llm_proxy

        # 获取依赖
        llm_proxy = get_llm_proxy()
        # 注意：这里需要实际的数据库会话，在实际使用中需要传入
        db = None  # 在实际集成时需要正确获取

        # 创建传统处理器适配器
        adapter = LegacyHandlerFactoryAdapter(db, llm_proxy)
        await adapter.initialize()

        # 准备输入数据
        input_data = {
            "intent_type": state.intent or "general_chat",
            "message": "",
            "context": {
                "conversation_id": state.conversation_id,
                "user_id": state.user_id,
                "user_profile": state.user_profile
            }
        }

        # 获取用户消息
        if state.messages:
            for msg in reversed(state.messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    input_data["message"] = msg.content
                    break

        # 检查是否可以处理该意图
        can_handle = await adapter.can_handle(input_data["intent_type"])
        if not can_handle:
            # 如果不能处理，返回通用响应
            response_content = "传统处理器无法处理此类请求，建议使用其他处理方式。"
        else:
            # 执行传统处理
            result = await adapter.process(input_data)
            response_content = result.get("content", "传统处理器已处理您的请求。")

        # 记录处理时间
        execution_time = time.time() - start_time

        # 添加AI响应到消息历史
        ai_message = AIMessage(content=response_content)

        logger.info(f"传统处理器完成: {input_data['intent_type']}")

        return {
            "current_node": "legacy_processor",
            "processing_system": "legacy",
            "intent": input_data["intent_type"],
            "confidence": 0.8,
            "response_content": response_content,
            "structured_data": {},
            "messages": state.messages + [ai_message],
            "node_execution_times": {
                **state.node_execution_times,
                "legacy_processor": execution_time
            }
        }

    except Exception as e:
        logger.error(f"传统处理器节点执行失败: {str(e)}")
        execution_time = time.time() - start_time

        error_response = "抱歉，传统处理器暂时无法处理您的请求，请稍后重试。"
        ai_message = AIMessage(content=error_response)

        return {
            "current_node": "legacy_processor",
            "processing_system": "legacy",
            "last_error": f"传统处理器失败: {str(e)}",
            "error_count": state.error_count + 1,
            "response_content": error_response,
            "messages": state.messages + [ai_message],
            "node_execution_times": {
                **state.node_execution_times,
                "legacy_processor": execution_time
            }
        }


async def state_machine_processor_node(state: UnifiedFitnessState) -> Dict[str, Any]:
    """
    状态机处理器节点包装器

    Args:
        state: 当前状态

    Returns:
        更新后的状态
    """
    start_time = time.time()

    try:
        from app.services.ai_assistant.conversation.states.manager import conversation_state_manager

        # 获取当前状态处理器
        current_state = conversation_state_manager.get_current_state(
            state.current_state_name or "idle"
        )

        # 获取用户消息
        user_message = ""
        if state.messages:
            for msg in reversed(state.messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 准备状态机输入
        state_input = {
            "message": user_message,
            "user_profile": state.user_profile,
            "conversation_history": state.conversation_history,
            "context": {
                "conversation_id": state.conversation_id,
                "user_id": state.user_id
            }
        }

        # 执行状态机处理
        result = await current_state.handle_message(state_input)

        # 记录处理时间
        execution_time = time.time() - start_time

        # 获取响应内容
        response_content = result.get("response", "状态机已处理您的请求。")

        # 添加AI响应到消息历史
        ai_message = AIMessage(content=response_content)

        logger.info(f"状态机处理器完成: {state.current_state_name}")

        return {
            "current_node": "state_machine_processor",
            "processing_system": "state_machine",
            "response_content": response_content,
            "confidence": result.get("confidence", 0.8),
            "current_state_name": result.get("next_state", state.current_state_name),
            "messages": state.messages + [ai_message],
            "node_execution_times": {
                **state.node_execution_times,
                "state_machine_processor": execution_time
            }
        }

    except Exception as e:
        logger.error(f"状态机处理器节点执行失败: {str(e)}")
        execution_time = time.time() - start_time

        error_response = "抱歉，状态机处理器暂时无法处理您的请求，请稍后重试。"
        ai_message = AIMessage(content=error_response)

        return {
            "current_node": "state_machine_processor",
            "processing_system": "state_machine",
            "last_error": f"状态机处理器失败: {str(e)}",
            "error_count": state.error_count + 1,
            "response_content": error_response,
            "messages": state.messages + [ai_message],
            "node_execution_times": {
                **state.node_execution_times,
                "state_machine_processor": execution_time
            }
        }
