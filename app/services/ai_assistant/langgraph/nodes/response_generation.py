"""
响应生成节点

生成最终的结构化训练建议响应
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils

logger = logging.getLogger(__name__)

async def response_generation_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """
    响应生成节点
    
    基于筛选后的动作生成最终的结构化训练建议
    """
    try:
        flow_state = state.get("flow_state", {})
        filtered_exercises = flow_state.get("filtered_exercises", [])
        user_profile = state.get("user_profile", {})
        training_params = state.get("training_params", {})
        
        logger.info(f"响应生成 - 筛选后动作数量: {len(filtered_exercises)}")
        
        if not filtered_exercises:
            # 没有筛选后的动作，生成默认响应
            response_content = generate_default_response(training_params)
            structured_data = {}
        else:
            # 生成详细的训练建议
            response_content = generate_detailed_response(
                filtered_exercises, user_profile, training_params
            )
            structured_data = generate_structured_data(
                filtered_exercises, user_profile, training_params
            )
        
        # 更新状态
        state["current_node"] = "response_generation"
        state["response_content"] = response_content
        state["confidence"] = 0.95
        state["structured_data"] = structured_data
        
        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        if "messages" not in state:
            state["messages"] = []
        state["messages"].append(ai_message)
        
        logger.info(f"响应生成完成，响应长度: {len(response_content)} 字符")
        return state

    except Exception as e:
        logger.error(f"响应生成失败: {str(e)}")
        
        # 错误处理，生成基础响应
        training_params = state.get("training_params", {})
        error_response = generate_error_response(training_params)
        
        state["current_node"] = "response_generation"
        state["response_content"] = error_response
        state["confidence"] = 0.5
        StateUtils.set_error(state, f"响应生成失败: {str(e)}")
        
        ai_message = AIMessage(content=error_response)
        if "messages" not in state:
            state["messages"] = []
        state["messages"].append(ai_message)
        
        return state

def generate_detailed_response(
    filtered_exercises: List[Dict[str, Any]], 
    user_profile: Dict[str, Any], 
    training_params: Dict[str, Any]
) -> str:
    """生成详细的训练建议响应"""
    
    body_part = training_params.get("body_part", "目标部位")
    scenario = training_params.get("scenario", "训练场景")
    user_name = user_profile.get("name", "")
    
    # 构建响应开头
    greeting = f"您好{user_name}！\n" if user_name else ""
    intro = f"根据您的{body_part}训练需求和{scenario}场景，我为您推荐以下{len(filtered_exercises)}个动作：\n\n"
    
    response_parts = [greeting + intro]
    
    # 添加每个动作的详细信息
    for i, exercise in enumerate(filtered_exercises, 1):
        exercise_section = format_exercise_details(exercise, i)
        response_parts.append(exercise_section)
    
    # 添加训练建议
    training_advice = generate_training_advice(user_profile, training_params)
    response_parts.append(f"\n**训练建议：**\n{training_advice}")
    
    return "\n".join(response_parts)

def format_exercise_details(exercise: Dict[str, Any], index: int) -> str:
    """格式化单个动作的详细信息"""
    
    name = exercise.get("name", "未知动作")
    description = exercise.get("description", "")
    sets = exercise.get("sets", 3)
    reps = exercise.get("reps", "8-12")
    rest_seconds = exercise.get("rest_seconds", 60)
    instructions = exercise.get("instructions", [])
    personalized_advice = exercise.get("personalized_advice", "")
    
    # 构建动作详情
    details = [f"**{index}. {name}**"]
    
    if description:
        details.append(f"   📝 {description}")
    
    details.append(f"   💪 训练参数：{sets}组 × {reps}次，组间休息{rest_seconds}秒")
    
    if instructions:
        details.append("   🎯 动作要点：")
        for instruction in instructions[:3]:  # 最多显示3个要点
            details.append(f"      • {instruction}")
    
    if personalized_advice:
        details.append(f"   💡 {personalized_advice}")
    
    return "\n".join(details)

def generate_training_advice(user_profile: Dict[str, Any], training_params: Dict[str, Any]) -> str:
    """生成训练建议"""
    
    advice_parts = []
    
    # 基础建议
    advice_parts.extend([
        "• 训练前充分热身，训练后进行拉伸",
        "• 注意动作标准，循序渐进增加强度",
        "• 保证充足休息，每周训练2-3次"
    ])
    
    # 根据健身目标添加建议
    fitness_goal = user_profile.get("fitness_goal", "")
    if fitness_goal == "增肌":
        advice_parts.append("• 增肌期间注意蛋白质摄入，建议每公斤体重1.6-2.2g")
    elif fitness_goal == "减脂":
        advice_parts.append("• 减脂期间控制饮食，配合有氧运动效果更佳")
    elif fitness_goal == "力量":
        advice_parts.append("• 力量训练注重重量递增，确保动作质量")
    
    # 根据健身水平添加建议
    fitness_level = user_profile.get("fitness_level", "")
    if fitness_level == "初级":
        advice_parts.append("• 初学者建议从轻重量开始，重点掌握动作技巧")
    elif fitness_level == "高级":
        advice_parts.append("• 可以尝试更多变化动作，增加训练强度")
    
    return "\n".join(advice_parts)

def generate_structured_data(
    filtered_exercises: List[Dict[str, Any]], 
    user_profile: Dict[str, Any], 
    training_params: Dict[str, Any]
) -> Dict[str, Any]:
    """生成结构化数据"""
    
    return {
        "exercise_recommendations": filtered_exercises,
        "user_profile_summary": {
            "fitness_level": user_profile.get("fitness_level", "未知"),
            "fitness_goal": user_profile.get("fitness_goal", "未知"),
            "age": user_profile.get("age", "未知"),
            "gender": user_profile.get("gender", "未知")
        },
        "training_parameters": training_params,
        "recommendation_count": len(filtered_exercises),
        "confidence_score": 0.95,
        "generated_at": StateUtils.get_current_timestamp()
    }

def generate_default_response(training_params: Dict[str, Any]) -> str:
    """生成默认响应（当没有筛选结果时）"""
    
    body_part = training_params.get("body_part", "目标部位")
    scenario = training_params.get("scenario", "")
    
    scenario_text = f"在{scenario}" if scenario else ""
    
    return f"""抱歉，暂时没有找到适合您的{body_part}训练动作{scenario_text}。

建议您：
• 尝试调整训练场景或目标部位
• 咨询专业健身教练获得个性化指导
• 从基础动作开始，逐步提升训练水平

如果您有其他健身相关问题，我很乐意为您解答！"""

def generate_error_response(training_params: Dict[str, Any]) -> str:
    """生成错误响应"""
    
    body_part = training_params.get("body_part", "")
    
    if body_part:
        return f"抱歉，在为您生成{body_part}训练建议时出现了问题。请稍后再试，或者尝试重新描述您的训练需求。"
    else:
        return "抱歉，在生成训练建议时出现了问题。请稍后再试，或者重新开始对话。"

# 历史数据分析和智能推荐优化相关函数

async def analyze_user_history(user_id: str) -> Dict[str, Any]:
    """
    分析用户历史数据
    
    Args:
        user_id: 用户ID
        
    Returns:
        用户历史分析结果
    """
    try:
        # 这里可以添加用户历史数据分析逻辑
        # 例如：分析用户过往的训练记录、偏好等
        
        # 暂时返回空结果，后续可以扩展
        return {
            "training_frequency": "未知",
            "preferred_exercises": [],
            "progress_trend": "未知",
            "last_training_date": None
        }
        
    except Exception as e:
        logger.error(f"分析用户历史数据失败: {str(e)}")
        return {}

async def collect_feedback(user_id: str, exercise_id: str, feedback_data: Dict[str, Any]) -> bool:
    """
    收集用户训练效果反馈
    
    Args:
        user_id: 用户ID
        exercise_id: 动作ID
        feedback_data: 反馈数据
        
    Returns:
        收集是否成功
    """
    try:
        # 这里可以添加反馈收集逻辑
        # 例如：保存到数据库、更新推荐算法等
        
        logger.info(f"收集用户 {user_id} 对动作 {exercise_id} 的反馈")
        return True
        
    except Exception as e:
        logger.error(f"收集用户反馈失败: {str(e)}")
        return False

def optimize_recommendations_with_history(
    exercises: List[Dict[str, Any]], 
    user_history: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    基于用户历史数据优化推荐
    
    Args:
        exercises: 候选动作列表
        user_history: 用户历史数据
        
    Returns:
        优化后的动作列表
    """
    try:
        # 这里可以添加基于历史数据的推荐优化逻辑
        # 例如：根据用户偏好调整动作顺序、参数等
        
        # 暂时直接返回原列表，后续可以扩展
        return exercises
        
    except Exception as e:
        logger.error(f"基于历史数据优化推荐失败: {str(e)}")
        return exercises
