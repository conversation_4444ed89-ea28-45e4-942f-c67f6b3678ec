"""
结果选择节点

基于质量评分算法选择最佳响应。
"""

import logging
import time
from typing import Dict, Any, List, Optional
from langchain_core.messages import AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState

logger = logging.getLogger(__name__)

class ResultSelector:
    """结果选择器"""
    
    def __init__(self):
        self.quality_weights = {
            "confidence": 0.3,      # 置信度权重
            "content_length": 0.2,  # 内容长度权重
            "specificity": 0.2,     # 专业性权重
            "completeness": 0.15,   # 完整性权重
            "relevance": 0.15       # 相关性权重
        }
    
    def evaluate_result_quality(self, result: Dict[str, Any], user_message: str = "") -> float:
        """
        评估单个结果的质量
        
        Args:
            result: 处理结果
            user_message: 用户消息（用于相关性评估）
            
        Returns:
            质量评分（0-1）
        """
        try:
            scores = {}
            
            # 1. 置信度评分
            confidence = result.get("confidence", 0.0)
            scores["confidence"] = min(confidence, 1.0)
            
            # 2. 内容长度评分
            content = result.get("content", "")
            content_length = len(content)
            if content_length == 0:
                scores["content_length"] = 0.0
            elif content_length < 20:
                scores["content_length"] = 0.3
            elif content_length < 100:
                scores["content_length"] = 0.7
            else:
                scores["content_length"] = 1.0
            
            # 3. 专业性评分
            scores["specificity"] = self._evaluate_specificity(result)
            
            # 4. 完整性评分
            scores["completeness"] = self._evaluate_completeness(result)
            
            # 5. 相关性评分
            scores["relevance"] = self._evaluate_relevance(result, user_message)
            
            # 计算加权总分
            total_score = sum(
                scores[metric] * self.quality_weights[metric]
                for metric in scores
            )
            
            logger.debug(f"结果质量评分: {total_score:.3f}, 详细: {scores}")
            
            return total_score
            
        except Exception as e:
            logger.error(f"评估结果质量失败: {str(e)}")
            return 0.0
    
    def _evaluate_specificity(self, result: Dict[str, Any]) -> float:
        """评估专业性"""
        try:
            content = result.get("content", "").lower()
            structured_data = result.get("structured_data", {})
            
            # 检查专业术语
            fitness_terms = [
                "训练", "健身", "运动", "锻炼", "肌肉", "力量", "有氧", "无氧",
                "蛋白质", "碳水", "营养", "热量", "组数", "次数", "重量"
            ]
            
            term_count = sum(1 for term in fitness_terms if term in content)
            specificity_score = min(term_count / 5.0, 1.0)  # 最多5个术语得满分
            
            # 如果有结构化数据，增加专业性评分
            if structured_data:
                specificity_score = min(specificity_score + 0.2, 1.0)
            
            return specificity_score
            
        except Exception:
            return 0.5
    
    def _evaluate_completeness(self, result: Dict[str, Any]) -> float:
        """评估完整性"""
        try:
            completeness_score = 0.0
            
            # 检查基本字段
            if result.get("content"):
                completeness_score += 0.4
            if result.get("confidence", 0) > 0:
                completeness_score += 0.2
            if result.get("intent"):
                completeness_score += 0.2
            if result.get("structured_data"):
                completeness_score += 0.2
            
            return min(completeness_score, 1.0)
            
        except Exception:
            return 0.5
    
    def _evaluate_relevance(self, result: Dict[str, Any], user_message: str) -> float:
        """评估相关性"""
        try:
            if not user_message:
                return 0.5
            
            content = result.get("content", "").lower()
            user_message_lower = user_message.lower()
            
            # 简单的关键词匹配
            user_words = set(user_message_lower.split())
            content_words = set(content.split())
            
            # 计算交集比例
            if not user_words:
                return 0.5
            
            intersection = user_words.intersection(content_words)
            relevance_score = len(intersection) / len(user_words)
            
            return min(relevance_score, 1.0)
            
        except Exception:
            return 0.5
    
    def select_best_result(
        self,
        results: List[Dict[str, Any]],
        user_message: str = "",
        strategy: str = "quality"
    ) -> Optional[Dict[str, Any]]:
        """
        选择最佳结果
        
        Args:
            results: 结果列表
            user_message: 用户消息
            strategy: 选择策略（quality, confidence, hybrid）
            
        Returns:
            最佳结果或None
        """
        try:
            if not results:
                logger.warning("没有结果可供选择")
                return None
            
            if len(results) == 1:
                logger.info("只有一个结果，直接选择")
                return results[0]
            
            # 根据策略选择
            if strategy == "quality":
                return self._select_by_quality(results, user_message)
            elif strategy == "confidence":
                return self._select_by_confidence(results)
            elif strategy == "hybrid":
                return self._select_by_hybrid(results, user_message)
            else:
                logger.warning(f"未知选择策略: {strategy}，使用质量策略")
                return self._select_by_quality(results, user_message)
                
        except Exception as e:
            logger.error(f"选择最佳结果失败: {str(e)}")
            # 返回第一个结果作为回退
            return results[0] if results else None
    
    def _select_by_quality(self, results: List[Dict[str, Any]], user_message: str) -> Dict[str, Any]:
        """基于质量评分选择"""
        scored_results = []
        
        for result in results:
            quality_score = self.evaluate_result_quality(result, user_message)
            scored_results.append((result, quality_score))
        
        # 按质量评分排序
        scored_results.sort(key=lambda x: x[1], reverse=True)
        
        best_result, best_score = scored_results[0]
        logger.info(f"基于质量选择结果: 来源={best_result.get('source')}, 评分={best_score:.3f}")
        
        return best_result
    
    def _select_by_confidence(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """基于置信度选择"""
        best_result = max(results, key=lambda x: x.get("confidence", 0.0))
        logger.info(f"基于置信度选择结果: 来源={best_result.get('source')}, 置信度={best_result.get('confidence', 0):.3f}")
        return best_result
    
    def _select_by_hybrid(self, results: List[Dict[str, Any]], user_message: str) -> Dict[str, Any]:
        """混合策略选择"""
        scored_results = []
        
        for result in results:
            quality_score = self.evaluate_result_quality(result, user_message)
            confidence = result.get("confidence", 0.0)
            
            # 混合评分：70%质量 + 30%置信度
            hybrid_score = 0.7 * quality_score + 0.3 * confidence
            scored_results.append((result, hybrid_score))
        
        # 按混合评分排序
        scored_results.sort(key=lambda x: x[1], reverse=True)
        
        best_result, best_score = scored_results[0]
        logger.info(f"基于混合策略选择结果: 来源={best_result.get('source')}, 评分={best_score:.3f}")
        
        return best_result

def result_selector_node(state: UnifiedFitnessState) -> Dict[str, Any]:
    """
    结果选择节点
    
    Args:
        state: 当前状态
        
    Returns:
        更新后的状态
    """
    start_time = time.time()
    
    try:
        # 检查是否有并行结果
        if not state.parallel_results:
            logger.warning("没有并行结果可供选择")
            return {
                "current_node": "result_selector",
                "response_content": "没有找到处理结果，请稍后重试。",
                "confidence": 0.0,
                "selected_result": None
            }
        
        # 获取用户消息
        user_message = ""
        if state.messages:
            for msg in reversed(state.messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break
        
        # 创建结果选择器
        selector = ResultSelector()
        
        # 选择最佳结果
        best_result = selector.select_best_result(
            state.parallel_results,
            user_message,
            strategy="hybrid"
        )
        
        if not best_result:
            logger.error("结果选择失败")
            return {
                "current_node": "result_selector",
                "response_content": "结果选择失败，请稍后重试。",
                "confidence": 0.0,
                "selected_result": None
            }
        
        # 提取最佳结果的内容
        response_content = best_result.get("content", "")
        confidence = best_result.get("confidence", 0.0)
        
        # 添加AI响应到消息历史
        ai_message = AIMessage(content=response_content)
        
        # 记录处理时间
        execution_time = time.time() - start_time
        
        # 生成选择理由
        selection_reason = f"从{len(state.parallel_results)}个结果中选择，来源: {best_result.get('source')}"
        
        logger.info(f"结果选择完成: {selection_reason}, 置信度: {confidence:.3f}")
        
        return {
            "current_node": "result_selector",
            "processing_system": "result_selection",
            "response_content": response_content,
            "confidence": confidence,
            "selected_result": {
                **best_result,
                "selection_reason": selection_reason,
                "selection_timestamp": time.time()
            },
            "messages": state.messages + [ai_message],
            "node_execution_times": {
                **state.node_execution_times,
                "result_selector": execution_time
            }
        }
        
    except Exception as e:
        logger.error(f"结果选择节点执行失败: {str(e)}")
        execution_time = time.time() - start_time
        
        error_response = "抱歉，结果选择过程中出现错误，请稍后重试。"
        ai_message = AIMessage(content=error_response)
        
        return {
            "current_node": "result_selector",
            "last_error": f"结果选择失败: {str(e)}",
            "error_count": state.error_count + 1,
            "response_content": error_response,
            "messages": state.messages + [ai_message],
            "node_execution_times": {
                **state.node_execution_times,
                "result_selector": execution_time
            }
        }
