"""
智能路由节点

基于多维度分析进行路由决策，包括消息复杂度、领域专业性、上下文依赖等。
"""

import logging
import time
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils

logger = logging.getLogger(__name__)

class IntelligentRouter:
    """智能路由器"""

    def __init__(self):
        self.complexity_keywords = {
            "simple": ["你好", "hi", "hello", "谢谢", "再见", "好的", "是的", "不是"],
            "medium": ["推荐", "建议", "怎么", "如何", "什么", "训练", "运动", "健身"],
            "complex": ["制定", "计划", "方案", "详细", "系统", "全面", "个性化", "定制"]
        }

        self.domain_keywords = {
            "fitness_specific": [
                "训练", "健身", "运动", "锻炼", "肌肉", "力量", "有氧", "无氧",
                "胸肌", "背肌", "腿部", "腹肌", "臀部", "肩膀", "手臂",
                "俯卧撑", "深蹲", "硬拉", "卧推", "引体向上"
            ],
            "nutrition": [
                "饮食", "营养", "蛋白质", "碳水", "脂肪", "热量", "卡路里",
                "减脂", "增肌", "补剂", "维生素", "矿物质"
            ],
            "general": [
                "你好", "谢谢", "帮助", "问题", "咨询", "了解", "知道"
            ]
        }

        self.context_indicators = {
            "requires_history": ["继续", "接着", "然后", "还有", "另外", "补充"],
            "requires_profile": ["我的", "个人", "适合我", "针对我", "根据我"],
            "standalone": ["什么是", "介绍", "解释", "定义", "概念"]
        }

    def analyze_message_complexity(self, message: str) -> Dict[str, Any]:
        """
        分析消息复杂度

        Args:
            message: 用户消息

        Returns:
            复杂度分析结果
        """
        message_lower = message.lower()

        # 计算各复杂度级别的匹配分数
        scores = {}
        for level, keywords in self.complexity_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            scores[level] = score

        # 确定复杂度级别
        if scores["complex"] > 0:
            complexity = "complex"
            confidence = min(0.9, 0.6 + scores["complex"] * 0.1)
        elif scores["medium"] > 0:
            complexity = "medium"
            confidence = min(0.8, 0.5 + scores["medium"] * 0.1)
        else:
            complexity = "simple"
            confidence = 0.7 if scores["simple"] > 0 else 0.4

        return {
            "complexity": complexity,
            "confidence": confidence,
            "scores": scores,
            "message_length": len(message),
            "word_count": len(message.split())
        }

    def analyze_domain_specificity(self, message: str) -> Dict[str, Any]:
        """
        分析领域专业性

        Args:
            message: 用户消息

        Returns:
            领域专业性分析结果
        """
        message_lower = message.lower()

        # 计算各领域的匹配分数
        domain_scores = {}
        for domain, keywords in self.domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            domain_scores[domain] = score

        # 确定主要领域
        max_score = max(domain_scores.values())
        if max_score == 0:
            primary_domain = "general"
            specificity = "low"
            confidence = 0.3
        else:
            primary_domain = max(domain_scores, key=domain_scores.get)
            if max_score >= 3:
                specificity = "high"
                confidence = 0.9
            elif max_score >= 2:
                specificity = "medium"
                confidence = 0.7
            else:
                specificity = "low"
                confidence = 0.5

        return {
            "primary_domain": primary_domain,
            "specificity": specificity,
            "confidence": confidence,
            "domain_scores": domain_scores
        }

    def analyze_context_dependency(self, message: str, state: UnifiedFitnessState) -> Dict[str, Any]:
        """
        分析上下文依赖性

        Args:
            message: 用户消息
            state: 当前状态

        Returns:
            上下文依赖性分析结果
        """
        message_lower = message.lower()

        # 检查各种上下文指标
        context_scores = {}
        for context_type, indicators in self.context_indicators.items():
            score = sum(1 for indicator in indicators if indicator in message_lower)
            context_scores[context_type] = score

        # 分析历史依赖
        history_dependency = "high" if context_scores["requires_history"] > 0 else "low"
        conversation_history = state.get("conversation_history", [])
        if len(conversation_history) > 3:
            history_dependency = "medium"

        # 分析用户档案依赖
        profile_dependency = "high" if context_scores["requires_profile"] > 0 else "low"
        user_profile = state.get("user_profile", {})
        if user_profile and len(user_profile) > 3:
            profile_dependency = "medium"

        # 确定总体依赖性
        if context_scores["standalone"] > 0:
            overall_dependency = "low"
            confidence = 0.8
        elif context_scores["requires_history"] > 0 or context_scores["requires_profile"] > 0:
            overall_dependency = "high"
            confidence = 0.9
        else:
            overall_dependency = "medium"
            confidence = 0.6

        return {
            "overall_dependency": overall_dependency,
            "history_dependency": history_dependency,
            "profile_dependency": profile_dependency,
            "confidence": confidence,
            "context_scores": context_scores
        }

    def make_routing_decision(
        self,
        complexity_analysis: Dict[str, Any],
        domain_analysis: Dict[str, Any],
        context_analysis: Dict[str, Any],
        state: UnifiedFitnessState
    ) -> Dict[str, Any]:
        """
        做出路由决策

        Args:
            complexity_analysis: 复杂度分析
            domain_analysis: 领域分析
            context_analysis: 上下文分析
            state: 当前状态

        Returns:
            路由决策结果
        """
        # 路由决策逻辑
        routing_scores = {
            "enhanced": 0.0,
            "legacy": 0.0,
            "state_machine": 0.0,
            "hybrid": 0.0
        }

        # 基于复杂度的评分
        complexity = complexity_analysis["complexity"]
        if complexity == "complex":
            routing_scores["enhanced"] += 0.4
            routing_scores["hybrid"] += 0.3
        elif complexity == "medium":
            routing_scores["legacy"] += 0.4
            routing_scores["hybrid"] += 0.3
        else:  # simple
            routing_scores["state_machine"] += 0.4

        # 基于领域专业性的评分
        domain = domain_analysis["primary_domain"]
        specificity = domain_analysis["specificity"]

        if domain in ["fitness_specific", "nutrition"] and specificity == "high":
            routing_scores["legacy"] += 0.4
            routing_scores["hybrid"] += 0.2
        elif domain == "general":
            routing_scores["state_machine"] += 0.3

        # 基于上下文依赖的评分
        dependency = context_analysis["overall_dependency"]
        if dependency == "high":
            routing_scores["enhanced"] += 0.3
            routing_scores["hybrid"] += 0.2
        elif dependency == "low":
            routing_scores["state_machine"] += 0.2

        # 考虑当前状态
        current_state_name = state.get("current_state_name", "idle")
        if current_state_name != "idle":
            routing_scores["state_machine"] += 0.2

        # 考虑错误历史
        error_count = state.get("error_count", 0)
        if error_count > 0:
            routing_scores["hybrid"] += 0.3

        # 选择最佳路由
        best_route = max(routing_scores, key=routing_scores.get)
        confidence = routing_scores[best_route]

        # 如果置信度太低，使用混合模式
        if confidence < 0.5:
            best_route = "hybrid"
            confidence = 0.6

        return {
            "route": best_route,
            "confidence": confidence,
            "scores": routing_scores,
            "reasoning": self._generate_reasoning(
                best_route, complexity_analysis, domain_analysis, context_analysis
            )
        }

    def _generate_reasoning(
        self,
        route: str,
        complexity_analysis: Dict[str, Any],
        domain_analysis: Dict[str, Any],
        context_analysis: Dict[str, Any]
    ) -> str:
        """生成路由决策的推理说明"""
        reasons = []

        complexity = complexity_analysis["complexity"]
        domain = domain_analysis["primary_domain"]
        dependency = context_analysis["overall_dependency"]

        if route == "enhanced":
            reasons.append(f"消息复杂度{complexity}，需要增强处理")
            if dependency == "high":
                reasons.append("高上下文依赖，需要智能分析")
        elif route == "legacy":
            reasons.append(f"领域专业性{domain}，使用专业处理器")
            if complexity == "medium":
                reasons.append("中等复杂度，适合传统处理")
        elif route == "state_machine":
            reasons.append(f"消息复杂度{complexity}，使用状态机处理")
            if dependency == "low":
                reasons.append("低上下文依赖，状态机足够")
        elif route == "hybrid":
            reasons.append("多维度分析建议使用混合处理")
            reasons.append("并行处理以获得最佳结果")

        return "; ".join(reasons)

def intelligent_router_node(state: UnifiedFitnessState) -> Dict[str, Any]:
    """
    智能路由节点

    Args:
        state: 当前状态

    Returns:
        更新后的状态
    """
    start_time = time.time()

    try:
        # 获取最新的用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        if not user_message:
            logger.warning("没有找到用户消息，使用默认路由")
            state["current_node"] = "intelligent_router"
            state["routing_decision"] = {
                "route": "state_machine",
                "confidence": 0.5,
                "reasoning": "没有用户消息，使用默认路由"
            }
            return state

        # 创建路由器实例
        router = IntelligentRouter()

        # 执行多维度分析
        complexity_analysis = router.analyze_message_complexity(user_message)
        domain_analysis = router.analyze_domain_specificity(user_message)
        context_analysis = router.analyze_context_dependency(user_message, state)

        # 做出路由决策
        routing_decision = router.make_routing_decision(
            complexity_analysis, domain_analysis, context_analysis, state
        )

        # 记录处理时间
        execution_time = time.time() - start_time

        logger.info(f"智能路由决策: {routing_decision['route']} "
                   f"(置信度: {routing_decision['confidence']:.2f})")

        # 更新状态并返回
        state["current_node"] = "intelligent_router"
        state["routing_decision"] = routing_decision
        state["complexity_analysis"] = complexity_analysis
        state["domain_analysis"] = domain_analysis
        state["context_analysis"] = context_analysis
        StateUtils.update_processing_metrics(state, "intelligent_router", execution_time)
        StateUtils.add_processing_step(state, "intelligent_router_analysis")

        return state

    except Exception as e:
        logger.error(f"智能路由节点执行失败: {str(e)}")
        execution_time = time.time() - start_time

        # 错误情况下更新状态
        state["current_node"] = "intelligent_router"
        state["routing_decision"] = {
            "route": "state_machine",  # 默认回退
            "confidence": 0.3,
            "reasoning": f"路由分析失败: {str(e)}"
        }
        StateUtils.set_error(state, f"智能路由失败: {str(e)}")
        StateUtils.update_processing_metrics(state, "intelligent_router", execution_time)

        return state
