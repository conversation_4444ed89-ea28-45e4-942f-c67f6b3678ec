"""
简化的处理器节点

用于测试LangGraph基本功能的简化版本。
"""

import logging
import time
from langchain_core.messages import AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils

logger = logging.getLogger(__name__)

async def simple_enhanced_processor_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简化的增强处理器节点"""
    start_time = time.time()

    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 简单的意图识别
        if "健身" in user_message or "训练" in user_message:
            intent = "exercise_action"
            confidence = 0.9
        elif "营养" in user_message or "饮食" in user_message:
            intent = "nutrition_advice"
            confidence = 0.8
        else:
            intent = "general_chat"
            confidence = 0.7

        response_content = f"增强处理器识别到您的意图是：{intent}，我来为您提供专业的健身建议。"

        # 更新状态
        state["current_node"] = "enhanced_processor"
        state["processing_system"] = "enhanced"
        state["intent"] = intent
        state["confidence"] = confidence
        state["response_content"] = response_content

        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state["messages"].append(ai_message)

        # 记录执行时间
        execution_time = time.time() - start_time
        StateUtils.update_processing_metrics(state, "enhanced_processor", execution_time)
        StateUtils.add_processing_step(state, "enhanced_processor_execution")

        logger.info(f"简化增强处理器完成: {intent} (置信度: {confidence:.2f})")
        return state

    except Exception as e:
        logger.error(f"简化增强处理器失败: {str(e)}")
        execution_time = time.time() - start_time

        error_response = "抱歉，增强处理器暂时无法处理您的请求。"
        state["current_node"] = "enhanced_processor"
        state["processing_system"] = "enhanced"
        StateUtils.set_error(state, f"增强处理器失败: {str(e)}")
        state["response_content"] = error_response
        StateUtils.update_processing_metrics(state, "enhanced_processor", execution_time)

        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)

        return state

async def simple_legacy_processor_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简化的传统处理器节点"""
    start_time = time.time()

    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        response_content = f"传统处理器已处理您的请求：{user_message[:50]}..."

        # 更新状态
        state["current_node"] = "legacy_processor"
        state["processing_system"] = "legacy"
        state["confidence"] = 0.8
        state["response_content"] = response_content

        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state["messages"].append(ai_message)

        # 记录执行时间
        execution_time = time.time() - start_time
        StateUtils.update_processing_metrics(state, "legacy_processor", execution_time)
        StateUtils.add_processing_step(state, "legacy_processor_execution")

        logger.info("简化传统处理器完成")
        return state

    except Exception as e:
        logger.error(f"简化传统处理器失败: {str(e)}")
        execution_time = time.time() - start_time

        error_response = "抱歉，传统处理器暂时无法处理您的请求。"
        state["current_node"] = "legacy_processor"
        state["processing_system"] = "legacy"
        StateUtils.set_error(state, f"传统处理器失败: {str(e)}")
        state["response_content"] = error_response
        StateUtils.update_processing_metrics(state, "legacy_processor", execution_time)

        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)

        return state

async def simple_state_machine_processor_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简化的状态机处理器节点"""
    start_time = time.time()

    try:
        # 获取用户消息
        user_message = ""
        if state.messages:
            for msg in reversed(state.messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        response_content = f"状态机处理器已处理您的消息。您好！我是您的健身AI助手，很高兴为您服务。"

        # 更新状态
        state.current_node = "state_machine_processor"
        state.processing_system = "state_machine"
        state.confidence = 0.8
        state.response_content = response_content
        state.current_state_name = "idle"

        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state.messages.append(ai_message)

        # 记录执行时间
        execution_time = time.time() - start_time
        state.node_execution_times["state_machine_processor"] = execution_time
        state.add_processing_step("state_machine_processor_execution")

        logger.info("简化状态机处理器完成")
        return state

    except Exception as e:
        logger.error(f"简化状态机处理器失败: {str(e)}")
        execution_time = time.time() - start_time

        error_response = "抱歉，状态机处理器暂时无法处理您的请求。"
        state.current_node = "state_machine_processor"
        state.processing_system = "state_machine"
        state.set_error(f"状态机处理器失败: {str(e)}")
        state.response_content = error_response
        state.node_execution_times["state_machine_processor"] = execution_time

        ai_message = AIMessage(content=error_response)
        state.messages.append(ai_message)

        return state

async def simple_hybrid_processor_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简化的混合处理器节点"""
    start_time = time.time()

    try:
        # 模拟并行处理多个处理器
        results = []

        # 模拟增强处理器结果
        enhanced_result = {
            "source": "enhanced",
            "content": "增强处理器：这是一个关于健身的专业建议。",
            "confidence": 0.9,
            "intent": "exercise_action"
        }
        results.append(enhanced_result)

        # 模拟状态机处理器结果
        state_machine_result = {
            "source": "state_machine",
            "content": "状态机处理器：您好！我是您的健身助手。",
            "confidence": 0.8,
            "intent": "general_chat"
        }
        results.append(state_machine_result)

        # 选择最佳结果（这里选择置信度最高的）
        best_result = max(results, key=lambda x: x["confidence"])

        # 更新状态
        state.current_node = "hybrid_processor"
        state.processing_system = "hybrid"
        state.parallel_results = results
        state.selected_result = best_result
        state.response_content = best_result["content"]
        state.confidence = best_result["confidence"]
        state.intent = best_result["intent"]

        # 添加AI响应
        ai_message = AIMessage(content=best_result["content"])
        state.messages.append(ai_message)

        # 记录执行时间
        execution_time = time.time() - start_time
        state.node_execution_times["hybrid_processor"] = execution_time
        state.add_processing_step("hybrid_processor_execution")

        logger.info(f"简化混合处理器完成，选择了{best_result['source']}的结果")
        return state

    except Exception as e:
        logger.error(f"简化混合处理器失败: {str(e)}")
        execution_time = time.time() - start_time

        error_response = "抱歉，混合处理器暂时无法处理您的请求。"
        state.current_node = "hybrid_processor"
        state.processing_system = "hybrid"
        state.set_error(f"混合处理器失败: {str(e)}")
        state.response_content = error_response
        state.node_execution_times["hybrid_processor"] = execution_time

        ai_message = AIMessage(content=error_response)
        state.messages.append(ai_message)

        return state

async def simple_result_selector_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简化的结果选择器节点"""
    start_time = time.time()

    try:
        # 如果已经有选中的结果，直接使用
        if state.selected_result:
            response_content = state.selected_result.get("content", state.response_content)
        elif state.parallel_results:
            # 选择置信度最高的结果
            best_result = max(state.parallel_results, key=lambda x: x.get("confidence", 0))
            response_content = best_result.get("content", "")
            state.selected_result = best_result
        else:
            # 使用当前响应内容
            response_content = state.response_content or "处理完成。"

        # 更新状态
        state.current_node = "result_selector"
        state.processing_system = "result_selection"
        state.response_content = response_content

        # 记录执行时间
        execution_time = time.time() - start_time
        state.node_execution_times["result_selector"] = execution_time
        state.add_processing_step("result_selector_execution")

        logger.info("简化结果选择器完成")
        return state

    except Exception as e:
        logger.error(f"简化结果选择器失败: {str(e)}")
        execution_time = time.time() - start_time

        error_response = "抱歉，结果选择过程中出现错误。"
        state.current_node = "result_selector"
        state.processing_system = "result_selection"
        state.set_error(f"结果选择器失败: {str(e)}")
        state.response_content = error_response
        state.node_execution_times["result_selector"] = execution_time

        return state
