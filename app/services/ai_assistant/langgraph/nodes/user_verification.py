"""
用户信息验证节点

负责验证用户信息完整性并与数据库集成
"""

import logging
from typing import Dict, Any
from langchain_core.messages import AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
from app.services.user_profile_manager import UserProfileManager

logger = logging.getLogger(__name__)

async def user_info_verification_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """
    用户信息验证节点

    检查用户信息完整性，如果缺失则收集，完整则保存到数据库
    """
    try:
        # 获取当前用户信息
        user_profile = state.get("user_profile", {})
        user_id = state.get("user_id", "unknown")

        # 检查缺失的必要信息
        missing_fields = UserProfileManager.get_missing_fields(user_profile)

        logger.info(f"用户档案: {user_profile}")
        logger.info(f"缺失字段: {missing_fields}")

        if missing_fields:
            # 需要收集用户信息
            next_field = missing_fields[0]  # 获取第一个缺失字段

            # 更新流程状态
            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["missing_user_info"] = missing_fields
            state["flow_state"]["current_collecting_field"] = next_field
            state["flow_state"]["stage"] = "collecting_user_info"

            # 生成询问提示
            field_prompt = UserProfileManager.get_field_prompt(next_field)
            response_content = f"为了给您提供更个性化的训练建议，我需要了解一些基本信息。\n\n{field_prompt}"

            # 更新状态
            state["current_node"] = "user_info_verification"
            state["response_content"] = response_content
            state["confidence"] = 0.9

            # 添加AI响应
            ai_message = AIMessage(content=response_content)
            if "messages" not in state:
                state["messages"] = []
            state["messages"].append(ai_message)

            logger.info(f"需要收集用户信息: {next_field}")
            return state
        else:
            # 用户信息完整，保存到数据库并继续参数收集
            save_success = await save_user_profile_to_database(user_id, user_profile)

            if "flow_state" not in state:
                state["flow_state"] = {}
            state["flow_state"]["needs_user_info"] = False
            state["flow_state"]["stage"] = "parameter_collection"
            state["flow_state"]["user_profile_saved"] = save_success

            logger.info(f"用户信息完整，数据库保存{'成功' if save_success else '失败'}，继续参数收集")
            return state

    except Exception as e:
        logger.error(f"用户信息验证失败: {str(e)}")
        state["current_node"] = "user_info_verification"
        state["response_content"] = "抱歉，用户信息验证过程中出现问题，我将使用默认设置为您提供建议。"
        StateUtils.set_error(state, f"用户信息验证失败: {str(e)}")

        # 跳过用户信息收集，继续流程
        if "flow_state" not in state:
            state["flow_state"] = {}
        state["flow_state"]["needs_user_info"] = False
        state["flow_state"]["stage"] = "parameter_collection"
        return state

def route_from_user_info(state: UnifiedFitnessState) -> str:
    """从用户信息验证节点的条件路由函数"""
    try:
        from langgraph.graph import END

        flow_state = state.get("flow_state", {})
        stage = flow_state.get("stage", "")

        if stage == "collecting_user_info":
            # 需要等待用户输入，结束当前流程
            return END
        elif stage == "parameter_collection":
            # 用户信息完整，继续参数收集
            return "parameter_collection"
        else:
            # 默认结束
            return END

    except Exception as e:
        logger.error(f"用户信息路由条件失败: {str(e)}")
        from langgraph.graph import END
        return END

async def save_user_profile_to_database(user_id: str, user_profile: Dict[str, Any]) -> bool:
    """
    保存用户档案到数据库

    Args:
        user_id: 用户ID
        user_profile: 用户档案数据

    Returns:
        保存是否成功
    """
    try:
        # 导入数据库相关模块
        from app.crud.user import CRUDUser
        from app.models.user import User
        from app.db.session import get_db

        # 获取数据库会话
        db = next(get_db())
        user_crud = CRUDUser(User)

        # 检查用户是否存在
        existing_user = user_crud.get(db, int(user_id))

        if existing_user:
            # 更新现有用户信息（增量更新）
            update_data = {}

            # 映射字段名
            field_mapping = {
                "gender": "gender",
                "age": "age",
                "height": "height",
                "weight": "weight",
                "fitness_goal": "fitness_goal",
                "fitness_level": "fitness_level",
                "activity_level": "activity_level"
            }

            # 只更新有变化的字段
            for profile_field, db_field in field_mapping.items():
                if profile_field in user_profile:
                    new_value = user_profile[profile_field]
                    current_value = getattr(existing_user, db_field, None)

                    # 处理特殊字段的类型转换
                    if db_field == "gender":
                        # 性别字段映射
                        gender_mapping = {"男": 1, "女": 2, "其他": 3}
                        if isinstance(new_value, str) and new_value in gender_mapping:
                            new_value = gender_mapping[new_value]
                        elif isinstance(new_value, str) and new_value.isdigit():
                            new_value = int(new_value)
                    elif db_field == "fitness_goal":
                        # 健身目标字段映射
                        goal_mapping = {"减脂": 1, "增肌": 2, "塑形": 3, "力量": 4, "健康": 5}
                        if isinstance(new_value, str) and new_value in goal_mapping:
                            new_value = goal_mapping[new_value]
                        elif isinstance(new_value, str) and new_value.isdigit():
                            new_value = int(new_value)
                    elif db_field == "fitness_level":
                        # 健身水平字段映射
                        level_mapping = {"初级": 1, "中级": 2, "高级": 3}
                        if isinstance(new_value, str) and new_value in level_mapping:
                            new_value = level_mapping[new_value]
                        elif isinstance(new_value, str) and new_value.isdigit():
                            new_value = int(new_value)
                    elif isinstance(new_value, str) and new_value.isdigit():
                        # 其他数值字段
                        new_value = int(new_value)
                    elif isinstance(new_value, str) and '.' in new_value:
                        try:
                            new_value = float(new_value)
                        except ValueError:
                            logger.warning(f"无法转换字段 {db_field} 为浮点数: {new_value}")
                            continue

                    if new_value != current_value:
                        update_data[db_field] = new_value

            if update_data:
                # 执行增量更新
                updated_user = user_crud.update(db, db_obj=existing_user, obj_in=update_data)
                logger.info(f"用户 {user_id} 信息更新成功: {update_data}")
                return updated_user is not None
            else:
                logger.info(f"用户 {user_id} 信息无变化，跳过更新")
                return True
        else:
            logger.warning(f"用户 {user_id} 不存在，无法更新档案")
            return False

    except Exception as e:
        logger.error(f"保存用户档案到数据库失败: {str(e)}")
        return False

async def handle_user_info_input(state: UnifiedFitnessState, user_input: str) -> UnifiedFitnessState:
    """
    处理用户信息输入

    Args:
        state: 当前状态
        user_input: 用户输入

    Returns:
        更新后的状态
    """
    try:
        flow_state = state.get("flow_state", {})
        current_field = flow_state.get("current_collecting_field", "")
        missing_fields = flow_state.get("missing_user_info", [])

        if not current_field:
            logger.warning("没有当前收集字段，跳过处理")
            return state

        # 处理用户输入
        is_valid, feedback, normalized_value = await UserProfileManager.process_user_info_input(
            current_field, user_input
        )

        if is_valid:
            # 更新用户档案
            if "user_profile" not in state:
                state["user_profile"] = {}
            state["user_profile"][current_field] = normalized_value

            # 从缺失字段列表中移除
            if current_field in missing_fields:
                missing_fields.remove(current_field)

            # 检查是否还有缺失字段
            if missing_fields:
                # 继续收集下一个字段
                next_field = missing_fields[0]
                state["flow_state"]["current_collecting_field"] = next_field
                state["flow_state"]["missing_user_info"] = missing_fields

                # 生成下一个字段的询问
                field_prompt = UserProfileManager.get_field_prompt(next_field)
                response_content = f"谢谢！{feedback}\n\n{field_prompt}"

                state["response_content"] = response_content
                ai_message = AIMessage(content=response_content)
                state["messages"].append(ai_message)

                logger.info(f"继续收集下一个字段: {next_field}")
            else:
                # 所有信息收集完成，保存到数据库
                user_id = state.get("user_id", "unknown")
                save_success = await save_user_profile_to_database(user_id, state["user_profile"])

                # 更新流程状态
                state["flow_state"]["stage"] = "parameter_collection"
                state["flow_state"]["needs_user_info"] = False
                state["flow_state"]["user_profile_saved"] = save_success

                # 生成完成提示
                save_status = "并已保存到您的档案中" if save_success else "但保存时出现问题"
                response_content = f"谢谢！{feedback}{save_status}。\n\n现在让我们继续您的训练计划。"

                state["response_content"] = response_content
                ai_message = AIMessage(content=response_content)
                state["messages"].append(ai_message)

                logger.info(f"用户信息收集完成，数据库保存{'成功' if save_success else '失败'}")
        else:
            # 输入无效，重新询问
            field_prompt = UserProfileManager.get_field_prompt(current_field)
            response_content = f"抱歉，{feedback}\n\n请重新提供：{field_prompt}"

            state["response_content"] = response_content
            ai_message = AIMessage(content=response_content)
            state["messages"].append(ai_message)

            logger.info(f"用户输入无效，重新询问: {current_field}")

        return state

    except Exception as e:
        logger.error(f"处理用户信息输入失败: {str(e)}")

        # 错误处理，跳过当前字段
        error_response = "抱歉，处理您的输入时出现问题。让我们继续其他信息的收集。"
        state["response_content"] = error_response
        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)

        return state
