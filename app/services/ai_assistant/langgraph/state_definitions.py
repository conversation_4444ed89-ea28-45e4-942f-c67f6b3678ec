"""
LangGraph统一状态定义

定义智能健身AI助手系统的统一状态模型，继承MessagesState并包含所有必要的状态字段。
"""

import logging
from typing import Annotated, Dict, Any, List, Optional, Union
from datetime import datetime
from langgraph.graph.message import add_messages
from langchain_core.messages import AnyMessage, BaseMessage
from typing_extensions import TypedDict

logger = logging.getLogger(__name__)

class UnifiedFitnessState(TypedDict):
    """
    统一的健身AI助手状态定义

    继承自MessagesState，包含健身AI助手所需的所有状态字段，
    确保与现有ConversationState的兼容性。
    """

    # ===== 基础会话信息 =====
    conversation_id: str = ""
    user_id: str = ""
    session_id: str = ""
    timestamp: Optional[datetime] = None

    # ===== 意图识别结果（多层级） =====
    intent: str = ""
    confidence: float = 0.0
    intent_parameters: Dict[str, Any] = {}
    enhanced_intent_result: Optional[Dict[str, Any]] = None
    original_intent: Optional[str] = None  # 原始识别的意图

    # ===== 用户信息 =====
    user_profile: Dict[str, Any] = {}
    user_preferences: Dict[str, Any] = {}
    user_context: Dict[str, Any] = {}

    # ===== 训练参数和健身数据 =====
    training_params: Dict[str, Any] = {}
    fitness_goals: List[str] = []
    current_workout: Optional[Dict[str, Any]] = None
    exercise_history: List[Dict[str, Any]] = []

    # ===== 流程状态和控制 =====
    flow_state: Dict[str, Any] = {}
    current_state_name: str = "idle"
    previous_state_name: str = ""
    state_transition_history: List[str] = []

    # ===== 系统状态和路由信息 =====
    current_node: str = ""
    processing_system: str = ""  # "enhanced", "legacy", "state_machine", "hybrid", "langgraph"
    processing_path: List[str] = []  # 处理路径追踪
    routing_decision: Dict[str, Any] = {}

    # ===== 响应信息 =====
    response_content: str = ""
    response_type: str = "text"  # "text", "structured", "streaming"
    structured_data: Dict[str, Any] = {}
    response_metadata: Dict[str, Any] = {}

    # ===== 错误处理和重试 =====
    error_count: int = 0
    last_error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

    # ===== 性能指标 =====
    processing_start_time: Optional[float] = None
    processing_end_time: Optional[float] = None
    node_execution_times: Dict[str, float] = {}
    total_processing_time: float = 0.0

    # ===== LangGraph特定字段 =====
    graph_execution_id: Optional[str] = None
    langgraph_checkpoint_id: Optional[str] = None  # 避免与保留字段冲突
    parallel_results: List[Dict[str, Any]] = []  # 并行处理结果
    selected_result: Optional[Dict[str, Any]] = None  # 选中的最佳结果

    # ===== 上下文和历史 =====
    conversation_history: List[Dict[str, Any]] = []
    context_summary: str = ""
    long_term_memory: Dict[str, Any] = {}

    # ===== 配置和控制标志 =====
    enable_streaming: bool = True
    enable_parallel_processing: bool = True
    enable_human_in_loop: bool = False
    debug_mode: bool = False

    # ===== 消息历史（自动管理） =====
    messages: Annotated[List[AnyMessage], add_messages]

# TypedDict不支持方法，所有操作方法已移至StateUtils类
