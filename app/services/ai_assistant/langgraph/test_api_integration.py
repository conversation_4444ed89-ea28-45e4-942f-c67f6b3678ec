"""
API层集成测试

测试ConversationOrchestrator与LangGraph的集成。
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append('/home/<USER>/backend')

from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
from app.core.unified_config import unified_settings

logger = logging.getLogger(__name__)

async def test_api_integration():
    """测试API层集成"""
    try:
        print("🚀 开始测试API层集成...")

        # 检查配置
        print(f"统一架构启用: {unified_settings.ENABLE_UNIFIED_ARCHITECTURE}")
        print(f"架构阶段: {unified_settings.UNIFIED_ARCH_PHASE}")
        print(f"LangGraph启用: {unified_settings.ENABLE_LANGGRAPH}")

        # 测试1：健身相关消息
        print("\n📝 测试1：健身相关消息")
        response1 = await conversation_orchestrator.process_message(
            message="你好，我想了解健身训练",
            conversation_id="test_api_conv_1",
            user_info={
                "user_id": "test_user_1",
                "nickname": "测试用户1"
            }
        )

        print(f"✅ 响应成功: {response1.get('response_content', '')[:100]}...")
        print(f"   意图: {response1.get('intent', 'unknown')}")
        print(f"   置信度: {response1.get('confidence', 0.0):.2f}")
        print(f"   来源系统: {response1.get('source_system', 'unknown')}")

        if 'langgraph_metadata' in response1:
            metadata = response1['langgraph_metadata']
            print(f"   LangGraph处理系统: {metadata.get('processing_system', 'unknown')}")
            print(f"   图执行ID: {metadata.get('graph_execution_id', 'unknown')[:8]}...")
            print(f"   总处理时间: {metadata.get('total_time', 0.0):.3f}s")

        # 测试2：一般消息
        print("\n📝 测试2：一般消息")
        response2 = await conversation_orchestrator.process_message(
            message="今天天气怎么样？",
            conversation_id="test_api_conv_2",
            user_info={
                "user_id": "test_user_2",
                "nickname": "测试用户2"
            }
        )

        print(f"✅ 响应成功: {response2.get('response_content', '')[:100]}...")
        print(f"   意图: {response2.get('intent', 'unknown')}")
        print(f"   置信度: {response2.get('confidence', 0.0):.2f}")
        print(f"   来源系统: {response2.get('source_system', 'unknown')}")

        if 'langgraph_metadata' in response2:
            metadata = response2['langgraph_metadata']
            print(f"   LangGraph处理系统: {metadata.get('processing_system', 'unknown')}")
            print(f"   图执行ID: {metadata.get('graph_execution_id', 'unknown')[:8]}...")
            print(f"   总处理时间: {metadata.get('total_time', 0.0):.3f}s")

        # 测试3：空消息处理
        print("\n📝 测试3：空消息处理")
        response3 = await conversation_orchestrator.process_message(
            message="",
            conversation_id="test_api_conv_3",
            user_info={
                "user_id": "test_user_3",
                "nickname": "测试用户3"
            }
        )

        print(f"✅ 空消息处理: {response3.get('response_content', '')[:100]}...")
        print(f"   响应类型: {response3.get('response_type', 'unknown')}")
        print(f"   错误: {response3.get('error', 'none')}")

        # 空消息应该被正常处理并给出友好响应
        is_handled_properly = (
            response3.get('response_content') is not None and
            len(response3.get('response_content', '')) > 0
        )

        # 验证结果
        success_criteria = [
            response1.get('response_content') is not None,
            response2.get('response_content') is not None,
            is_handled_properly,  # 空消息应该被正常处理
            # 如果启用了LangGraph，应该有相应的元数据
            (not unified_settings.ENABLE_LANGGRAPH or
             response1.get('source_system') in ['langgraph', 'fallback_from_langgraph']),
        ]

        all_success = all(success_criteria)

        print(f"\n🎯 API集成测试结果:")
        print(f"   基本响应: {'✅' if success_criteria[0] and success_criteria[1] else '❌'}")
        print(f"   空消息处理: {'✅' if success_criteria[2] else '❌'}")
        print(f"   LangGraph集成: {'✅' if success_criteria[3] else '❌'}")
        print(f"   总体结果: {'🎉 成功' if all_success else '❌ 失败'}")

        return all_success

    except Exception as e:
        print(f"❌ API集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_langgraph_fallback():
    """测试LangGraph回退机制"""
    try:
        print("\n🔄 测试LangGraph回退机制...")

        # 临时禁用LangGraph来测试回退
        original_enable = unified_settings.ENABLE_LANGGRAPH
        unified_settings.ENABLE_LANGGRAPH = False

        response = await conversation_orchestrator.process_message(
            message="测试回退机制",
            conversation_id="test_fallback_conv",
            user_info={"user_id": "test_fallback_user"}
        )

        # 恢复设置
        unified_settings.ENABLE_LANGGRAPH = original_enable

        print(f"✅ 回退响应: {response.get('response_content', '')[:100]}...")
        print(f"   来源系统: {response.get('source_system', 'unknown')}")

        # 验证回退是否正常工作
        fallback_success = (
            response.get('response_content') is not None and
            response.get('source_system') != 'langgraph'
        )

        print(f"🔄 回退机制测试: {'✅ 成功' if fallback_success else '❌ 失败'}")
        return fallback_success

    except Exception as e:
        print(f"❌ 回退机制测试失败: {str(e)}")
        return False

async def test_response_format_compatibility():
    """测试响应格式兼容性"""
    try:
        print("\n🔍 测试响应格式兼容性...")

        response = await conversation_orchestrator.process_message(
            message="测试响应格式",
            conversation_id="test_format_conv",
            user_info={"user_id": "test_format_user"}
        )

        # 检查必需的响应字段
        required_fields = [
            'response_content', 'conversation_id', 'message_id',
            'intent', 'confidence', 'current_state', 'next_state',
            'timestamp', 'processing_time_ms'
        ]

        missing_fields = [field for field in required_fields if field not in response]

        if missing_fields:
            print(f"❌ 缺少必需字段: {missing_fields}")
            return False
        else:
            print("✅ 所有必需字段都存在")

        # 检查字段类型
        type_checks = [
            isinstance(response.get('confidence'), (int, float)),
            isinstance(response.get('timestamp'), int),
            isinstance(response.get('processing_time_ms'), int),
            isinstance(response.get('response_content'), str)
        ]

        if all(type_checks):
            print("✅ 字段类型正确")
            return True
        else:
            print("❌ 字段类型不正确")
            return False

    except Exception as e:
        print(f"❌ 响应格式兼容性测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🧪 LangGraph API层集成测试")
    print("=" * 50)

    # 运行所有测试
    test_results = []

    # 基本API集成测试
    result1 = await test_api_integration()
    test_results.append(("API集成", result1))

    # 回退机制测试
    result2 = await test_langgraph_fallback()
    test_results.append(("回退机制", result2))

    # 响应格式兼容性测试
    result3 = await test_response_format_compatibility()
    test_results.append(("响应格式兼容性", result3))

    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")

    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")

    overall_success = all(result for _, result in test_results)
    print(f"\n🎯 总体结果: {'🎉 所有测试通过' if overall_success else '❌ 部分测试失败'}")

    return overall_success

if __name__ == "__main__":
    asyncio.run(main())
