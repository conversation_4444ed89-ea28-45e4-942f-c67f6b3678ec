"""
LangGraph综合测试套件

包含单元测试、集成测试、性能测试和错误处理测试。
"""

import asyncio
import time
import logging
import sys
import statistics
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append('/home/<USER>/backend')

from app.services.ai_assistant.langgraph.test_basic_graph import test_basic_graph_functionality
from app.services.ai_assistant.langgraph.test_api_integration import test_api_integration
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
from app.services.ai_assistant.langgraph.adapters.state_adapter import StateAdapter

logger = logging.getLogger(__name__)

class LangGraphTestSuite:
    """LangGraph综合测试套件"""
    
    def __init__(self):
        self.test_results = []
        self.performance_metrics = []
    
    async def run_unit_tests(self) -> bool:
        """运行单元测试"""
        print("🔬 运行单元测试...")
        
        try:
            # 测试StateUtils
            state_utils_result = await self._test_state_utils()
            
            # 测试StateAdapter
            state_adapter_result = await self._test_state_adapter()
            
            # 测试基础图功能
            basic_graph_result = await test_basic_graph_functionality()
            
            unit_test_results = [
                ("StateUtils", state_utils_result),
                ("StateAdapter", state_adapter_result),
                ("BasicGraph", basic_graph_result)
            ]
            
            success_count = sum(1 for _, result in unit_test_results if result)
            total_count = len(unit_test_results)
            
            print(f"📊 单元测试结果: {success_count}/{total_count} 通过")
            for test_name, result in unit_test_results:
                status = "✅" if result else "❌"
                print(f"   {test_name}: {status}")
            
            return success_count == total_count
            
        except Exception as e:
            print(f"❌ 单元测试失败: {str(e)}")
            return False
    
    async def _test_state_utils(self) -> bool:
        """测试StateUtils功能"""
        try:
            # 创建状态
            state = StateUtils.create_initial_state(
                conversation_id="test_conv",
                user_id="test_user",
                message="测试消息"
            )
            
            # 测试状态操作
            StateUtils.add_processing_step(state, "test_step")
            StateUtils.update_processing_metrics(state, "test_node", 0.1)
            StateUtils.set_error(state, "测试错误")
            
            # 验证状态
            is_valid = StateUtils.validate(state)
            
            # 测试重试逻辑
            can_retry = StateUtils.can_retry(state)
            StateUtils.increment_retry(state)
            
            # 测试并行结果
            StateUtils.add_parallel_result(state, {"source": "test", "content": "测试结果"})
            
            # 获取摘要
            summary = StateUtils.get_summary(state)
            
            # 验证所有操作
            checks = [
                state["conversation_id"] == "test_conv",
                "test_step" in state["processing_path"],
                state["node_execution_times"]["test_node"] == 0.1,
                state["last_error"] == "测试错误",
                state["error_count"] == 1,
                len(state["parallel_results"]) == 1,
                isinstance(summary, dict),
                "conversation_id" in summary
            ]
            
            return all(checks)
            
        except Exception as e:
            print(f"StateUtils测试失败: {str(e)}")
            return False
    
    async def _test_state_adapter(self) -> bool:
        """测试StateAdapter功能"""
        try:
            # 测试状态转换
            state = StateAdapter.to_unified_state(
                message="测试消息",
                conversation_id="test_conv",
                user_info={"user_id": "test_user"}
            )
            
            # 添加响应内容
            state["response_content"] = "测试响应"
            state["intent"] = "test_intent"
            state["confidence"] = 0.8
            
            # 测试API响应创建
            api_response = StateAdapter.create_api_response(state)
            
            # 测试状态转换
            conv_state = StateAdapter.from_unified_state(state)
            
            # 验证结果
            checks = [
                isinstance(state, dict),
                state["conversation_id"] == "test_conv",
                api_response["success"] == True,
                api_response["response"] == "测试响应",
                api_response["intent_type"] == "test_intent",
                api_response["confidence"] == 0.8,
                isinstance(conv_state, dict),
                conv_state["conversation_id"] == "test_conv"
            ]
            
            return all(checks)
            
        except Exception as e:
            print(f"StateAdapter测试失败: {str(e)}")
            return False
    
    async def run_integration_tests(self) -> bool:
        """运行集成测试"""
        print("🔗 运行集成测试...")
        
        try:
            # API集成测试
            api_result = await test_api_integration()
            
            # 端到端流程测试
            e2e_result = await self._test_end_to_end_flow()
            
            integration_results = [
                ("API集成", api_result),
                ("端到端流程", e2e_result)
            ]
            
            success_count = sum(1 for _, result in integration_results if result)
            total_count = len(integration_results)
            
            print(f"📊 集成测试结果: {success_count}/{total_count} 通过")
            for test_name, result in integration_results:
                status = "✅" if result else "❌"
                print(f"   {test_name}: {status}")
            
            return success_count == total_count
            
        except Exception as e:
            print(f"❌ 集成测试失败: {str(e)}")
            return False
    
    async def _test_end_to_end_flow(self) -> bool:
        """测试端到端流程"""
        try:
            from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
            
            # 测试完整的消息处理流程
            test_cases = [
                ("健身训练咨询", "我想了解如何开始健身训练"),
                ("营养建议", "请给我一些健康饮食建议"),
                ("一般聊天", "今天天气不错"),
                ("空消息处理", ""),
                ("特殊字符", "🏋️‍♂️💪🔥")
            ]
            
            results = []
            for test_name, message in test_cases:
                try:
                    response = await basic_test_graph.process_message(
                        message=message,
                        conversation_id=f"e2e_test_{test_name}",
                        user_info={"user_id": f"e2e_user_{test_name}"}
                    )
                    
                    # 验证响应基本结构
                    is_valid = (
                        response.get("success") is not None and
                        response.get("response") is not None and
                        response.get("conversation_id") is not None
                    )
                    
                    results.append(is_valid)
                    print(f"   {test_name}: {'✅' if is_valid else '❌'}")
                    
                except Exception as e:
                    print(f"   {test_name}: ❌ ({str(e)})")
                    results.append(False)
            
            return all(results)
            
        except Exception as e:
            print(f"端到端测试失败: {str(e)}")
            return False
    
    async def run_performance_tests(self) -> bool:
        """运行性能测试"""
        print("⚡ 运行性能测试...")
        
        try:
            from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
            
            # 性能测试参数
            test_message = "我想了解健身训练"
            num_requests = 10
            
            # 单次请求性能测试
            response_times = []
            
            for i in range(num_requests):
                start_time = time.time()
                
                response = await basic_test_graph.process_message(
                    message=test_message,
                    conversation_id=f"perf_test_{i}",
                    user_info={"user_id": f"perf_user_{i}"}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # 转换为毫秒
                response_times.append(response_time)
                
                # 验证响应成功
                if not response.get("success", False):
                    print(f"   请求 {i+1} 失败")
                    return False
            
            # 计算性能指标
            avg_time = statistics.mean(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            p95_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            
            print(f"📊 性能测试结果 ({num_requests} 次请求):")
            print(f"   平均响应时间: {avg_time:.2f}ms")
            print(f"   最小响应时间: {min_time:.2f}ms")
            print(f"   最大响应时间: {max_time:.2f}ms")
            print(f"   95%响应时间: {p95_time:.2f}ms")
            
            # 性能标准：平均响应时间不超过5秒，95%响应时间不超过10秒
            performance_ok = avg_time < 5000 and p95_time < 10000
            
            if performance_ok:
                print("✅ 性能测试通过")
            else:
                print("❌ 性能测试失败：响应时间过长")
            
            # 保存性能指标
            self.performance_metrics = {
                "avg_response_time_ms": avg_time,
                "min_response_time_ms": min_time,
                "max_response_time_ms": max_time,
                "p95_response_time_ms": p95_time,
                "total_requests": num_requests,
                "success_rate": 100.0  # 所有请求都成功才到这里
            }
            
            return performance_ok
            
        except Exception as e:
            print(f"❌ 性能测试失败: {str(e)}")
            return False
    
    async def run_error_handling_tests(self) -> bool:
        """运行错误处理测试"""
        print("🛡️ 运行错误处理测试...")
        
        try:
            from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
            
            # 错误场景测试
            error_test_cases = [
                ("空消息", ""),
                ("超长消息", "x" * 10000),
                ("特殊字符", "!@#$%^&*()"),
                ("SQL注入尝试", "'; DROP TABLE users; --"),
                ("XSS尝试", "<script>alert('xss')</script>"),
            ]
            
            results = []
            for test_name, message in error_test_cases:
                try:
                    response = await basic_test_graph.process_message(
                        message=message,
                        conversation_id=f"error_test_{test_name}",
                        user_info={"user_id": f"error_user_{test_name}"}
                    )
                    
                    # 验证系统能够处理错误输入而不崩溃
                    is_handled = (
                        response is not None and
                        isinstance(response, dict) and
                        "response" in response
                    )
                    
                    results.append(is_handled)
                    print(f"   {test_name}: {'✅' if is_handled else '❌'}")
                    
                except Exception as e:
                    print(f"   {test_name}: ❌ 系统崩溃 ({str(e)})")
                    results.append(False)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"📊 错误处理测试: {success_count}/{total_count} 通过")
            
            return success_count == total_count
            
        except Exception as e:
            print(f"❌ 错误处理测试失败: {str(e)}")
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🧪 LangGraph综合测试套件")
        print("=" * 60)
        
        start_time = time.time()
        
        # 运行各类测试
        unit_result = await self.run_unit_tests()
        integration_result = await self.run_integration_tests()
        performance_result = await self.run_performance_tests()
        error_handling_result = await self.run_error_handling_tests()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 汇总结果
        test_results = {
            "unit_tests": unit_result,
            "integration_tests": integration_result,
            "performance_tests": performance_result,
            "error_handling_tests": error_handling_result
        }
        
        success_count = sum(1 for result in test_results.values() if result)
        total_count = len(test_results)
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        
        for test_type, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_type}: {status}")
        
        overall_success = success_count == total_count
        print(f"\n🎯 总体结果: {'🎉 所有测试通过' if overall_success else '❌ 部分测试失败'}")
        print(f"⏱️ 总测试时间: {total_time:.2f}秒")
        
        # 返回详细结果
        return {
            "overall_success": overall_success,
            "test_results": test_results,
            "performance_metrics": self.performance_metrics,
            "total_time_seconds": total_time,
            "success_rate": (success_count / total_count) * 100
        }

async def main():
    """主函数"""
    test_suite = LangGraphTestSuite()
    results = await test_suite.run_all_tests()
    
    # 输出最终结果
    if results["overall_success"]:
        print("\n🎉 LangGraph阶段二测试验证完成！")
        exit(0)
    else:
        print("\n❌ 测试验证失败，需要修复问题。")
        exit(1)

if __name__ == "__main__":
    asyncio.run(main())
