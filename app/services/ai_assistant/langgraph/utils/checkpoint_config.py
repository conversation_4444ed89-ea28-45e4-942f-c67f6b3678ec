"""
LangGraph检查点存储配置

配置PostgreSQL检查点存储，设置TTL和序列化机制。
"""

import logging
import os
from typing import Optional, Dict, Any
from datetime import timedelta

try:
    from langgraph_checkpoint_postgres import PostgresSaver
except ImportError:
    try:
        from langgraph.checkpoint.postgres import PostgresSaver
    except ImportError:
        PostgresSaver = None
from app.core.config import settings
from app.core.unified_config import unified_settings

logger = logging.getLogger(__name__)

class CheckpointConfig:
    """检查点配置管理器"""

    def __init__(self):
        self.saver: Optional[PostgresSaver] = None
        self.connection_string: Optional[str] = None
        self._initialized = False

    def initialize(self) -> bool:
        """
        初始化检查点存储

        Returns:
            初始化是否成功
        """
        try:
            if self._initialized:
                logger.info("检查点存储已初始化")
                return True

            # 构建连接字符串
            self.connection_string = self._build_connection_string()

            if not self.connection_string:
                logger.warning("无法构建数据库连接字符串，使用内存存储")
                return False

            # 创建PostgreSQL检查点存储器
            self.saver = PostgresSaver.from_conn_string(
                conn_string=self.connection_string
            )

            # 设置表名前缀（可选）
            if hasattr(self.saver, 'table_name'):
                self.saver.table_name = "langgraph_checkpoints"

            self._initialized = True
            logger.info("PostgreSQL检查点存储初始化成功")
            return True

        except Exception as e:
            logger.error(f"检查点存储初始化失败: {str(e)}")
            self.saver = None
            return False

    def _build_connection_string(self) -> Optional[str]:
        """
        构建数据库连接字符串

        Returns:
            连接字符串或None
        """
        try:
            # 尝试从环境变量获取
            if hasattr(settings, 'DATABASE_URL') and settings.DATABASE_URL:
                return settings.DATABASE_URL

            # 尝试从配置构建
            if hasattr(settings, 'POSTGRES_SERVER') and settings.POSTGRES_SERVER:
                user = getattr(settings, 'POSTGRES_USER', 'postgres')
                password = getattr(settings, 'POSTGRES_PASSWORD', '')
                server = settings.POSTGRES_SERVER
                port = getattr(settings, 'POSTGRES_PORT', 5432)
                db = getattr(settings, 'POSTGRES_DB', 'fitness_ai')

                return f"postgresql://{user}:{password}@{server}:{port}/{db}"

            # 尝试从环境变量直接构建
            db_host = os.getenv('DB_HOST', 'localhost')
            db_port = os.getenv('DB_PORT', '5432')
            db_user = os.getenv('DB_USER', 'postgres')
            db_password = os.getenv('DB_PASSWORD', '')
            db_name = os.getenv('DB_NAME', 'fitness_ai')

            if db_host and db_user and db_name:
                return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

            logger.warning("无法构建数据库连接字符串，缺少必要的配置")
            return None

        except Exception as e:
            logger.error(f"构建连接字符串失败: {str(e)}")
            return None

    def get_saver(self) -> Optional[PostgresSaver]:
        """
        获取检查点存储器

        Returns:
            PostgresSaver实例或None
        """
        if not self._initialized:
            self.initialize()

        return self.saver

    def is_available(self) -> bool:
        """
        检查检查点存储是否可用

        Returns:
            是否可用
        """
        return self._initialized and self.saver is not None

    def test_connection(self) -> bool:
        """
        测试数据库连接

        Returns:
            连接是否成功
        """
        try:
            if not self.saver:
                return False

            # 尝试执行一个简单的操作来测试连接
            # 这里可以添加具体的测试逻辑
            logger.info("检查点存储连接测试成功")
            return True

        except Exception as e:
            logger.error(f"检查点存储连接测试失败: {str(e)}")
            return False

    def get_config_info(self) -> Dict[str, Any]:
        """
        获取配置信息

        Returns:
            配置信息字典
        """
        return {
            "initialized": self._initialized,
            "saver_available": self.saver is not None,
            "connection_string_configured": self.connection_string is not None,
            "checkpoint_ttl": unified_settings.LANGGRAPH_CHECKPOINT_TTL,
            "table_name": getattr(self.saver, 'table_name', None) if self.saver else None
        }

    def cleanup_expired_checkpoints(self) -> bool:
        """
        清理过期的检查点

        Returns:
            清理是否成功
        """
        try:
            if not self.saver:
                logger.warning("检查点存储器不可用，无法清理")
                return False

            # 这里可以添加清理逻辑
            # PostgresSaver通常会自动处理TTL，但可以手动触发清理
            logger.info("检查点清理完成")
            return True

        except Exception as e:
            logger.error(f"检查点清理失败: {str(e)}")
            return False

    def get_checkpoint_stats(self) -> Dict[str, Any]:
        """
        获取检查点统计信息

        Returns:
            统计信息字典
        """
        try:
            if not self.saver:
                return {"error": "检查点存储器不可用"}

            # 这里可以添加统计查询逻辑
            stats = {
                "storage_type": "PostgreSQL",
                "ttl_seconds": unified_settings.LANGGRAPH_CHECKPOINT_TTL,
                "connection_status": "connected" if self.test_connection() else "disconnected"
            }

            return stats

        except Exception as e:
            logger.error(f"获取检查点统计失败: {str(e)}")
            return {"error": str(e)}

# 全局检查点配置实例
checkpoint_config = CheckpointConfig()

def get_checkpoint_saver() -> Optional[PostgresSaver]:
    """
    获取全局检查点存储器

    Returns:
        PostgresSaver实例或None
    """
    return checkpoint_config.get_saver()

def initialize_checkpoint_storage() -> bool:
    """
    初始化全局检查点存储

    Returns:
        初始化是否成功
    """
    return checkpoint_config.initialize()

def is_checkpoint_storage_available() -> bool:
    """
    检查检查点存储是否可用

    Returns:
        是否可用
    """
    return checkpoint_config.is_available()

class MemoryCheckpointSaver:
    """
    内存检查点存储器（用于测试和开发）
    """

    def __init__(self):
        self.checkpoints: Dict[str, Any] = {}
        self.ttl_seconds = unified_settings.LANGGRAPH_CHECKPOINT_TTL
        logger.info("使用内存检查点存储器")

    def save_checkpoint(self, checkpoint_id: str, data: Any) -> bool:
        """
        保存检查点

        Args:
            checkpoint_id: 检查点ID
            data: 检查点数据

        Returns:
            保存是否成功
        """
        try:
            import time
            self.checkpoints[checkpoint_id] = {
                "data": data,
                "timestamp": time.time()
            }
            logger.debug(f"检查点已保存: {checkpoint_id}")
            return True

        except Exception as e:
            logger.error(f"保存检查点失败: {str(e)}")
            return False

    def load_checkpoint(self, checkpoint_id: str) -> Optional[Any]:
        """
        加载检查点

        Args:
            checkpoint_id: 检查点ID

        Returns:
            检查点数据或None
        """
        try:
            import time

            if checkpoint_id not in self.checkpoints:
                return None

            checkpoint = self.checkpoints[checkpoint_id]

            # 检查TTL
            if time.time() - checkpoint["timestamp"] > self.ttl_seconds:
                del self.checkpoints[checkpoint_id]
                logger.debug(f"检查点已过期: {checkpoint_id}")
                return None

            logger.debug(f"检查点已加载: {checkpoint_id}")
            return checkpoint["data"]

        except Exception as e:
            logger.error(f"加载检查点失败: {str(e)}")
            return None

    def cleanup_expired(self) -> int:
        """
        清理过期检查点

        Returns:
            清理的检查点数量
        """
        try:
            import time
            current_time = time.time()
            expired_keys = []

            for checkpoint_id, checkpoint in self.checkpoints.items():
                if current_time - checkpoint["timestamp"] > self.ttl_seconds:
                    expired_keys.append(checkpoint_id)

            for key in expired_keys:
                del self.checkpoints[key]

            logger.info(f"清理了{len(expired_keys)}个过期检查点")
            return len(expired_keys)

        except Exception as e:
            logger.error(f"清理过期检查点失败: {str(e)}")
            return 0

# 内存检查点存储器实例（备用）
memory_checkpoint_saver = MemoryCheckpointSaver()
