"""
状态工具类

提供UnifiedFitnessState的操作方法，因为TypedDict不支持方法。
"""

import logging
import uuid
from typing import Dict, Any, List
from datetime import datetime
from langchain_core.messages import HumanMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState

logger = logging.getLogger(__name__)

class StateUtils:
    """状态工具类"""
    
    @staticmethod
    def create_initial_state(
        conversation_id: str = "",
        user_id: str = "",
        session_id: str = "",
        message: str = ""
    ) -> UnifiedFitnessState:
        """
        创建初始状态
        
        Args:
            conversation_id: 对话ID
            user_id: 用户ID
            session_id: 会话ID
            message: 用户消息
            
        Returns:
            初始化的状态字典
        """
        state: UnifiedFitnessState = {
            # 基础会话信息
            "conversation_id": conversation_id or "unknown",
            "user_id": user_id or "unknown",
            "session_id": session_id or "unknown",
            "timestamp": datetime.now(),
            
            # 意图识别结果
            "intent": "",
            "confidence": 0.0,
            "intent_parameters": {},
            "enhanced_intent_result": None,
            "original_intent": None,
            
            # 用户信息
            "user_profile": {},
            "user_preferences": {},
            "user_context": {},
            
            # 训练参数和健身数据
            "training_params": {},
            "fitness_goals": [],
            "current_workout": None,
            "exercise_history": [],
            
            # 流程状态和控制
            "flow_state": {},
            "current_state_name": "idle",
            "previous_state_name": "",
            "state_transition_history": [],
            
            # 系统状态和路由信息
            "current_node": "",
            "processing_system": "",
            "processing_path": [],
            "routing_decision": {},
            
            # 响应信息
            "response_content": "",
            "response_type": "text",
            "structured_data": {},
            "response_metadata": {},
            
            # 错误处理和重试
            "error_count": 0,
            "last_error": None,
            "retry_count": 0,
            "max_retries": 3,
            
            # 性能指标
            "processing_start_time": None,
            "processing_end_time": None,
            "node_execution_times": {},
            "total_processing_time": 0.0,
            
            # LangGraph特定字段
            "graph_execution_id": str(uuid.uuid4()),
            "langgraph_checkpoint_id": None,
            "parallel_results": [],
            "selected_result": None,
            
            # 上下文和历史
            "conversation_history": [],
            "context_summary": "",
            "long_term_memory": {},
            
            # 配置和控制标志
            "enable_streaming": True,
            "enable_parallel_processing": True,
            "enable_human_in_loop": False,
            "debug_mode": False,
            
            # 消息历史
            "messages": []
        }
        
        # 添加用户消息
        if message:
            try:
                human_message = HumanMessage(content=message)
                state["messages"].append(human_message)
            except Exception as e:
                logger.warning(f"添加用户消息失败: {str(e)}")
        
        return state
    
    @staticmethod
    def update_processing_metrics(state: UnifiedFitnessState, node_name: str, execution_time: float):
        """更新处理指标"""
        state["node_execution_times"][node_name] = execution_time
        state["total_processing_time"] += execution_time
        
        if not state["processing_start_time"]:
            state["processing_start_time"] = datetime.now().timestamp()
        
        state["processing_end_time"] = datetime.now().timestamp()
    
    @staticmethod
    def add_processing_step(state: UnifiedFitnessState, step: str):
        """添加处理步骤到路径追踪"""
        state["processing_path"].append(step)
        logger.debug(f"处理路径: {' -> '.join(state['processing_path'])}")
    
    @staticmethod
    def set_error(state: UnifiedFitnessState, error_message: str, increment_count: bool = True):
        """设置错误信息"""
        state["last_error"] = error_message
        if increment_count:
            state["error_count"] += 1
        
        logger.error(f"状态错误 (#{state['error_count']}): {error_message}")
    
    @staticmethod
    def can_retry(state: UnifiedFitnessState) -> bool:
        """检查是否可以重试"""
        return state["retry_count"] < state["max_retries"]
    
    @staticmethod
    def increment_retry(state: UnifiedFitnessState):
        """增加重试计数"""
        state["retry_count"] += 1
        logger.info(f"重试计数: {state['retry_count']}/{state['max_retries']}")
    
    @staticmethod
    def reset_error_state(state: UnifiedFitnessState):
        """重置错误状态"""
        state["last_error"] = None
        state["retry_count"] = 0
        logger.debug("错误状态已重置")
    
    @staticmethod
    def add_parallel_result(state: UnifiedFitnessState, result: Dict[str, Any]):
        """添加并行处理结果"""
        state["parallel_results"].append(result)
        logger.debug(f"添加并行结果，当前结果数: {len(state['parallel_results'])}")
    
    @staticmethod
    def clear_parallel_results(state: UnifiedFitnessState):
        """清空并行处理结果"""
        state["parallel_results"].clear()
        state["selected_result"] = None
        logger.debug("并行处理结果已清空")
    
    @staticmethod
    def get_summary(state: UnifiedFitnessState) -> Dict[str, Any]:
        """获取状态摘要"""
        return {
            "conversation_id": state["conversation_id"],
            "user_id": state["user_id"],
            "current_state": state["current_state_name"],
            "current_node": state["current_node"],
            "intent": state["intent"],
            "confidence": state["confidence"],
            "processing_system": state["processing_system"],
            "processing_path": state["processing_path"],
            "error_count": state["error_count"],
            "retry_count": state["retry_count"],
            "total_processing_time": state["total_processing_time"],
            "parallel_results_count": len(state["parallel_results"]),
            "messages_count": len(state["messages"]),
            "timestamp": state["timestamp"].isoformat() if state["timestamp"] else None
        }
    
    @staticmethod
    def validate(state: UnifiedFitnessState) -> bool:
        """验证状态的有效性"""
        try:
            # 检查必需字段
            if not state["conversation_id"]:
                logger.error("conversation_id不能为空")
                return False
            
            if not state["user_id"]:
                logger.error("user_id不能为空")
                return False
            
            # 检查置信度范围
            if not (0.0 <= state["confidence"] <= 1.0):
                logger.error(f"confidence必须在0-1范围内: {state['confidence']}")
                return False
            
            # 检查重试计数
            if state["retry_count"] < 0 or state["retry_count"] > state["max_retries"]:
                logger.error(f"retry_count超出范围: {state['retry_count']}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"状态验证失败: {str(e)}")
            return False
    
    @staticmethod
    def to_conversation_state_dict(state: UnifiedFitnessState) -> Dict[str, Any]:
        """转换为ConversationState兼容的字典格式"""
        return {
            "conversation_id": state["conversation_id"],
            "user_id": state["user_id"],
            "session_id": state["session_id"],
            "current_state": state["current_state_name"],
            "intent": state["intent"],
            "confidence": state["confidence"],
            "user_profile": state["user_profile"],
            "training_params": state["training_params"],
            "flow_state": state["flow_state"],
            "response_content": state["response_content"],
            "structured_data": state["structured_data"],
            "conversation_history": state["conversation_history"],
            "timestamp": state["timestamp"].isoformat() if state["timestamp"] else None
        }
    
    @staticmethod
    def from_conversation_state(conv_state: Dict[str, Any]) -> UnifiedFitnessState:
        """从ConversationState字典创建UnifiedFitnessState"""
        # 创建基础状态
        state = StateUtils.create_initial_state()
        
        # 映射基础字段
        state["conversation_id"] = conv_state.get("conversation_id", "")
        state["user_id"] = conv_state.get("user_id", "")
        state["session_id"] = conv_state.get("session_id", "")
        state["current_state_name"] = conv_state.get("current_state", "idle")
        state["intent"] = conv_state.get("intent", "")
        state["confidence"] = conv_state.get("confidence", 0.0)
        state["user_profile"] = conv_state.get("user_profile", {})
        state["training_params"] = conv_state.get("training_params", {})
        state["flow_state"] = conv_state.get("flow_state", {})
        state["response_content"] = conv_state.get("response_content", "")
        state["structured_data"] = conv_state.get("structured_data", {})
        state["conversation_history"] = conv_state.get("conversation_history", [])
        
        # 处理时间戳
        timestamp_str = conv_state.get("timestamp")
        if timestamp_str:
            try:
                state["timestamp"] = datetime.fromisoformat(timestamp_str)
            except (ValueError, TypeError):
                state["timestamp"] = datetime.now()
        else:
            state["timestamp"] = datetime.now()
        
        return state
