"""
运动数据处理工具类
提供运动ID解析、验证等功能
"""

from typing import Union, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import logging

from app.models.exercise import Exercise

logger = logging.getLogger(__name__)


class ExerciseDataProcessor:
    """运动数据处理工具类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def resolve_exercise_id(self, exercise_id: Union[int, str]) -> int:
        """
        解析运动ID，处理字符串类型的临时ID
        
        Args:
            exercise_id: 运动ID，可能是整数或字符串格式
            
        Returns:
            解析后的整数运动ID
            
        Raises:
            ValueError: 当无法解析ID或运动不存在时
        """
        try:
            # 如果已经是整数，直接验证并返回
            if isinstance(exercise_id, int):
                return self._validate_exercise_id(exercise_id)
            
            # 处理字符串类型的ID
            if isinstance(exercise_id, str):
                return self._resolve_string_exercise_id(exercise_id)
            
            # 其他类型，使用默认值
            logger.warning(f"未知的exercise_id类型: {type(exercise_id)}, 值: {exercise_id}")
            return self._get_default_exercise_id()
            
        except Exception as e:
            logger.error(f"解析exercise_id失败: {str(e)}")
            return self._get_default_exercise_id()
    
    def _resolve_string_exercise_id(self, exercise_id: str) -> int:
        """解析字符串类型的运动ID"""
        if exercise_id.startswith('added_exercise_'):
            # 处理临时运动ID，格式：added_exercise_{timestamp}_{exercise_id}_{random}
            return self._parse_temporary_exercise_id(exercise_id)
        elif exercise_id.isdigit():
            # 数字字符串，直接转换
            return self._validate_exercise_id(int(exercise_id))
        else:
            logger.warning(f"未知的exercise_id格式: {exercise_id}")
            return self._get_default_exercise_id()
    
    def _parse_temporary_exercise_id(self, temp_id: str) -> int:
        """
        解析临时运动ID
        格式：added_exercise_{timestamp}_{exercise_id}_{random}
        """
        try:
            parts = temp_id.split('_')
            if len(parts) >= 4:
                # 尝试从临时ID中提取真实的exercise_id（第3个部分）
                real_exercise_id = int(parts[3])
                return self._validate_exercise_id(real_exercise_id)
            else:
                logger.warning(f"临时exercise_id格式不正确: {temp_id}")
                return self._get_default_exercise_id()
        except (ValueError, IndexError) as e:
            logger.warning(f"解析临时exercise_id失败: {e}")
            return self._get_default_exercise_id()
    
    def _validate_exercise_id(self, exercise_id: int) -> int:
        """验证运动ID是否存在"""
        try:
            exercise = self.db.query(Exercise).filter(Exercise.id == exercise_id).first()
            if exercise:
                return exercise_id
            else:
                logger.warning(f"Exercise ID {exercise_id} 不存在")
                return self._get_default_exercise_id()
        except SQLAlchemyError as e:
            logger.error(f"验证exercise_id时数据库错误: {str(e)}")
            return self._get_default_exercise_id()
    
    def _get_default_exercise_id(self) -> int:
        """获取默认的运动ID"""
        try:
            first_exercise = self.db.query(Exercise).first()
            if first_exercise:
                logger.info(f"使用默认运动ID: {first_exercise.id}")
                return first_exercise.id
            else:
                logger.error("系统中没有可用的健身动作")
                raise ValueError("系统中没有可用的健身动作")
        except SQLAlchemyError as e:
            logger.error(f"获取默认运动ID时数据库错误: {str(e)}")
            raise ValueError("无法获取默认运动ID")
    
    def get_exercise_with_details(self, exercise_id: int) -> Optional[Exercise]:
        """
        获取包含详细信息的运动对象
        
        Args:
            exercise_id: 运动ID
            
        Returns:
            运动对象，包含详细信息
        """
        try:
            from sqlalchemy.orm import joinedload
            
            exercise = self.db.query(Exercise).options(
                joinedload(Exercise.details)
            ).filter(Exercise.id == exercise_id).first()
            
            return exercise
        except SQLAlchemyError as e:
            logger.error(f"获取运动详细信息失败: {str(e)}")
            return None
    
    def validate_exercise_exists(self, exercise_id: int) -> bool:
        """
        验证运动是否存在
        
        Args:
            exercise_id: 运动ID
            
        Returns:
            运动是否存在
        """
        try:
            exercise = self.db.query(Exercise).filter(Exercise.id == exercise_id).first()
            return exercise is not None
        except SQLAlchemyError as e:
            logger.error(f"验证运动存在性时数据库错误: {str(e)}")
            return False
    
    def get_exercise_basic_info(self, exercise_id: int) -> Optional[dict]:
        """
        获取运动基本信息
        
        Args:
            exercise_id: 运动ID
            
        Returns:
            运动基本信息字典
        """
        try:
            exercise = self.get_exercise_with_details(exercise_id)
            if not exercise:
                return None
            
            return {
                "id": exercise.id,
                "name": exercise.name,
                "en_name": exercise.en_name,
                "body_part_id": exercise.body_part_id,
                "equipment_id": exercise.equipment_id,
                "image_name": exercise.image_name,
                "gif_url": exercise.gif_url,
                "description": exercise.description,
                "level": exercise.level,
                "exercise_type": exercise.exercise_type,
                "target_muscles_id": exercise.details.target_muscles_id if exercise.details else None,
                "synergist_muscles_id": exercise.details.synergist_muscles_id if exercise.details else None
            }
        except Exception as e:
            logger.error(f"获取运动基本信息失败: {str(e)}")
            return None
