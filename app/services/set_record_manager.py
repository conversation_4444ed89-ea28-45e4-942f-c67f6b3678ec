"""
组记录管理工具类
提供组记录的创建、更新、查询等功能，处理字符串ID和整数ID的转换
"""

from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, date
import logging

from app.models.set_record import SetRecord
from app.models.workout_exercise import WorkoutExercise
from app.schemas.set_record import SetRecordCreate, SetRecordUpdate

logger = logging.getLogger(__name__)


class SetRecordManager:
    """组记录管理工具类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_set_records_for_exercise(
        self, 
        workout_exercise_id: int, 
        sets_count: int, 
        template_weight: Optional[str] = None,
        template_reps: Optional[str] = None,
        target_date: Optional[date] = None
    ) -> List[SetRecord]:
        """
        为训练动作创建组记录
        
        Args:
            workout_exercise_id: 训练动作ID
            sets_count: 组数
            template_weight: 模板重量
            template_reps: 模板次数
            target_date: 目标日期
            
        Returns:
            创建的组记录列表
        """
        try:
            created_records = []
            
            for set_num in range(1, sets_count + 1):
                # 解析重量和次数
                weight = self._parse_weight(template_weight)
                reps = self._parse_reps(template_reps)
                
                # 设置创建时间
                created_at = datetime.combine(target_date, datetime.min.time()) if target_date else None
                
                set_record = SetRecord(
                    workout_exercise_id=workout_exercise_id,
                    set_number=set_num,
                    set_type="normal",
                    weight=weight,
                    reps=reps,
                    completed=False,
                    created_at=created_at
                )
                
                self.db.add(set_record)
                created_records.append(set_record)
            
            self.db.flush()  # 获取ID但不提交
            return created_records
            
        except SQLAlchemyError as e:
            logger.error(f"创建组记录失败: {str(e)}")
            return []
    
    def get_set_records_by_exercise(self, workout_exercise_id: int) -> List[SetRecord]:
        """
        获取训练动作的所有组记录
        
        Args:
            workout_exercise_id: 训练动作ID
            
        Returns:
            组记录列表
        """
        try:
            records = self.db.query(SetRecord).filter(
                SetRecord.workout_exercise_id == workout_exercise_id
            ).order_by(SetRecord.set_number).all()
            
            return records
        except SQLAlchemyError as e:
            logger.error(f"获取组记录失败: {str(e)}")
            return []
    
    def update_set_record(
        self, 
        set_record_id: Union[int, str], 
        update_data: Dict[str, Any]
    ) -> Optional[SetRecord]:
        """
        更新组记录
        
        Args:
            set_record_id: 组记录ID，可能是整数或字符串
            update_data: 更新数据
            
        Returns:
            更新后的组记录
        """
        try:
            # 解析组记录ID
            record_id = self._resolve_set_record_id(set_record_id)
            if not record_id:
                return None
            
            # 获取现有记录
            set_record = self.db.query(SetRecord).filter(SetRecord.id == record_id).first()
            if not set_record:
                logger.warning(f"组记录不存在: {record_id}")
                return None
            
            # 更新字段
            self._update_set_record_fields(set_record, update_data)
            
            self.db.flush()
            return set_record
            
        except SQLAlchemyError as e:
            logger.error(f"更新组记录失败: {str(e)}")
            return None
    
    def create_set_record(
        self, 
        workout_exercise_id: int, 
        set_data: Dict[str, Any]
    ) -> Optional[SetRecord]:
        """
        创建新的组记录
        
        Args:
            workout_exercise_id: 训练动作ID
            set_data: 组记录数据
            
        Returns:
            创建的组记录
        """
        try:
            # 如果没有提供组号，自动分配
            if "set_number" not in set_data:
                existing_records = self.get_set_records_by_exercise(workout_exercise_id)
                set_data["set_number"] = len(existing_records) + 1
            
            set_record = SetRecord(
                workout_exercise_id=workout_exercise_id,
                set_number=set_data.get("set_number", 1),
                set_type=set_data.get("set_type", "normal"),
                weight=set_data.get("weight"),
                reps=set_data.get("reps"),
                completed=set_data.get("completed", False),
                notes=set_data.get("notes")
            )
            
            self.db.add(set_record)
            self.db.flush()
            
            return set_record
            
        except SQLAlchemyError as e:
            logger.error(f"创建组记录失败: {str(e)}")
            return None
    
    def _resolve_set_record_id(self, set_record_id: Union[int, str]) -> Optional[int]:
        """
        解析组记录ID
        
        Args:
            set_record_id: 组记录ID，可能是整数或字符串
            
        Returns:
            解析后的整数ID
        """
        try:
            # 如果是整数，直接返回
            if isinstance(set_record_id, int):
                return set_record_id
            
            # 如果是字符串，尝试转换为整数
            if isinstance(set_record_id, str):
                if set_record_id.isdigit():
                    return int(set_record_id)
                else:
                    # 处理特殊格式的字符串ID，如 "62_set_1"
                    return self._parse_complex_set_id(set_record_id)
            
            return None
            
        except (ValueError, TypeError) as e:
            logger.warning(f"解析组记录ID失败: {set_record_id}, 错误: {str(e)}")
            return None
    
    def _parse_complex_set_id(self, complex_id: str) -> Optional[int]:
        """
        解析复杂格式的组记录ID
        
        Args:
            complex_id: 复杂格式的ID字符串
            
        Returns:
            解析后的整数ID，如果无法解析则返回None
        """
        try:
            # 尝试从复杂ID中提取数字部分
            # 例如："62_set_1" -> 尝试查找对应的记录
            parts = complex_id.split('_')
            if len(parts) >= 1 and parts[0].isdigit():
                potential_id = int(parts[0])
                # 验证这个ID是否存在
                record = self.db.query(SetRecord).filter(SetRecord.id == potential_id).first()
                if record:
                    return potential_id
            
            return None
            
        except Exception as e:
            logger.warning(f"解析复杂组记录ID失败: {complex_id}, 错误: {str(e)}")
            return None
    
    def _update_set_record_fields(self, set_record: SetRecord, update_data: Dict[str, Any]):
        """更新组记录字段"""
        field_mappings = {
            'set_number': 'set_number',
            'set_type': 'set_type',
            'weight': 'weight',
            'reps': 'reps',
            'completed': 'completed',
            'notes': 'notes'
        }
        
        for field, db_field in field_mappings.items():
            if field in update_data:
                value = update_data[field]
                # 特殊处理某些字段
                if field == 'weight' and value is not None:
                    value = float(value)
                elif field == 'reps' and value is not None:
                    value = int(value)
                elif field == 'completed':
                    value = bool(value)
                
                setattr(set_record, db_field, value)
    
    def _parse_weight(self, weight_str: Optional[str]) -> Optional[float]:
        """解析重量字符串"""
        if not weight_str:
            return None
        
        try:
            # 移除可能的单位和空格
            clean_weight = weight_str.replace('kg', '').replace('KG', '').strip()
            if clean_weight and clean_weight.replace('.', '').isdigit():
                return float(clean_weight)
        except (ValueError, AttributeError):
            pass
        
        return None
    
    def _parse_reps(self, reps_str: Optional[str]) -> Optional[int]:
        """解析次数字符串"""
        if not reps_str:
            return None
        
        try:
            # 如果是纯数字字符串
            if reps_str.isdigit():
                return int(reps_str)
            
            # 如果是范围格式，如 "10-12"，取中间值
            if '-' in reps_str:
                parts = reps_str.split('-')
                if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                    return int((int(parts[0]) + int(parts[1])) / 2)
        except (ValueError, AttributeError):
            pass
        
        return None
    
    def get_set_records_dict(self, workout_exercise_id: int) -> List[Dict[str, Any]]:
        """
        获取组记录的字典格式数据
        
        Args:
            workout_exercise_id: 训练动作ID
            
        Returns:
            组记录字典列表
        """
        records = self.get_set_records_by_exercise(workout_exercise_id)
        
        return [
            {
                "id": record.id,
                "set_number": record.set_number,
                "set_type": record.set_type,
                "weight": record.weight,
                "reps": record.reps,
                "completed": record.completed,
                "notes": record.notes,
                "created_at": record.created_at.isoformat() if record.created_at else None,
                "updated_at": record.updated_at.isoformat() if record.updated_at else None
            }
            for record in records
        ]
