# 错误处理机制分析

本文档分析智能健身AI助手系统的错误处理机制，包括错误检测、恢复策略和容错设计。

## 1. 错误处理架构

系统采用分层错误处理架构，在不同层级实现相应的错误处理机制：

1. **HTTP/WebSocket层**：处理网络和请求异常
2. **服务层**：处理业务逻辑异常
3. **LangGraph节点层**：处理AI处理异常
4. **数据库层**：处理数据存储异常
5. **外部集成层**：处理第三方服务异常

### 1.1 整体错误处理流程

```mermaid
graph TD
    A[客户端请求] --> B{API网关}
    B -->|错误| C[HTTP错误处理]
    B -->|正常| D{服务层}
    D -->|错误| E[服务层错误处理]
    D -->|正常| F{LangGraph引擎}
    F -->|错误| G[LangGraph错误处理]
    F -->|正常| H{数据库操作}
    H -->|错误| I[数据库错误处理]
    H -->|正常| J[正常响应]
    
    C --> K[错误响应]
    E --> K
    G --> K
    I --> K
    J --> L[客户端接收]
    K --> L
```

## 2. HTTP/WebSocket层错误处理

### 2.1 HTTP异常处理

系统使用FastAPI的异常处理器统一处理HTTP异常：

```python
# app/api/errors/http_error.py
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

async def http_error_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """处理HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "code": exc.status_code}
    )

async def validation_error_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """处理请求验证异常"""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"error": "请求参数验证失败", "details": exc.errors(), "code": 422}
    )

# app/main.py
from fastapi import FastAPI
from starlette.exceptions import HTTPException as StarletteHTTPException
from fastapi.exceptions import RequestValidationError

from app.api.errors.http_error import http_error_handler, validation_error_handler

app = FastAPI()

# 注册异常处理器
app.add_exception_handler(StarletteHTTPException, http_error_handler)
app.add_exception_handler(RequestValidationError, validation_error_handler)
```

### 2.2 WebSocket异常处理

WebSocket连接的错误处理机制：

```python
# app/api/endpoints/websocket.py
@router.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str, db: Session = Depends(deps.get_db)):
    """WebSocket端点"""
    try:
        await websocket.accept()
        # 用户认证
        authenticated = False
        
        # 等待认证消息
        try:
            auth_data = await asyncio.wait_for(websocket.receive_json(), timeout=10.0)
            # 验证认证信息
            if auth_data.get("token"):
                # 验证token
                user = await verify_token(auth_data.get("token"), db)
                if user:
                    authenticated = True
                    user_id = user.id
        except asyncio.TimeoutError:
            await websocket.close(code=1008, reason="认证超时")
            return
        except Exception as e:
            await websocket.close(code=1011, reason=f"认证过程出错: {str(e)}")
            return
            
        if not authenticated:
            await websocket.close(code=1008, reason="认证失败")
            return
            
        # 主消息处理循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_json()
                # 处理消息
                # ...
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端断开连接: {session_id}")
                break
            except Exception as e:
                error_message = f"处理WebSocket消息时出错: {str(e)}"
                logger.error(error_message)
                try:
                    await websocket.send_json({
                        "error": error_message,
                        "code": "internal_error"
                    })
                except:
                    break
    except Exception as e:
        logger.error(f"WebSocket连接处理失败: {str(e)}")
        try:
            await websocket.close(code=1011, reason="服务器内部错误")
        except:
            pass
```

## 3. 服务层错误处理

### 3.1 全局错误处理基类

```python
# app/core/error_handler.py
class ErrorHandler:
    """错误处理基类"""
    
    @staticmethod
    def handle_exception(func):
        """异常处理装饰器"""
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ValidationError as e:
                # 处理验证错误
                logger.warning(f"验证错误: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={"message": "数据验证失败", "errors": e.errors()}
                )
            except PermissionError as e:
                # 处理权限错误
                logger.warning(f"权限错误: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail={"message": str(e)}
                )
            except ResourceNotFoundError as e:
                # 处理资源不存在错误
                logger.warning(f"资源不存在: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={"message": str(e)}
                )
            except BusinessLogicError as e:
                # 处理业务逻辑错误
                logger.warning(f"业务逻辑错误: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"message": str(e)}
                )
            except Exception as e:
                # 处理未捕获的异常
                logger.error(f"未捕获的异常: {str(e)}", exc_info=True)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail={"message": "服务器内部错误"}
                )
        return wrapper
```

### 3.2 业务错误类型

```python
# app/core/exceptions.py
class BusinessLogicError(Exception):
    """业务逻辑错误基类"""
    pass

class ResourceNotFoundError(BusinessLogicError):
    """资源不存在错误"""
    pass

class AuthenticationError(BusinessLogicError):
    """认证错误"""
    pass

class PermissionError(BusinessLogicError):
    """权限错误"""
    pass

class ValidationError(BusinessLogicError):
    """验证错误"""
    def __init__(self, message, errors):
        super().__init__(message)
        self.errors = errors

class ExternalServiceError(BusinessLogicError):
    """外部服务错误"""
    pass

class AIProcessingError(BusinessLogicError):
    """AI处理错误"""
    pass

class DatabaseError(BusinessLogicError):
    """数据库错误"""
    pass
```

## 4. LangGraph节点层错误处理

### 4.1 节点内错误处理

每个LangGraph节点包含try-except块捕获并处理异常：

```python
# app/services/ai_assistant/langgraph/nodes/training_plan_expert.py
async def training_plan_expert_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """训练计划专家节点"""
    try:
        # 处理逻辑...
        return state
    except Exception as e:
        # 记录错误
        logger.error(f"训练计划专家节点处理失败: {str(e)}")
        
        # 更新状态中的错误计数和信息
        state["error_count"] = state.get("error_count", 0) + 1
        state["last_error"] = f"训练计划生成失败: {str(e)}"
        
        # 设置回退响应
        if not state.get("response_content"):
            state["response_content"] = "抱歉，我在生成训练计划时遇到了问题。请提供更明确的健身目标和您的身体状况，我会再次尝试。"
        
        return state
```

### 4.2 错误监控节点

系统实现了专门的错误监控节点，检测和处理图执行过程中的错误：

```python
# app/services/ai_assistant/langgraph/nodes/error_monitor.py
async def error_monitor_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """错误监控节点"""
    # 检查状态中的错误信息
    error_count = state.get("error_count", 0)
    last_error = state.get("last_error")
    
    if error_count > 0 and last_error:
        # 记录错误
        logger.warning(f"检测到错误 (计数: {error_count}): {last_error}")
        
        # 检查是否达到最大重试次数
        max_retries = state.get("max_retries", 3)
        retry_count = state.get("retry_count", 0)
        
        if retry_count < max_retries:
            # 增加重试计数
            state["retry_count"] = retry_count + 1
            
            # 根据错误类型确定重试策略
            if "连接错误" in last_error or "超时" in last_error:
                # 网络相关错误，添加短暂延迟
                await asyncio.sleep(1)
            
            # 清除错误状态，准备重试
            state["error_count"] = 0
            state["last_error"] = None
            
            # 设置重试标志
            state["flow_state"]["retry_mode"] = True
            
            logger.info(f"准备第 {state['retry_count']} 次重试")
        else:
            # 达到最大重试次数，设置最终错误响应
            logger.error(f"达到最大重试次数 ({max_retries})，放弃处理")
            
            # 设置友好的错误响应
            state["response_content"] = "非常抱歉，我无法完成您的请求。可能是系统暂时遇到了问题，请稍后再试。"
            
            # 设置错误标志
            state["flow_state"]["fatal_error"] = True
    
    return state
```

## 5. 数据库层错误处理

### 5.1 数据库操作错误处理

CRUD操作中的错误处理：

```python
# app/crud/base.py
def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
    """创建对象"""
    try:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    except IntegrityError as e:
        db.rollback()
        if "unique constraint" in str(e).lower():
            raise BusinessLogicError("资源已存在") from e
        else:
            raise DatabaseError(f"数据库约束错误: {str(e)}") from e
    except SQLAlchemyError as e:
        db.rollback()
        raise DatabaseError(f"数据库操作错误: {str(e)}") from e
```

### 5.2 事务管理

使用上下文管理器确保事务一致性：

```python
# app/services/user_service.py
async def create_user_with_profile(user_data, profile_data, db):
    """创建用户并关联用户档案"""
    try:
        # 创建用户
        user = crud_user.create(db, obj_in=user_data)
        
        # 关联用户ID到档案数据
        profile_data.user_id = user.id
        
        # 创建用户档案
        profile = crud_user_profile.create(db, obj_in=profile_data)
        
        # 提交事务
        db.commit()
        
        return {"user": user, "profile": profile}
    except Exception as e:
        # 回滚事务
        db.rollback()
        logger.error(f"创建用户失败: {str(e)}")
        raise BusinessLogicError(f"创建用户失败: {str(e)}") from e
```

## 6. 外部集成层错误处理

### 6.1 外部API调用错误处理

```python
# app/services/external_service.py
async def call_external_api(url, method="GET", data=None, headers=None, timeout=10):
    """调用外部API"""
    try:
        # 设置超时
        timeout = httpx.Timeout(timeout)
        
        # 创建客户端
        async with httpx.AsyncClient(timeout=timeout) as client:
            if method == "GET":
                response = await client.get(url, headers=headers)
            elif method == "POST":
                response = await client.post(url, json=data, headers=headers)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            # 检查响应状态
            response.raise_for_status()
            
            return response.json()
    except httpx.TimeoutException:
        logger.error(f"调用外部API超时: {url}")
        raise ExternalServiceError(f"外部服务响应超时") from None
    except httpx.HTTPStatusError as e:
        logger.error(f"外部API返回错误状态: {e.response.status_code} - {url}")
        raise ExternalServiceError(f"外部服务返回错误: {e.response.status_code}") from e
    except httpx.RequestError as e:
        logger.error(f"请求外部API失败: {url} - {str(e)}")
        raise ExternalServiceError(f"无法连接到外部服务") from e
    except Exception as e:
        logger.error(f"调用外部API时发生未预期错误: {url} - {str(e)}")
        raise ExternalServiceError(f"与外部服务通信时发生错误") from e
```

## 7. 用户反馈和恢复机制

### 7.1 用户友好错误消息

系统定义了用户友好的错误消息模板：

```python
# app/core/error_messages.py
ERROR_MESSAGES = {
    "connection_error": "抱歉，我暂时无法连接到服务器。请稍后再试。",
    "timeout_error": "抱歉，请求处理超时。请尝试简化您的问题或稍后再试。",
    "validation_error": "您提供的信息有误。{details}",
    "authentication_error": "您需要登录后才能使用此功能。",
    "permission_error": "您没有权限执行此操作。",
    "resource_not_found": "未找到您请求的资源。",
    "rate_limit_error": "您的请求过于频繁，请稍后再试。",
    "internal_error": "系统内部出现问题。我们的团队已收到通知并正在处理。",
    "llm_error": "AI模型暂时无法正常工作。请稍后再试。",
    "invalid_parameter": "参数'{param}'无效: {reason}",
    "data_not_available": "所请求的数据暂时不可用。",
    "feature_not_available": "此功能暂时不可用。",
    "database_error": "数据存储服务暂时不可用。请稍后再试。"
}

def get_user_friendly_error(error_type, **kwargs):
    """获取用户友好的错误消息"""
    message = ERROR_MESSAGES.get(error_type, ERROR_MESSAGES["internal_error"])
    if kwargs:
        message = message.format(**kwargs)
    return message
```

### 7.2 会话恢复机制

系统实现了会话恢复机制，从错误中恢复会话：

```python
# app/services/ai_assistant/conversation/recovery.py
class ConversationRecovery:
    """会话恢复服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def recover_session(self, session_id: str) -> Dict[str, Any]:
        """恢复会话"""
        try:
            # 获取会话信息
            conversation = crud_conversation.get_by_session_id(self.db, session_id=session_id)
            if not conversation:
                raise ResourceNotFoundError(f"会话不存在: {session_id}")
            
            # 获取会话消息
            messages = crud_message.get_multi_by_conversation(
                self.db, conversation_id=conversation.id, skip=0, limit=50
            )
            
            # 获取会话状态
            langgraph_state = None
            checkpoint_id = f"conversation_{conversation.id}"
            checkpointer = PostgreSQLCheckpointer(self.db)
            
            try:
                langgraph_state = checkpointer.get(checkpoint_id)
            except Exception as e:
                logger.warning(f"无法获取会话状态: {str(e)}")
            
            # 构建恢复上下文
            recovery_context = {
                "conversation_id": conversation.id,
                "session_id": session_id,
                "user_id": conversation.user_id,
                "messages": [
                    {"role": msg.role, "content": msg.content}
                    for msg in messages
                ],
                "langgraph_state": langgraph_state,
                "recovered": True,
                "recovery_time": datetime.utcnow().isoformat()
            }
            
            return recovery_context
        except Exception as e:
            logger.error(f"恢复会话失败: {str(e)}")
            raise BusinessLogicError(f"恢复会话失败: {str(e)}") from e
```

## 8. 错误日志和监控

### 8.1 日志配置

系统使用结构化日志记录错误：

```python
# app/core/logging.py
import logging
import json
from datetime import datetime
import traceback
import sys

class StructuredLogFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        """格式化日志记录"""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # 添加额外信息
        if hasattr(record, "extra") and record.extra:
            log_data["extra"] = record.extra
        
        return json.dumps(log_data)

def setup_logging():
    """设置日志系统"""
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(StructuredLogFormatter())
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    file_handler = logging.FileHandler("logs/app.log")
    file_handler.setFormatter(StructuredLogFormatter())
    root_logger.addHandler(file_handler)
    
    # 错误文件处理器
    error_file_handler = logging.FileHandler("logs/error.log")
    error_file_handler.setLevel(logging.ERROR)
    error_file_handler.setFormatter(StructuredLogFormatter())
    root_logger.addHandler(error_file_handler)
```

### 8.2 错误指标收集

```python
# app/core/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# 错误计数器
error_counter = Counter(
    "app_errors_total",
    "Total number of application errors",
    ["error_type", "component"]
)

# 错误处理时间直方图
error_handling_time = Histogram(
    "app_error_handling_seconds",
    "Time spent handling errors",
    ["error_type", "component"]
)

# 当前活跃错误数
active_errors = Gauge(
    "app_active_errors",
    "Number of active errors being handled",
    ["component"]
)

def record_error(error_type, component, handling_time=None):
    """记录错误指标"""
    error_counter.labels(error_type=error_type, component=component).inc()
    
    if handling_time is not None:
        error_handling_time.labels(
            error_type=error_type, component=component
        ).observe(handling_time)

def track_active_error(component, is_active=True):
    """跟踪活跃错误"""
    if is_active:
        active_errors.labels(component=component).inc()
    else:
        active_errors.labels(component=component).dec()
```

## 9. 总结

智能健身AI助手系统实现了全面的错误处理机制，主要特点包括：

1. **分层错误处理**：在不同层级实现相应的错误处理，确保错误被及时捕获和处理
2. **统一异常类型**：定义统一的业务异常类型，便于错误分类和处理
3. **友好错误反馈**：向用户提供友好的错误信息，提升用户体验
4. **自动重试机制**：对可恢复错误实现自动重试，提高系统稳定性
5. **会话恢复**：支持从错误中恢复会话，减少用户数据丢失
6. **结构化日志**：使用结构化日志记录错误信息，便于分析和排查
7. **错误指标收集**：收集错误指标，支持系统监控和性能优化

通过这些机制，系统能够有效应对各种异常情况，提供稳定、可靠的服务，确保用户在遇到问题时能够获得及时的反馈和帮助。 