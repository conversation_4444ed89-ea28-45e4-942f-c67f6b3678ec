# 数据流处理文档

## 1. 数据流概览

智能健身AI助手系统的数据流设计遵循现代化的异步处理模式，支持实时流式响应和批量处理。数据在系统中经历多个转换阶段，从用户输入到AI响应输出，确保数据的完整性和一致性。

### 1.1 数据流架构图

```mermaid
graph TD
    A[用户输入] --> B[API层验证]
    B --> C[数据预处理]
    C --> D[意图识别]
    D --> E[状态管理]
    E --> F{路由决策}
    
    F -->|LangGraph| G[LangGraph处理]
    F -->|传统| H[状态机处理]
    F -->|智能| I[智能模块处理]
    
    G --> J[专家节点处理]
    H --> K[意图处理器]
    I --> L[学习引擎]
    
    J --> M[响应生成]
    K --> M
    L --> M
    
    M --> N[数据后处理]
    N --> O[响应格式化]
    O --> P[数据持久化]
    P --> Q[用户响应]
    
    style A fill:#e1f5fe
    style Q fill:#e8f5e8
    style F fill:#fff3e0
    style M fill:#fce4ec
```

### 1.2 数据类型定义

#### 1.2.1 输入数据类型
```python
# app/schemas/chat.py
class ChatRequest(BaseModel):
    """聊天请求数据模型"""
    message: str = Field(..., min_length=1, max_length=2000)
    session_id: str = Field(..., min_length=1)
    user_id: Optional[str] = None
    user_preferences: Optional[Dict[str, Any]] = {}
    context: Optional[Dict[str, Any]] = {}

class StreamChatRequest(BaseModel):
    """流式聊天请求数据模型"""
    message: str
    session_id: str
    user_id: str
    enable_intelligence: bool = True
```

#### 1.2.2 内部数据类型
```python
# app/services/ai_assistant/langgraph/state_definitions.py
class UnifiedFitnessState(TypedDict):
    """统一状态数据模型"""
    # 基础信息
    conversation_id: str
    user_id: str
    session_id: str
    timestamp: Optional[datetime]
    
    # 处理状态
    intent: str
    confidence: float
    current_node: str
    processing_system: str
    
    # 数据内容
    user_profile: Dict[str, Any]
    training_params: Dict[str, Any]
    response_content: str
    structured_data: Dict[str, Any]
    
    # 消息历史
    messages: Annotated[List[AnyMessage], add_messages]
```

#### 1.2.3 输出数据类型
```python
# app/schemas/chat.py
class ChatResponse(BaseModel):
    """聊天响应数据模型"""
    response: str
    session_id: str
    response_type: str = "text"
    metadata: Dict[str, Any] = {}
    structured_data: Optional[Dict[str, Any]] = None
    processing_time: Optional[float] = None

class StreamChatResponse(BaseModel):
    """流式响应数据模型"""
    type: str  # "token", "metadata", "complete"
    content: str
    session_id: str
    timestamp: datetime
```

## 2. 请求处理数据流

### 2.1 HTTP请求处理流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API层
    participant Validator as 数据验证器
    participant Orch as 对话协调器
    participant State as 状态管理器
    participant DB as 数据库
    
    Client->>API: POST /message (ChatRequest)
    API->>Validator: validate_request()
    Validator->>Validator: 数据格式验证
    Validator->>Validator: 业务规则验证
    Validator-->>API: 验证结果
    
    API->>Orch: process_message()
    Orch->>State: get_current_state()
    State->>DB: 查询会话状态
    DB-->>State: 状态数据
    State-->>Orch: ConversationState
    
    Orch->>Orch: 意图识别和处理
    Orch->>DB: 保存消息记录
    Orch-->>API: 处理结果
    
    API->>API: 格式化响应
    API-->>Client: ChatResponse
```

### 2.2 WebSocket流式处理流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant WS as WebSocket处理器
    participant Orch as 对话协调器
    participant LLM as LLM代理
    participant Cache as 缓存服务
    
    Client->>WS: WebSocket连接
    WS->>WS: 连接验证
    
    Client->>WS: 发送消息
    WS->>Orch: process_message_stream()
    
    Orch->>LLM: astream()
    loop 流式生成
        LLM-->>Orch: token chunk
        Orch->>Cache: 缓存token
        Orch-->>WS: 流式数据
        WS-->>Client: 实时推送
    end
    
    Orch->>Orch: 完成处理
    Orch-->>WS: 完成信号
    WS-->>Client: 处理完成
```

## 3. 数据转换和验证

### 3.1 输入数据验证

#### 3.1.1 API层验证
**位置**: `app/api/v1/endpoints/ai_chat.py`

```python
@router.post("/message")
async def send_message(
    request: ChatRequest,  # Pydantic自动验证
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """消息发送端点 - 包含完整的数据验证流程"""
    
    # 1. 基础数据验证（Pydantic自动完成）
    # 2. 业务规则验证
    if not await validate_user_permissions(current_user.id, request.session_id):
        raise HTTPException(status_code=403, detail="无权限访问此会话")
    
    # 3. 消息内容清理
    cleaned_message = clean_user_input(request.message)
    
    # 4. 调用处理服务
    response = await conversation_orchestrator.process_message(
        message=cleaned_message,
        conversation_id=request.session_id,
        user_info={
            "user_id": current_user.id,
            "preferences": request.user_preferences
        }
    )
    
    return ChatResponse(**response)
```

#### 3.1.2 数据清理和标准化
**位置**: `app/services/ai_assistant/common/data_cleaner.py`

```python
class DataCleaner:
    """数据清理器 - 确保输入数据的安全性和一致性"""
    
    @staticmethod
    def clean_user_input(message: str) -> str:
        """清理用户输入"""
        # 1. 移除多余空白字符
        message = re.sub(r'\s+', ' ', message.strip())
        
        # 2. 移除潜在的恶意字符
        message = re.sub(r'[<>"\']', '', message)
        
        # 3. 长度限制
        if len(message) > 1000:
            message = message[:1000] + "..."
        
        # 4. 编码标准化
        message = message.encode('utf-8').decode('utf-8')
        
        return message
    
    @staticmethod
    def validate_training_params(params: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理训练参数"""
        validated = {}
        
        # 身体部位验证
        if "body_part" in params:
            valid_parts = ["chest", "back", "legs", "arms", "shoulders", "core", "full_body"]
            if params["body_part"] in valid_parts:
                validated["body_part"] = params["body_part"]
        
        # 训练强度验证
        if "intensity" in params:
            try:
                intensity = float(params["intensity"])
                if 1.0 <= intensity <= 10.0:
                    validated["intensity"] = intensity
            except (ValueError, TypeError):
                pass
        
        # 训练时长验证
        if "duration" in params:
            try:
                duration = int(params["duration"])
                if 10 <= duration <= 180:  # 10分钟到3小时
                    validated["duration"] = duration
            except (ValueError, TypeError):
                pass
        
        return validated
```

### 3.2 状态数据转换

#### 3.2.1 传统状态到LangGraph状态转换
**位置**: `app/services/ai_assistant/langgraph/adapters/state_adapter.py`

```python
class StateAdapter:
    """状态适配器 - 处理不同状态格式间的转换"""
    
    @staticmethod
    def to_unified_state(
        message: str,
        conversation_id: str,
        user_info: Dict[str, Any],
        existing_state: Optional[Dict] = None
    ) -> UnifiedFitnessState:
        """转换为LangGraph统一状态"""
        
        # 基础状态构建
        unified_state = {
            "conversation_id": conversation_id,
            "user_id": user_info.get("user_id", ""),
            "session_id": conversation_id,
            "timestamp": datetime.now(),
            "messages": [HumanMessage(content=message)],
            "user_profile": user_info,
            "current_node": "start",
            "processing_system": "langgraph",
            "intent": "",
            "confidence": 0.0,
            "response_content": "",
            "structured_data": {},
            "error_count": 0,
            "processing_start_time": time.time()
        }
        
        # 合并现有状态
        if existing_state:
            unified_state.update({
                "intent": existing_state.get("intent", ""),
                "confidence": existing_state.get("confidence", 0.0),
                "training_params": existing_state.get("training_params", {}),
                "flow_state": existing_state.get("flow_state", {}),
                "conversation_history": existing_state.get("conversation_history", [])
            })
        
        return unified_state
    
    @staticmethod
    def from_unified_state(state: UnifiedFitnessState) -> Dict[str, Any]:
        """从LangGraph状态转换为响应格式"""
        
        # 计算处理时间
        processing_time = 0.0
        if state.get("processing_start_time"):
            processing_time = time.time() - state["processing_start_time"]
        
        # 构建响应数据
        response_data = {
            "response_content": state.get("response_content", ""),
            "response_type": state.get("response_type", "text"),
            "structured_data": state.get("structured_data", {}),
            "metadata": {
                "conversation_id": state.get("conversation_id", ""),
                "intent": state.get("intent", ""),
                "confidence": state.get("confidence", 0.0),
                "processing_system": state.get("processing_system", ""),
                "processing_time": processing_time,
                "current_node": state.get("current_node", ""),
                "error_count": state.get("error_count", 0)
            }
        }
        
        # 添加性能指标
        if state.get("node_execution_times"):
            response_data["metadata"]["node_execution_times"] = state["node_execution_times"]
        
        return response_data
```

#### 3.2.2 响应数据格式化
**位置**: `app/services/ai_assistant/common/response_adapter.py`

```python
class ResponseAdapter:
    """响应适配器 - 统一响应格式"""
    
    @staticmethod
    def format_response(
        content: str,
        intent: str,
        confidence: float,
        structured_data: Optional[Dict] = None,
        source_system: str = "ai_assistant",
        processing_time: Optional[float] = None
    ) -> Dict[str, Any]:
        """格式化标准响应"""
        
        response = {
            "response_content": content,
            "intent": intent,
            "confidence": confidence,
            "source_system": source_system,
            "timestamp": datetime.now().isoformat(),
            "response_type": "text"
        }
        
        # 添加结构化数据
        if structured_data:
            response["structured_data"] = structured_data
            response["response_type"] = "structured"
        
        # 添加性能指标
        if processing_time:
            response["processing_time"] = processing_time
        
        # 添加元数据
        response["metadata"] = {
            "generated_at": datetime.now().isoformat(),
            "version": "1.0",
            "source": source_system
        }
        
        return response
    
    @staticmethod
    def format_stream_chunk(
        chunk_type: str,
        content: str,
        session_id: str,
        metadata: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """格式化流式响应块"""
        
        chunk = {
            "type": chunk_type,  # "token", "metadata", "complete", "error"
            "content": content,
            "session_id": session_id,
            "timestamp": datetime.now().isoformat()
        }
        
        if metadata:
            chunk["metadata"] = metadata
        
        return chunk
```

## 4. 数据持久化流程

### 4.1 消息数据持久化

#### 4.1.1 消息保存流程
**位置**: `app/services/ai_assistant/conversation/orchestrator.py`

```python
class ConversationOrchestrator:
    async def _save_message_to_db(
        self,
        conversation_id: str,
        user_message: str,
        ai_response: str,
        metadata: Dict[str, Any]
    ) -> bool:
        """保存消息到数据库"""
        try:
            # 1. 获取或创建会话
            conversation = await self._get_or_create_conversation(conversation_id)
            
            # 2. 保存用户消息
            user_msg = await crud_message.create(
                db=self.db,
                obj_in={
                    "conversation_id": conversation.id,
                    "role": "user",
                    "content": user_message,
                    "metadata": {"timestamp": datetime.now().isoformat()}
                }
            )
            
            # 3. 保存AI响应
            ai_msg = await crud_message.create(
                db=self.db,
                obj_in={
                    "conversation_id": conversation.id,
                    "role": "assistant",
                    "content": ai_response,
                    "metadata": metadata
                }
            )
            
            # 4. 更新会话时间戳
            await crud_conversation.update(
                db=self.db,
                db_obj=conversation,
                obj_in={"updated_at": datetime.now()}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"保存消息失败: {str(e)}")
            return False
```

#### 4.1.2 状态数据持久化
**位置**: `app/services/ai_assistant/conversation/states/manager.py`

```python
class ConversationStateManager:
    async def save_conversation_state(
        self,
        conversation_id: str,
        state_data: Dict[str, Any]
    ) -> bool:
        """保存对话状态"""
        try:
            # 1. 序列化状态数据
            serialized_state = json.dumps(state_data, default=str, ensure_ascii=False)
            
            # 2. 保存到Redis缓存（短期存储）
            cache_key = f"conversation_state:{conversation_id}"
            await self.cache_service.set(cache_key, serialized_state, ttl=3600)
            
            # 3. 保存到数据库（长期存储）
            await crud_conversation_state.upsert(
                db=self.db,
                conversation_id=conversation_id,
                state_data=state_data
            )
            
            return True
            
        except Exception as e:
            logger.error(f"保存状态失败: {str(e)}")
            return False
```

### 4.2 缓存数据管理

#### 4.2.1 多层缓存策略
**位置**: `app/services/ai_assistant/intelligence/optimization/cache_manager.py`

```python
class IntelligentCacheManager:
    async def set_with_strategy(
        self,
        key: str,
        value: Any,
        strategy: str = "lru",
        ttl: Optional[int] = None
    ) -> bool:
        """使用指定策略设置缓存"""
        try:
            # 1. 数据序列化
            serialized_value = self._serialize_value(value)
            
            # 2. 根据策略选择缓存层
            if strategy == "memory":
                # 内存缓存 - 最快访问
                self._memory_cache[key] = {
                    "value": serialized_value,
                    "timestamp": time.time(),
                    "access_count": 0
                }
            
            elif strategy == "redis":
                # Redis缓存 - 持久化
                await self.redis_client.setex(
                    key, ttl or 3600, serialized_value
                )
            
            elif strategy == "hybrid":
                # 混合策略 - 双写
                self._memory_cache[key] = {
                    "value": serialized_value,
                    "timestamp": time.time(),
                    "access_count": 0
                }
                await self.redis_client.setex(
                    key, ttl or 3600, serialized_value
                )
            
            return True
            
        except Exception as e:
            logger.error(f"缓存设置失败: {str(e)}")
            return False
```
