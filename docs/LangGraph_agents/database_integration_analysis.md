# 数据库集成实现分析

## 1. 数据库集成概览

智能健身AI助手系统采用PostgreSQL作为主数据库，Redis作为缓存层，实现了完整的数据持久化和高性能缓存策略。系统支持LangGraph检查点存储、会话状态管理、用户数据存储等多种数据库操作。

### 1.1 数据库架构图

```mermaid
graph TB
    subgraph "应用层"
        A[ConversationOrchestrator]
        B[LangGraphService]
        C[StateManager]
    end
    
    subgraph "数据访问层"
        D[CRUD操作]
        E[SQLAlchemy ORM]
        F[数据库会话管理]
    end
    
    subgraph "缓存层"
        G[Redis缓存]
        H[内存缓存]
        I[智能缓存管理器]
    end
    
    subgraph "数据存储层"
        J[PostgreSQL主库]
        K[检查点存储]
        L[会话数据]
        M[用户数据]
    end
    
    A --> D
    B --> E
    C --> F
    D --> G
    E --> H
    F --> I
    G --> J
    H --> K
    I --> L
    J --> M
    
    style J fill:#e1f5fe
    style G fill:#e8f5e8
    style A fill:#fff3e0
```

### 1.2 核心数据模型

#### 1.2.1 会话相关模型
```python
# app/models/conversation.py
class Conversation(Base):
    """对话会话模型"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, unique=True, index=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    metadata = Column(JSON, default={})
    
    # 关系定义
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    user = relationship("User", back_populates="conversations")
    state_snapshots = relationship("ConversationState", back_populates="conversation")

# app/models/message.py
class Message(Base):
    """消息模型"""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)
    role = Column(String, nullable=False)  # "user", "assistant", "system"
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON, default={})
    
    # 关系定义
    conversation = relationship("Conversation", back_populates="messages")
```

#### 1.2.2 状态存储模型
```python
# app/models/conversation_state.py
class ConversationState(Base):
    """对话状态存储模型"""
    __tablename__ = "conversation_states"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)
    state_name = Column(String, nullable=False)
    state_data = Column(JSON, nullable=False)
    checkpoint_id = Column(String, nullable=True)  # LangGraph检查点ID
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系定义
    conversation = relationship("Conversation", back_populates="state_snapshots")

# app/models/langgraph_checkpoint.py
class LangGraphCheckpoint(Base):
    """LangGraph检查点存储模型"""
    __tablename__ = "langgraph_checkpoints"
    
    id = Column(String, primary_key=True)  # 检查点ID
    thread_id = Column(String, nullable=False, index=True)
    checkpoint_ns = Column(String, nullable=False)
    checkpoint_data = Column(JSON, nullable=False)
    metadata = Column(JSON, default={})
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 索引定义
    __table_args__ = (
        Index('idx_thread_checkpoint', 'thread_id', 'checkpoint_ns'),
    )
```

#### 1.2.3 用户和健身数据模型
```python
# app/models/user.py
class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    fitness_profile = Column(JSON, default={})  # 健身档案
    preferences = Column(JSON, default={})  # 用户偏好
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系定义
    conversations = relationship("Conversation", back_populates="user")
    training_plans = relationship("TrainingPlan", back_populates="user")
    workouts = relationship("Workout", back_populates="user")

# app/models/training_plan.py
class TrainingPlan(Base):
    """训练计划模型"""
    __tablename__ = "training_plans"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text)
    plan_data = Column(JSON, nullable=False)  # 计划详细数据
    difficulty_level = Column(Integer, default=1)  # 1-10难度等级
    duration_weeks = Column(Integer, default=4)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # 关系定义
    user = relationship("User", back_populates="training_plans")
    workouts = relationship("Workout", back_populates="training_plan")
```

## 2. CRUD操作实现

### 2.1 会话管理CRUD

**文件位置**: `app/crud/crud_conversation.py`

```python
class CRUDConversation(CRUDBase[Conversation, ConversationCreate, ConversationUpdate]):
    """对话会话CRUD操作"""
    
    def get_by_session_id(self, db: Session, *, session_id: str) -> Optional[Conversation]:
        """根据会话ID获取对话"""
        return db.query(Conversation).filter(Conversation.session_id == session_id).first()
    
    def get_multi_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[Conversation]:
        """获取用户的多个对话"""
        return (
            db.query(Conversation)
            .filter(Conversation.user_id == user_id)
            .order_by(Conversation.updated_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def create_with_user(
        self, db: Session, *, obj_in: ConversationCreate, user_id: int
    ) -> Conversation:
        """创建用户对话"""
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data, user_id=user_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update_last_activity(self, db: Session, *, db_obj: Conversation) -> Conversation:
        """更新最后活动时间"""
        db_obj.updated_at = datetime.utcnow()
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

# 全局实例
crud_conversation = CRUDConversation(Conversation)
```

### 2.2 消息管理CRUD

**文件位置**: `app/crud/crud_message.py`

```python
class CRUDMessage(CRUDBase[Message, MessageCreate, MessageUpdate]):
    """消息CRUD操作"""
    
    def get_by_conversation(
        self, db: Session, *, conversation_id: int, skip: int = 0, limit: int = 50
    ) -> List[Message]:
        """获取对话的消息列表"""
        return (
            db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.created_at.asc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_recent_messages(
        self, db: Session, *, conversation_id: int, limit: int = 10
    ) -> List[Message]:
        """获取最近的消息"""
        return (
            db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.created_at.desc())
            .limit(limit)
            .all()
        )
    
    def create_user_message(
        self, db: Session, *, conversation_id: int, content: str, metadata: Dict = None
    ) -> Message:
        """创建用户消息"""
        message_data = {
            "conversation_id": conversation_id,
            "role": "user",
            "content": content,
            "metadata": metadata or {}
        }
        db_obj = Message(**message_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def create_assistant_message(
        self, db: Session, *, conversation_id: int, content: str, 
        metadata: Dict = None
    ) -> Message:
        """创建助手消息"""
        message_data = {
            "conversation_id": conversation_id,
            "role": "assistant",
            "content": content,
            "metadata": metadata or {}
        }
        db_obj = Message(**message_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

# 全局实例
crud_message = CRUDMessage(Message)
```

### 2.3 状态存储CRUD

**文件位置**: `app/crud/crud_conversation_state.py`

```python
class CRUDConversationState(CRUDBase[ConversationState, ConversationStateCreate, ConversationStateUpdate]):
    """对话状态CRUD操作"""
    
    def get_latest_state(
        self, db: Session, *, conversation_id: int
    ) -> Optional[ConversationState]:
        """获取最新的对话状态"""
        return (
            db.query(ConversationState)
            .filter(ConversationState.conversation_id == conversation_id)
            .order_by(ConversationState.updated_at.desc())
            .first()
        )
    
    def upsert_state(
        self, db: Session, *, conversation_id: int, state_name: str, 
        state_data: Dict, checkpoint_id: str = None
    ) -> ConversationState:
        """插入或更新状态"""
        # 查找现有状态
        existing_state = (
            db.query(ConversationState)
            .filter(
                ConversationState.conversation_id == conversation_id,
                ConversationState.state_name == state_name
            )
            .first()
        )
        
        if existing_state:
            # 更新现有状态
            existing_state.state_data = state_data
            existing_state.checkpoint_id = checkpoint_id
            existing_state.updated_at = datetime.utcnow()
            db.add(existing_state)
            db.commit()
            db.refresh(existing_state)
            return existing_state
        else:
            # 创建新状态
            new_state = ConversationState(
                conversation_id=conversation_id,
                state_name=state_name,
                state_data=state_data,
                checkpoint_id=checkpoint_id
            )
            db.add(new_state)
            db.commit()
            db.refresh(new_state)
            return new_state
    
    def get_state_history(
        self, db: Session, *, conversation_id: int, limit: int = 20
    ) -> List[ConversationState]:
        """获取状态历史"""
        return (
            db.query(ConversationState)
            .filter(ConversationState.conversation_id == conversation_id)
            .order_by(ConversationState.created_at.desc())
            .limit(limit)
            .all()
        )

# 全局实例
crud_conversation_state = CRUDConversationState(ConversationState)
```

## 3. LangGraph检查点集成

### 3.1 PostgreSQL检查点存储器

**文件位置**: `app/services/ai_assistant/langgraph/checkpointer/postgresql_checkpointer.py`

```python
class PostgreSQLCheckpointer:
    """PostgreSQL检查点存储器"""
    
    def __init__(self, db_session_factory):
        """初始化检查点存储器"""
        self.db_session_factory = db_session_factory
    
    async def aget_checkpoint(self, thread_id: str, checkpoint_ns: str) -> Optional[Dict]:
        """异步获取检查点"""
        async with self.db_session_factory() as db:
            checkpoint = await db.execute(
                select(LangGraphCheckpoint)
                .where(
                    LangGraphCheckpoint.thread_id == thread_id,
                    LangGraphCheckpoint.checkpoint_ns == checkpoint_ns
                )
                .order_by(LangGraphCheckpoint.created_at.desc())
                .limit(1)
            )
            result = checkpoint.scalar_one_or_none()
            
            if result:
                return result.checkpoint_data
            return None
    
    async def aput_checkpoint(
        self, thread_id: str, checkpoint_ns: str, 
        checkpoint_data: Dict, metadata: Dict = None
    ) -> str:
        """异步保存检查点"""
        checkpoint_id = f"{thread_id}_{checkpoint_ns}_{int(time.time())}"
        
        async with self.db_session_factory() as db:
            checkpoint = LangGraphCheckpoint(
                id=checkpoint_id,
                thread_id=thread_id,
                checkpoint_ns=checkpoint_ns,
                checkpoint_data=checkpoint_data,
                metadata=metadata or {}
            )
            
            db.add(checkpoint)
            await db.commit()
            
        return checkpoint_id
    
    async def alist_checkpoints(
        self, thread_id: str, checkpoint_ns: str, limit: int = 10
    ) -> List[Dict]:
        """列出检查点"""
        async with self.db_session_factory() as db:
            checkpoints = await db.execute(
                select(LangGraphCheckpoint)
                .where(
                    LangGraphCheckpoint.thread_id == thread_id,
                    LangGraphCheckpoint.checkpoint_ns == checkpoint_ns
                )
                .order_by(LangGraphCheckpoint.created_at.desc())
                .limit(limit)
            )
            
            results = checkpoints.scalars().all()
            return [
                {
                    "id": cp.id,
                    "thread_id": cp.thread_id,
                    "checkpoint_ns": cp.checkpoint_ns,
                    "checkpoint_data": cp.checkpoint_data,
                    "metadata": cp.metadata,
                    "created_at": cp.created_at
                }
                for cp in results
            ]
```

### 3.2 检查点管理服务

**文件位置**: `app/services/ai_assistant/langgraph/checkpointer/checkpoint_manager.py`

```python
class CheckpointManager:
    """检查点管理服务"""
    
    def __init__(self, db_session, redis_client=None):
        """初始化检查点管理器"""
        self.db = db_session
        self.redis = redis_client
        self.postgresql_checkpointer = PostgreSQLCheckpointer(lambda: self.db)
    
    async def save_checkpoint(
        self, thread_id: str, checkpoint_ns: str, 
        state_data: Dict, metadata: Dict = None
    ) -> str:
        """保存检查点"""
        try:
            # 保存到PostgreSQL
            checkpoint_id = await self.postgresql_checkpointer.aput_checkpoint(
                thread_id=thread_id,
                checkpoint_ns=checkpoint_ns,
                checkpoint_data=state_data,
                metadata=metadata
            )
            
            # 同时缓存到Redis（如果可用）
            if self.redis:
                cache_key = f"checkpoint:{thread_id}:{checkpoint_ns}"
                await self.redis.setex(
                    cache_key, 
                    3600,  # 1小时TTL
                    json.dumps(state_data, default=str)
                )
            
            return checkpoint_id
            
        except Exception as e:
            logger.error(f"保存检查点失败: {str(e)}")
            raise
    
    async def load_checkpoint(
        self, thread_id: str, checkpoint_ns: str
    ) -> Optional[Dict]:
        """加载检查点"""
        try:
            # 首先尝试从Redis缓存加载
            if self.redis:
                cache_key = f"checkpoint:{thread_id}:{checkpoint_ns}"
                cached_data = await self.redis.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)
            
            # 从PostgreSQL加载
            checkpoint_data = await self.postgresql_checkpointer.aget_checkpoint(
                thread_id=thread_id,
                checkpoint_ns=checkpoint_ns
            )
            
            # 如果从数据库加载成功，更新Redis缓存
            if checkpoint_data and self.redis:
                cache_key = f"checkpoint:{thread_id}:{checkpoint_ns}"
                await self.redis.setex(
                    cache_key,
                    3600,
                    json.dumps(checkpoint_data, default=str)
                )
            
            return checkpoint_data
            
        except Exception as e:
            logger.error(f"加载检查点失败: {str(e)}")
            return None
    
    async def cleanup_old_checkpoints(self, days_old: int = 7) -> int:
        """清理旧检查点"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            # 删除旧的检查点
            result = await self.db.execute(
                delete(LangGraphCheckpoint)
                .where(LangGraphCheckpoint.created_at < cutoff_date)
            )
            
            await self.db.commit()
            
            deleted_count = result.rowcount
            logger.info(f"清理了 {deleted_count} 个旧检查点")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理检查点失败: {str(e)}")
            return 0
```

## 4. 缓存集成策略

### 4.1 Redis缓存集成

**文件位置**: `app/services/cache_service.py`

```python
class CacheService:
    """Redis缓存服务"""
    
    def __init__(self, redis_url: str):
        """初始化缓存服务"""
        self.redis = redis.from_url(redis_url, decode_responses=True)
        self.ttl = 3600  # 默认1小时TTL
        self._memory_cache = {}  # 内存缓存作为降级策略
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            # 首先尝试Redis
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
            
            # 降级到内存缓存
            memory_key = f"memory:{key}"
            if memory_key in self._memory_cache:
                cache_item = self._memory_cache[memory_key]
                # 检查是否过期
                if time.time() - cache_item["timestamp"] < self.ttl:
                    return cache_item["value"]
                else:
                    del self._memory_cache[memory_key]
            
            return None
            
        except Exception as e:
            logger.error(f"缓存获取失败: {str(e)}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        try:
            expire_time = ttl or self.ttl
            serialized_value = json.dumps(value, default=str, ensure_ascii=False)
            
            # 设置Redis缓存
            await self.redis.setex(key, expire_time, serialized_value)
            
            # 同时设置内存缓存
            memory_key = f"memory:{key}"
            self._memory_cache[memory_key] = {
                "value": value,
                "timestamp": time.time()
            }
            
            return True
            
        except Exception as e:
            logger.error(f"缓存设置失败: {str(e)}")
            # 仅设置内存缓存作为降级
            memory_key = f"memory:{key}"
            self._memory_cache[memory_key] = {
                "value": value,
                "timestamp": time.time()
            }
            return False
```

### 4.2 智能缓存管理

**文件位置**: `app/services/ai_assistant/intelligence/optimization/cache_manager.py`

```python
class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, redis_client, db_session):
        """初始化智能缓存管理器"""
        self.redis = redis_client
        self.db = db_session
        self._memory_cache = {}
        self._cache_stats = {}
    
    async def get_with_strategy(self, key: str, strategy: str = "lru") -> Optional[Any]:
        """使用指定策略获取缓存"""
        # 记录访问统计
        self._record_access(key)
        
        if strategy == "memory_first":
            # 内存优先策略
            value = self._get_from_memory(key)
            if value is not None:
                return value
            value = await self._get_from_redis(key)
            if value is not None:
                self._set_to_memory(key, value)
            return value
        
        elif strategy == "redis_first":
            # Redis优先策略
            value = await self._get_from_redis(key)
            if value is not None:
                return value
            return self._get_from_memory(key)
        
        elif strategy == "hybrid":
            # 混合策略 - 根据访问频率决定
            access_count = self._cache_stats.get(key, {}).get("access_count", 0)
            if access_count > 10:  # 高频访问使用内存缓存
                return await self.get_with_strategy(key, "memory_first")
            else:  # 低频访问使用Redis
                return await self.get_with_strategy(key, "redis_first")
        
        else:  # 默认LRU策略
            return await self._get_lru(key)
    
    def _record_access(self, key: str):
        """记录缓存访问统计"""
        if key not in self._cache_stats:
            self._cache_stats[key] = {
                "access_count": 0,
                "last_access": time.time(),
                "hit_count": 0,
                "miss_count": 0
            }
        
        self._cache_stats[key]["access_count"] += 1
        self._cache_stats[key]["last_access"] = time.time()
```
