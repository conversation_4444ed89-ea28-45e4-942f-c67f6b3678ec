# 错误处理系统文档

## 1. 错误处理架构概览

智能健身AI助手系统采用多层次的错误处理架构，确保系统在各种异常情况下都能提供稳定的服务。错误处理策略包括预防性检查、优雅降级、自动重试和完整的错误记录。

### 1.1 错误处理层次结构

```mermaid
graph TD
    A[用户请求] --> B[API层错误处理]
    B --> C[业务逻辑层错误处理]
    C --> D[服务层错误处理]
    D --> E[数据层错误处理]
    
    B --> F[HTTP异常处理]
    C --> G[业务规则验证]
    D --> H[LLM调用错误处理]
    E --> I[数据库连接错误]
    
    F --> J[错误响应格式化]
    G --> J
    H --> J
    I --> J
    
    J --> K[错误日志记录]
    K --> L[监控告警]
    L --> M[自动恢复机制]
    
    style A fill:#e1f5fe
    style M fill:#e8f5e8
    style J fill:#fff3e0
    style K fill:#fce4ec
```

### 1.2 错误分类体系

#### 1.2.1 错误类型定义
```python
# app/core/exceptions.py
class BaseAIAssistantException(Exception):
    """AI助手基础异常类"""
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
        super().__init__(self.message)

class ValidationError(BaseAIAssistantException):
    """数据验证错误"""
    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(message, "VALIDATION_ERROR", {"field": field, "value": value})

class LLMServiceError(BaseAIAssistantException):
    """LLM服务错误"""
    def __init__(self, message: str, provider: str = None, model: str = None):
        super().__init__(message, "LLM_SERVICE_ERROR", {"provider": provider, "model": model})

class DatabaseError(BaseAIAssistantException):
    """数据库操作错误"""
    def __init__(self, message: str, operation: str = None, table: str = None):
        super().__init__(message, "DATABASE_ERROR", {"operation": operation, "table": table})

class StateManagementError(BaseAIAssistantException):
    """状态管理错误"""
    def __init__(self, message: str, state_name: str = None, conversation_id: str = None):
        super().__init__(message, "STATE_MANAGEMENT_ERROR", 
                        {"state_name": state_name, "conversation_id": conversation_id})

class LangGraphExecutionError(BaseAIAssistantException):
    """LangGraph执行错误"""
    def __init__(self, message: str, node_name: str = None, graph_name: str = None):
        super().__init__(message, "LANGGRAPH_EXECUTION_ERROR", 
                        {"node_name": node_name, "graph_name": graph_name})
```

#### 1.2.2 错误严重级别
```python
# app/core/error_levels.py
from enum import Enum

class ErrorLevel(Enum):
    """错误严重级别"""
    CRITICAL = "critical"    # 系统无法继续运行
    HIGH = "high"           # 核心功能受影响
    MEDIUM = "medium"       # 部分功能受影响
    LOW = "low"            # 轻微影响
    INFO = "info"          # 信息性错误

class ErrorCategory(Enum):
    """错误分类"""
    SYSTEM = "system"           # 系统级错误
    BUSINESS = "business"       # 业务逻辑错误
    INTEGRATION = "integration" # 集成服务错误
    USER_INPUT = "user_input"   # 用户输入错误
    PERFORMANCE = "performance" # 性能相关错误
```

## 2. API层错误处理

### 2.1 FastAPI异常处理器

**文件位置**: `app/api/v1/error_handlers.py`

```python
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import logging

logger = logging.getLogger(__name__)

def setup_error_handlers(app: FastAPI):
    """设置全局错误处理器"""
    
    @app.exception_handler(BaseAIAssistantException)
    async def ai_assistant_exception_handler(request: Request, exc: BaseAIAssistantException):
        """AI助手异常处理器"""
        logger.error(f"AI助手异常: {exc.message}", extra={
            "error_code": exc.error_code,
            "details": exc.details,
            "path": request.url.path,
            "method": request.method
        })
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": exc.error_code,
                    "message": exc.message,
                    "details": exc.details,
                    "timestamp": datetime.now().isoformat(),
                    "path": request.url.path
                }
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """请求验证异常处理器"""
        logger.warning(f"请求验证失败: {exc.errors()}", extra={
            "path": request.url.path,
            "method": request.method,
            "errors": exc.errors()
        })
        
        return JSONResponse(
            status_code=422,
            content={
                "error": {
                    "code": "VALIDATION_ERROR",
                    "message": "请求数据验证失败",
                    "details": exc.errors(),
                    "timestamp": datetime.now().isoformat()
                }
            }
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理器"""
        logger.warning(f"HTTP异常: {exc.detail}", extra={
            "status_code": exc.status_code,
            "path": request.url.path,
            "method": request.method
        })
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "timestamp": datetime.now().isoformat()
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理器"""
        logger.error(f"未处理的异常: {str(exc)}", exc_info=True, extra={
            "path": request.url.path,
            "method": request.method,
            "exception_type": type(exc).__name__
        })
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "服务器内部错误，请稍后重试",
                    "timestamp": datetime.now().isoformat()
                }
            }
        )
```

### 2.2 API端点错误处理

**文件位置**: `app/api/v1/endpoints/ai_chat.py`

```python
@router.post("/message")
async def send_message(
    request: ChatRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """发送消息端点 - 包含完整错误处理"""
    try:
        # 1. 输入验证
        if not request.message or len(request.message.strip()) == 0:
            raise ValidationError("消息内容不能为空", field="message", value=request.message)
        
        if len(request.message) > 2000:
            raise ValidationError("消息长度不能超过2000字符", field="message", value=len(request.message))
        
        # 2. 权限检查
        if not await check_conversation_permission(current_user.id, request.session_id, db):
            raise HTTPException(status_code=403, detail="无权限访问此会话")
        
        # 3. 处理消息
        response = await conversation_orchestrator.process_message(
            message=request.message,
            conversation_id=request.session_id,
            user_info={"user_id": current_user.id}
        )
        
        # 4. 响应验证
        if not response or "response_content" not in response:
            raise LLMServiceError("AI服务返回无效响应")
        
        return ChatResponse(
            response=response["response_content"],
            session_id=request.session_id,
            metadata=response.get("metadata", {}),
            processing_time=response.get("processing_time")
        )
        
    except ValidationError as e:
        logger.warning(f"输入验证失败: {e.message}")
        raise HTTPException(status_code=400, detail=e.message)
    
    except LLMServiceError as e:
        logger.error(f"LLM服务错误: {e.message}")
        # 返回降级响应
        return ChatResponse(
            response="抱歉，AI服务暂时不可用，请稍后重试。",
            session_id=request.session_id,
            metadata={"error": "llm_service_unavailable", "fallback": True}
        )
    
    except Exception as e:
        logger.error(f"消息处理失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="消息处理失败，请稍后重试")
```

## 3. 服务层错误处理

### 3.1 对话协调器错误处理

**文件位置**: `app/services/ai_assistant/conversation/orchestrator.py`

```python
class ConversationOrchestrator:
    async def process_message(self, message: str, conversation_id: str, user_info: Dict = None) -> Dict[str, Any]:
        """消息处理 - 包含完整错误处理和重试机制"""
        max_retries = 3
        retry_count = 0
        last_error = None
        
        while retry_count < max_retries:
            try:
                # 记录处理开始
                start_time = time.time()
                logger.info(f"开始处理消息，会话ID: {conversation_id}, 重试次数: {retry_count}")
                
                # 1. 获取当前状态
                try:
                    current_state = await self.state_manager.get_current_state(conversation_id)
                except Exception as e:
                    logger.error(f"获取状态失败: {str(e)}")
                    # 使用默认状态
                    current_state = await self.state_manager.create_default_state(conversation_id)
                
                # 2. 意图识别
                try:
                    intent_result = await self._recognize_intent(message, conversation_id)
                except Exception as e:
                    logger.warning(f"意图识别失败，使用默认意图: {str(e)}")
                    intent_result = {"intent": "general_chat", "confidence": 0.5, "parameters": {}}
                
                # 3. 智能模块处理（可选）
                if self.intelligence_enabled:
                    try:
                        await self._process_with_intelligence_modules(
                            message, conversation_id, user_info, 
                            intent_result["intent"], intent_result["confidence"], start_time
                        )
                    except Exception as e:
                        logger.warning(f"智能模块处理失败，继续基础处理: {str(e)}")
                
                # 4. 核心消息处理
                response = await self._process_core_message(
                    current_state, message, intent_result, user_info
                )
                
                # 5. 保存消息到数据库
                try:
                    await self._save_message_to_db(
                        conversation_id, message, response["response_content"], 
                        response.get("metadata", {})
                    )
                except Exception as e:
                    logger.error(f"保存消息失败: {str(e)}")
                    # 不影响响应返回
                
                # 6. 记录成功处理
                processing_time = time.time() - start_time
                response["processing_time"] = processing_time
                logger.info(f"消息处理成功，耗时: {processing_time:.2f}秒")
                
                return response
                
            except LLMServiceError as e:
                last_error = e
                retry_count += 1
                logger.warning(f"LLM服务错误，重试 {retry_count}/{max_retries}: {str(e)}")
                
                if retry_count < max_retries:
                    # 指数退避重试
                    await asyncio.sleep(2 ** retry_count)
                    continue
                else:
                    # 最大重试次数后返回降级响应
                    return self._create_fallback_response(conversation_id, str(e))
            
            except StateManagementError as e:
                last_error = e
                logger.error(f"状态管理错误: {str(e)}")
                # 状态错误不重试，直接返回错误响应
                return self._create_error_response(conversation_id, str(e))
            
            except Exception as e:
                last_error = e
                retry_count += 1
                logger.error(f"未知错误，重试 {retry_count}/{max_retries}: {str(e)}", exc_info=True)
                
                if retry_count < max_retries:
                    await asyncio.sleep(1)
                    continue
                else:
                    return self._create_error_response(conversation_id, str(e))
        
        # 所有重试都失败
        logger.error(f"消息处理最终失败: {str(last_error)}")
        return self._create_error_response(conversation_id, str(last_error))
    
    def _create_fallback_response(self, conversation_id: str, error_message: str) -> Dict[str, Any]:
        """创建降级响应"""
        return {
            "response_content": "抱歉，AI服务暂时不可用。您可以稍后重试，或者联系客服获得帮助。",
            "intent": "system_fallback",
            "confidence": 0.0,
            "metadata": {
                "fallback": True,
                "error": error_message,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat()
            }
        }
    
    def _create_error_response(self, conversation_id: str, error_message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "response_content": "抱歉，处理您的请求时出现了问题。请稍后重试。",
            "intent": "system_error",
            "confidence": 0.0,
            "metadata": {
                "error": True,
                "error_message": error_message,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat()
            }
        }
```

### 3.2 LLM服务错误处理

**文件位置**: `app/services/ai_assistant/llm/proxy.py`

```python
class DefaultLLMProxy(LLMProxy):
    async def astream(self, messages: List[Dict], **kwargs) -> AsyncGenerator[str, None]:
        """流式生成 - 包含错误处理和重试"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 验证输入
                if not messages:
                    raise LLMServiceError("消息列表不能为空")
                
                # 调用具体的LLM提供商
                async for chunk in self._call_llm_stream(messages, **kwargs):
                    yield chunk
                
                # 成功完成，退出重试循环
                break
                
            except asyncio.TimeoutError:
                retry_count += 1
                logger.warning(f"LLM调用超时，重试 {retry_count}/{max_retries}")
                
                if retry_count < max_retries:
                    await asyncio.sleep(2 ** retry_count)
                    continue
                else:
                    raise LLMServiceError("LLM服务超时，请稍后重试")
            
            except aiohttp.ClientError as e:
                retry_count += 1
                logger.warning(f"LLM网络错误，重试 {retry_count}/{max_retries}: {str(e)}")
                
                if retry_count < max_retries:
                    await asyncio.sleep(1)
                    continue
                else:
                    raise LLMServiceError(f"LLM网络连接失败: {str(e)}")
            
            except Exception as e:
                logger.error(f"LLM调用失败: {str(e)}", exc_info=True)
                raise LLMServiceError(f"LLM服务错误: {str(e)}")
    
    async def _call_llm_stream(self, messages: List[Dict], **kwargs) -> AsyncGenerator[str, None]:
        """实际的LLM调用 - 子类实现"""
        raise NotImplementedError("子类必须实现此方法")
```

### 3.3 状态管理错误处理

**文件位置**: `app/services/ai_assistant/conversation/states/manager.py`

```python
class ConversationStateManager:
    async def get_current_state(self, conversation_id: str) -> ConversationState:
        """获取当前状态 - 包含错误处理和恢复"""
        try:
            # 1. 尝试从缓存获取
            cached_state = await self._get_state_from_cache(conversation_id)
            if cached_state:
                return cached_state
            
            # 2. 从数据库获取
            db_state = await self._get_state_from_db(conversation_id)
            if db_state:
                # 恢复状态对象
                state_obj = await self._restore_state_object(db_state)
                # 更新缓存
                await self._cache_state(conversation_id, state_obj)
                return state_obj
            
            # 3. 创建默认状态
            default_state = await self.create_default_state(conversation_id)
            return default_state
            
        except Exception as e:
            logger.error(f"获取状态失败: {str(e)}", exc_info=True)
            # 创建紧急状态
            return await self._create_emergency_state(conversation_id)
    
    async def _create_emergency_state(self, conversation_id: str) -> ConversationState:
        """创建紧急状态 - 确保系统可以继续运行"""
        try:
            from app.services.ai_assistant.conversation.states.idle import IdleState
            
            emergency_context = {
                "conversation_id": conversation_id,
                "emergency_mode": True,
                "created_at": datetime.now(),
                "error_recovery": True
            }
            
            emergency_state = IdleState(emergency_context)
            logger.warning(f"创建紧急状态，会话ID: {conversation_id}")
            
            return emergency_state
            
        except Exception as e:
            logger.critical(f"创建紧急状态失败: {str(e)}")
            raise StateManagementError(f"无法创建紧急状态: {str(e)}", conversation_id=conversation_id)
```

## 4. LangGraph错误处理

### 4.1 图执行错误处理

**文件位置**: `app/services/ai_assistant/langgraph/graph/fitness_ai_graph.py`

```python
class FitnessAIGraph:
    async def execute(self, initial_state: UnifiedFitnessState, config: Optional[Dict] = None) -> UnifiedFitnessState:
        """执行图 - 包含完整错误处理"""
        try:
            if not self._initialized:
                self.compile_graph()
            
            # 验证初始状态
            if not self._validate_initial_state(initial_state):
                raise LangGraphExecutionError("初始状态验证失败", graph_name="FitnessAIGraph")
            
            # 设置执行配置
            run_config = self._prepare_run_config(initial_state, config)
            
            # 执行图
            result = await self.compiled_graph.ainvoke(initial_state, config=run_config)
            
            # 验证结果
            if not self._validate_result(result):
                logger.warning("图执行结果验证失败，使用降级结果")
                return self._create_fallback_result(initial_state)
            
            return result
            
        except Exception as e:
            logger.error(f"图执行失败: {str(e)}", exc_info=True)
            return self._handle_execution_error(initial_state, e)
    
    def _handle_execution_error(self, initial_state: UnifiedFitnessState, error: Exception) -> UnifiedFitnessState:
        """处理执行错误"""
        error_state = initial_state.copy()
        error_state.update({
            "error_count": error_state.get("error_count", 0) + 1,
            "last_error": str(error),
            "response_content": "抱歉，处理您的请求时出现了问题。",
            "current_node": "error_handler",
            "processing_system": "error_recovery"
        })
        
        return error_state
```

### 4.2 节点级错误处理

**文件位置**: `app/services/ai_assistant/langgraph/nodes/router_node.py`

```python
async def intelligent_router_node(state: UnifiedFitnessState) -> Command:
    """智能路由节点 - 包含错误处理"""
    try:
        # 记录节点开始执行
        start_time = time.time()
        logger.debug(f"智能路由节点开始执行，会话ID: {state.get('conversation_id')}")
        
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break
        
        if not user_message:
            logger.warning("未找到用户消息，使用默认路由")
            return Command(
                update={"current_node": "intelligent_router", "routing_decision": {"target_processor": "state_machine_processor"}},
                goto="state_machine_processor"
            )
        
        # 分析消息复杂度
        analysis_result = await _analyze_message_complexity(user_message, state)
        
        # 路由决策
        routing_decision = _make_routing_decision(analysis_result, state)
        
        # 记录执行时间
        execution_time = time.time() - start_time
        
        # 更新状态
        state_update = {
            "current_node": "intelligent_router",
            "analysis_result": analysis_result,
            "routing_decision": routing_decision,
            "node_execution_times": {
                **state.get("node_execution_times", {}),
                "intelligent_router": execution_time
            }
        }
        
        logger.info(f"智能路由决策: {routing_decision['target_processor']}, 耗时: {execution_time:.3f}秒")
        
        return Command(
            update=state_update,
            goto=routing_decision["target_processor"]
        )
        
    except Exception as e:
        logger.error(f"智能路由节点执行失败: {str(e)}", exc_info=True)
        
        # 错误恢复 - 使用默认路由
        error_update = {
            "current_node": "intelligent_router",
            "error_count": state.get("error_count", 0) + 1,
            "last_error": f"路由节点错误: {str(e)}",
            "routing_decision": {
                "target_processor": "state_machine_processor",
                "error_fallback": True,
                "error_message": str(e)
            }
        }
        
        return Command(
            update=error_update,
            goto="state_machine_processor"
        )
```

## 5. 错误监控和告警

### 5.1 错误监控服务

**文件位置**: `app/services/ai_assistant/intelligence/monitoring/error_monitor.py`

```python
class ErrorMonitor:
    """错误监控服务"""
    
    def __init__(self, db_session, redis_client):
        """初始化错误监控"""
        self.db = db_session
        self.redis = redis_client
        self.error_stats = {}
    
    async def record_error(self, error: Exception, context: Dict[str, Any]):
        """记录错误"""
        try:
            error_data = {
                "error_type": type(error).__name__,
                "error_message": str(error),
                "error_code": getattr(error, 'error_code', 'UNKNOWN'),
                "context": context,
                "timestamp": datetime.now().isoformat(),
                "severity": self._determine_severity(error),
                "category": self._categorize_error(error)
            }
            
            # 保存到数据库
            await self._save_error_to_db(error_data)
            
            # 更新Redis统计
            await self._update_error_stats(error_data)
            
            # 检查是否需要告警
            await self._check_alert_conditions(error_data)
            
        except Exception as e:
            logger.error(f"记录错误失败: {str(e)}")
    
    def _determine_severity(self, error: Exception) -> str:
        """确定错误严重程度"""
        if isinstance(error, (DatabaseError, StateManagementError)):
            return ErrorLevel.HIGH.value
        elif isinstance(error, LLMServiceError):
            return ErrorLevel.MEDIUM.value
        elif isinstance(error, ValidationError):
            return ErrorLevel.LOW.value
        else:
            return ErrorLevel.MEDIUM.value
    
    async def _check_alert_conditions(self, error_data: Dict):
        """检查告警条件"""
        error_type = error_data["error_type"]
        severity = error_data["severity"]
        
        # 获取最近1小时的错误统计
        recent_errors = await self._get_recent_error_count(error_type, hours=1)
        
        # 告警条件
        alert_thresholds = {
            ErrorLevel.CRITICAL.value: 1,   # 严重错误立即告警
            ErrorLevel.HIGH.value: 5,       # 高级错误5次告警
            ErrorLevel.MEDIUM.value: 20,    # 中级错误20次告警
            ErrorLevel.LOW.value: 50        # 低级错误50次告警
        }
        
        threshold = alert_thresholds.get(severity, 10)
        
        if recent_errors >= threshold:
            await self._send_alert(error_data, recent_errors)
    
    async def _send_alert(self, error_data: Dict, error_count: int):
        """发送告警"""
        alert_message = f"""
        错误告警：
        错误类型：{error_data['error_type']}
        错误消息：{error_data['error_message']}
        严重程度：{error_data['severity']}
        最近1小时发生次数：{error_count}
        时间：{error_data['timestamp']}
        """
        
        # 这里可以集成邮件、短信、钉钉等告警渠道
        logger.critical(alert_message)
```
