# API文档概述

## 📖 API简介

智能健身AI助手系统提供完整的RESTful API接口，支持所有核心功能的程序化访问。API设计遵循REST原则，使用JSON格式进行数据交换，支持标准的HTTP状态码和错误处理机制。

## 🏗️ API架构

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端应用                                │
├─────────────────────────────────────────────────────────────┤
│  Web应用  │  移动应用  │  第三方集成  │  管理工具            │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    API网关                                  │
├─────────────────────────────────────────────────────────────┤
│  路由管理  │  认证授权  │  限流控制  │  监控统计              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    API服务层                                │
├─────────────────────────────────────────────────────────────┤
│  核心API  │  管理API  │  监控API  │  工具API                │
└─────────────────────────────────────────────────────────────┘
```

### API分类

#### 1. 核心业务API
- **对话接口**: 智能对话和问答
- **训练计划**: 个性化训练方案
- **营养建议**: 饮食和营养指导
- **数据分析**: 运动数据分析

#### 2. 用户管理API
- **用户认证**: 登录、注册、令牌管理
- **用户档案**: 个人信息管理
- **偏好设置**: 个性化配置
- **历史记录**: 交互历史查询

#### 3. 系统管理API
- **系统配置**: 系统参数管理
- **监控数据**: 性能指标查询
- **日志管理**: 系统日志查询
- **健康检查**: 系统状态检查

#### 4. 扩展功能API
- **文件上传**: 多媒体文件处理
- **数据导出**: 数据导出功能
- **第三方集成**: 外部服务接口
- **开发工具**: 调试和测试接口

## 🔐 认证授权

### 认证方式

#### JWT令牌认证
```http
Authorization: Bearer <JWT_TOKEN>
```

#### API密钥认证
```http
X-API-Key: <API_KEY>
```

### 权限级别

| 权限级别 | 说明 | 适用场景 |
|---------|------|----------|
| public | 公开访问 | 健康检查、文档 |
| user | 用户权限 | 个人数据访问 |
| admin | 管理员权限 | 系统管理 |
| system | 系统权限 | 内部服务调用 |

## 📋 API规范

### 请求格式

#### 基础URL
```
https://api.sciencefit.com/v1
```

#### 请求头
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer <token>
X-Request-ID: <unique_id>
```

#### 请求体
```json
{
  "data": {
    "field1": "value1",
    "field2": "value2"
  },
  "metadata": {
    "timestamp": "2025-01-25T10:00:00Z",
    "version": "1.0"
  }
}
```

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {
    "result": "response_data"
  },
  "metadata": {
    "timestamp": "2025-01-25T10:00:00Z",
    "request_id": "req_123456",
    "processing_time_ms": 150
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": {
      "field": "user_id",
      "reason": "不能为空"
    }
  },
  "metadata": {
    "timestamp": "2025-01-25T10:00:00Z",
    "request_id": "req_123456"
  }
}
```

## 🔄 HTTP状态码

### 成功状态码
- **200 OK**: 请求成功
- **201 Created**: 资源创建成功
- **202 Accepted**: 请求已接受，异步处理
- **204 No Content**: 请求成功，无返回内容

### 客户端错误
- **400 Bad Request**: 请求参数错误
- **401 Unauthorized**: 未授权访问
- **403 Forbidden**: 权限不足
- **404 Not Found**: 资源不存在
- **409 Conflict**: 资源冲突
- **422 Unprocessable Entity**: 请求格式正确但语义错误
- **429 Too Many Requests**: 请求频率超限

### 服务器错误
- **500 Internal Server Error**: 服务器内部错误
- **502 Bad Gateway**: 网关错误
- **503 Service Unavailable**: 服务不可用
- **504 Gateway Timeout**: 网关超时

## 📊 API端点概览

### 核心对话API

#### 发送消息
```http
POST /v1/chat/messages
```

#### 获取对话历史
```http
GET /v1/chat/conversations/{conversation_id}/messages
```

#### 创建新对话
```http
POST /v1/chat/conversations
```

### 训练计划API

#### 生成训练计划
```http
POST /v1/training/plans
```

#### 获取训练计划
```http
GET /v1/training/plans/{plan_id}
```

#### 更新训练进度
```http
PUT /v1/training/plans/{plan_id}/progress
```

### 用户管理API

#### 用户注册
```http
POST /v1/auth/register
```

#### 用户登录
```http
POST /v1/auth/login
```

#### 获取用户信息
```http
GET /v1/users/{user_id}
```

### 系统监控API

#### 健康检查
```http
GET /v1/health
```

#### 系统状态
```http
GET /v1/system/status
```

#### 性能指标
```http
GET /v1/system/metrics
```

## 🔧 请求限制

### 频率限制

| 端点类型 | 限制 | 时间窗口 |
|---------|------|----------|
| 对话API | 100次 | 1分钟 |
| 训练计划 | 50次 | 1分钟 |
| 用户管理 | 20次 | 1分钟 |
| 系统监控 | 200次 | 1分钟 |

### 数据限制

| 限制类型 | 限制值 |
|---------|--------|
| 请求体大小 | 10MB |
| 文件上传 | 50MB |
| 响应超时 | 30秒 |
| 并发连接 | 100个 |

## 📝 使用示例

### Python示例

```python
import requests
import json

# 配置
BASE_URL = "https://api.sciencefit.com/v1"
API_KEY = "your_api_key_here"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}"
}

# 发送消息
def send_message(message, user_id):
    url = f"{BASE_URL}/chat/messages"
    data = {
        "data": {
            "message": message,
            "user_id": user_id
        }
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# 使用示例
result = send_message("我想制定一个减脂计划", "user123")
print(json.dumps(result, indent=2, ensure_ascii=False))
```

### JavaScript示例

```javascript
const BASE_URL = "https://api.sciencefit.com/v1";
const API_KEY = "your_api_key_here";

const headers = {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${API_KEY}`
};

// 发送消息
async function sendMessage(message, userId) {
    const url = `${BASE_URL}/chat/messages`;
    const data = {
        data: {
            message: message,
            user_id: userId
        }
    };
    
    const response = await fetch(url, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(data)
    });
    
    return await response.json();
}

// 使用示例
sendMessage("我想制定一个减脂计划", "user123")
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

### cURL示例

```bash
# 发送消息
curl -X POST "https://api.sciencefit.com/v1/chat/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_api_key_here" \
  -d '{
    "data": {
      "message": "我想制定一个减脂计划",
      "user_id": "user123"
    }
  }'

# 获取健康状态
curl -X GET "https://api.sciencefit.com/v1/health" \
  -H "Authorization: Bearer your_api_key_here"
```

## 🔍 调试工具

### API测试工具
- **Postman集合**: 提供完整的API测试集合
- **Swagger UI**: 交互式API文档和测试界面
- **API调试器**: 内置的API调试工具

### 开发者工具
- **请求日志**: 详细的请求响应日志
- **性能分析**: API性能分析工具
- **错误追踪**: 错误信息追踪和分析

## 📚 相关文档

- [认证授权详解](authentication.md)
- [核心接口文档](core_endpoints.md)
- [管理接口文档](admin_endpoints.md)
- [错误码参考](error_codes.md)
- [SDK使用指南](../guides/sdk_usage.md)

---

**文档版本**: v1.0  
**最后更新**: 2025年1月25日  
**维护团队**: ScienceFit AI团队
