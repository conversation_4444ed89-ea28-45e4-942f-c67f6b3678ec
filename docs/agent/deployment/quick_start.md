# 快速部署指南

## 🚀 5分钟快速部署

本指南将帮助您在5分钟内快速部署智能健身AI助手系统，适用于开发环境和快速体验。

## 📋 前置要求

### 系统要求
- **操作系统**: Linux (Ubuntu 20.04+) / macOS / Windows 10+
- **内存**: 最少4GB，推荐8GB+
- **磁盘空间**: 最少10GB可用空间
- **网络**: 稳定的互联网连接

### 软件依赖
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.0+

## 🛠️ 安装步骤

### 步骤1：克隆项目

```bash
# 克隆项目代码
git clone https://github.com/sciencefit/ai-assistant.git
cd ai-assistant

# 切换到稳定版本
git checkout v1.0
```

### 步骤2：环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件（可选，使用默认配置即可快速启动）
nano .env
```

**基础配置示例**:
```bash
# 数据库配置
DATABASE_URL=**************************************/fitness_ai
REDIS_URL=redis://redis:6379/0

# AI模型配置
OPENAI_API_KEY=your_openai_key_here
DASHSCOPE_API_KEY=your_dashscope_key_here

# 系统配置
DEBUG=true
LOG_LEVEL=INFO
```

### 步骤3：一键启动

```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 查看启动状态
docker-compose ps
```

**预期输出**:
```
NAME                COMMAND                  SERVICE             STATUS              PORTS
ai-assistant-api    "uvicorn app.main:ap…"   api                 running             0.0.0.0:8000->8000/tcp
ai-assistant-db     "docker-entrypoint.s…"   db                  running             5432/tcp
ai-assistant-redis  "docker-entrypoint.s…"   redis               running             6379/tcp
ai-assistant-web    "python -m streamlit…"   web                 running             0.0.0.0:8501->8501/tcp
```

### 步骤4：验证部署

```bash
# 检查API健康状态
curl http://localhost:8000/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2025-01-25T10:00:00Z",
  "version": "1.0.0"
}
```

## 🌐 访问系统

### Web界面
- **用户界面**: http://localhost:8501
- **API文档**: http://localhost:8000/docs
- **管理后台**: http://localhost:8000/admin

### API端点
- **基础URL**: http://localhost:8000/v1
- **健康检查**: http://localhost:8000/health
- **系统状态**: http://localhost:8000/v1/system/status

## 🧪 快速测试

### 1. Web界面测试

1. 打开浏览器访问 http://localhost:8501
2. 在聊天框中输入："我想制定一个减脂计划"
3. 查看AI助手的响应

### 2. API测试

```bash
# 发送测试消息
curl -X POST "http://localhost:8000/v1/chat/messages" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "message": "我想制定一个减脂计划",
      "user_id": "test_user"
    }
  }'
```

### 3. 系统监控测试

```bash
# 查看系统状态
curl http://localhost:8000/v1/system/status

# 查看性能指标
curl http://localhost:8000/v1/system/metrics
```

## 🔧 常用命令

### 服务管理

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f api
```

### 数据管理

```bash
# 初始化数据库
docker-compose exec api python -m alembic upgrade head

# 创建测试数据
docker-compose exec api python scripts/create_test_data.py

# 备份数据库
docker-compose exec db pg_dump -U postgres fitness_ai > backup.sql

# 恢复数据库
docker-compose exec -T db psql -U postgres fitness_ai < backup.sql
```

### 开发调试

```bash
# 进入API容器
docker-compose exec api bash

# 查看实时日志
docker-compose logs -f --tail=100 api

# 重新构建镜像
docker-compose build --no-cache

# 清理未使用的资源
docker system prune -f
```

## 🔍 故障排除

### 常见问题

#### 1. 端口冲突
**问题**: 端口8000或8501已被占用
**解决方案**:
```bash
# 查看端口占用
lsof -i :8000
lsof -i :8501

# 修改docker-compose.yml中的端口映射
ports:
  - "8080:8000"  # 将8000改为8080
```

#### 2. 数据库连接失败
**问题**: 无法连接到PostgreSQL数据库
**解决方案**:
```bash
# 检查数据库容器状态
docker-compose logs db

# 重启数据库服务
docker-compose restart db

# 检查数据库连接
docker-compose exec db psql -U postgres -c "SELECT 1"
```

#### 3. AI模型调用失败
**问题**: AI模型API调用失败
**解决方案**:
```bash
# 检查API密钥配置
docker-compose exec api env | grep API_KEY

# 测试API连接
docker-compose exec api python -c "
import os
from openai import OpenAI
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
print('API连接正常')
"
```

#### 4. 内存不足
**问题**: 系统内存不足导致服务异常
**解决方案**:
```bash
# 查看内存使用
docker stats

# 限制容器内存使用
# 在docker-compose.yml中添加：
deploy:
  resources:
    limits:
      memory: 2G
```

### 日志分析

```bash
# 查看错误日志
docker-compose logs api | grep ERROR

# 查看访问日志
docker-compose logs api | grep "POST\|GET"

# 导出日志到文件
docker-compose logs api > api.log 2>&1
```

## 📊 性能优化

### 快速优化建议

1. **增加内存分配**
```yaml
# docker-compose.yml
services:
  api:
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
```

2. **启用缓存**
```bash
# 在.env文件中启用Redis缓存
ENABLE_CACHE=true
CACHE_TTL=3600
```

3. **数据库优化**
```bash
# 增加数据库连接池
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
```

## 🔄 更新升级

### 版本更新

```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose down
docker-compose build
docker-compose up -d

# 运行数据库迁移
docker-compose exec api python -m alembic upgrade head
```

### 配置更新

```bash
# 更新环境变量后重启
docker-compose restart api

# 重新加载配置
docker-compose exec api python -c "
from app.core.config import settings
print('配置加载成功')
"
```

## 📚 下一步

部署成功后，您可以：

1. **阅读详细文档**: [完整部署指南](installation.md)
2. **配置生产环境**: [生产环境优化](production_optimization.md)
3. **集成开发**: [API使用指南](../api/overview.md)
4. **监控运维**: [系统监控](../maintenance/system_monitoring.md)

## 🆘 获取帮助

- **文档中心**: [docs.sciencefit.com](https://docs.sciencefit.com)
- **问题反馈**: [GitHub Issues](https://github.com/sciencefit/ai-assistant/issues)
- **技术支持**: <EMAIL>
- **开发者社区**: [community.sciencefit.com](https://community.sciencefit.com)

---

**文档版本**: v1.0  
**最后更新**: 2025年1月25日  
**维护团队**: ScienceFit AI团队
