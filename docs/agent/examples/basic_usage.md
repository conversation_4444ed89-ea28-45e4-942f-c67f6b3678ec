# 基础使用示例

## 📖 概述

本文档提供智能健身AI助手系统的基础使用示例，包括API调用、Web界面使用和常见功能演示。这些示例将帮助您快速上手系统的核心功能。

## 🚀 快速开始

### 环境准备

确保系统已正确部署并运行：

```bash
# 检查系统状态
curl http://localhost:8000/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2025-01-25T10:00:00Z",
  "version": "1.0.0"
}
```

## 💬 基础对话功能

### 示例1：简单问答

#### API调用方式

```python
import requests
import json

# 配置
BASE_URL = "http://localhost:8000/v1"
headers = {"Content-Type": "application/json"}

def send_message(message, user_id="demo_user"):
    """发送消息到AI助手"""
    url = f"{BASE_URL}/chat/messages"
    data = {
        "data": {
            "message": message,
            "user_id": user_id
        }
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# 示例对话
messages = [
    "你好，我是新手，想开始健身",
    "我想减脂，应该怎么做？",
    "推荐一些适合初学者的运动",
    "制定一个一周的训练计划"
]

for message in messages:
    print(f"用户: {message}")
    result = send_message(message)
    if result["success"]:
        print(f"AI助手: {result['data']['response']}")
    else:
        print(f"错误: {result['error']['message']}")
    print("-" * 50)
```

#### 预期输出

```
用户: 你好，我是新手，想开始健身
AI助手: 你好！欢迎开始健身之旅！作为新手，建议您从基础动作开始，循序渐进。我可以为您制定个性化的训练计划。请告诉我您的健身目标是什么？比如减脂、增肌还是提高体能？

用户: 我想减脂，应该怎么做？
AI助手: 减脂需要结合有氧运动和力量训练，同时控制饮食。建议您：
1. 有氧运动：每周3-4次，每次30-45分钟
2. 力量训练：每周2-3次，增加肌肉量提高代谢
3. 饮食控制：创造热量缺口，多吃蛋白质和蔬菜
4. 充足睡眠：每天7-8小时

您目前的运动基础如何？我可以为您制定更具体的计划。
```

### 示例2：多轮对话

```python
def conversation_demo():
    """多轮对话演示"""
    user_id = "demo_user_001"
    conversation_id = None
    
    # 创建新对话
    create_url = f"{BASE_URL}/chat/conversations"
    create_data = {"data": {"user_id": user_id}}
    response = requests.post(create_url, headers=headers, json=create_data)
    
    if response.json()["success"]:
        conversation_id = response.json()["data"]["conversation_id"]
        print(f"创建对话: {conversation_id}")
    
    # 对话流程
    conversation_flow = [
        "我想制定一个减脂计划",
        "我是25岁男性，身高175cm，体重80kg",
        "我每周可以运动4次，每次1小时",
        "我有哑铃和跑步机",
        "请给我详细的训练计划"
    ]
    
    for message in conversation_flow:
        data = {
            "data": {
                "message": message,
                "user_id": user_id,
                "conversation_id": conversation_id
            }
        }
        
        response = requests.post(f"{BASE_URL}/chat/messages", 
                               headers=headers, json=data)
        result = response.json()
        
        print(f"用户: {message}")
        if result["success"]:
            print(f"AI助手: {result['data']['response']}")
        print("-" * 50)

# 运行对话演示
conversation_demo()
```

## 🏋️ 训练计划功能

### 示例3：生成个性化训练计划

```python
def generate_training_plan():
    """生成训练计划示例"""
    url = f"{BASE_URL}/training/plans"
    
    # 用户信息和目标
    plan_data = {
        "data": {
            "user_profile": {
                "age": 25,
                "gender": "male",
                "height": 175,
                "weight": 80,
                "fitness_level": "beginner",
                "available_time": 60,
                "available_days": 4
            },
            "goals": {
                "primary_goal": "weight_loss",
                "target_weight": 75,
                "timeline_weeks": 12
            },
            "equipment": ["dumbbells", "treadmill", "yoga_mat"],
            "preferences": {
                "exercise_types": ["cardio", "strength"],
                "intensity": "moderate"
            }
        }
    }
    
    response = requests.post(url, headers=headers, json=plan_data)
    result = response.json()
    
    if result["success"]:
        plan = result["data"]["training_plan"]
        print("🏋️ 个性化训练计划")
        print(f"计划名称: {plan['plan_name']}")
        print(f"训练周期: {plan['duration_weeks']}周")
        print(f"每周训练: {plan['sessions_per_week']}次")
        
        print("\n📅 周训练安排:")
        for day, workout in plan["weekly_schedule"].items():
            if workout:
                print(f"{day}: {workout['workout_type']} - {workout['duration']}分钟")
        
        print("\n💪 训练动作:")
        for exercise in plan["exercises"][:5]:  # 显示前5个动作
            print(f"- {exercise['name']}: {exercise['sets']}组 x {exercise['reps']}次")
    
    return result

# 生成训练计划
plan_result = generate_training_plan()
```

### 示例4：训练进度追踪

```python
def track_workout_progress():
    """训练进度追踪示例"""
    # 假设已有训练计划ID
    plan_id = "plan_123456"
    
    # 记录训练完成情况
    progress_data = {
        "data": {
            "plan_id": plan_id,
            "workout_date": "2025-01-25",
            "exercises_completed": [
                {
                    "exercise_name": "深蹲",
                    "sets_completed": 3,
                    "reps_completed": [12, 10, 8],
                    "weight_used": 20,
                    "rest_time": 60,
                    "difficulty_rating": 7
                },
                {
                    "exercise_name": "俯卧撑",
                    "sets_completed": 3,
                    "reps_completed": [15, 12, 10],
                    "weight_used": 0,
                    "rest_time": 45,
                    "difficulty_rating": 6
                }
            ],
            "workout_duration": 45,
            "overall_rating": 8,
            "notes": "感觉不错，下次可以增加重量"
        }
    }
    
    url = f"{BASE_URL}/training/plans/{plan_id}/progress"
    response = requests.put(url, headers=headers, json=progress_data)
    result = response.json()
    
    if result["success"]:
        print("✅ 训练进度记录成功")
        progress = result["data"]["progress_summary"]
        print(f"完成率: {progress['completion_rate']:.1%}")
        print(f"总训练次数: {progress['total_workouts']}")
        print(f"本周训练: {progress['this_week_workouts']}")
    
    return result

# 记录训练进度
progress_result = track_workout_progress()
```

## 🥗 营养建议功能

### 示例5：获取营养建议

```python
def get_nutrition_advice():
    """获取营养建议示例"""
    url = f"{BASE_URL}/nutrition/advice"
    
    nutrition_request = {
        "data": {
            "user_profile": {
                "age": 25,
                "gender": "male",
                "weight": 80,
                "height": 175,
                "activity_level": "moderate",
                "goal": "weight_loss"
            },
            "dietary_preferences": {
                "diet_type": "balanced",
                "allergies": [],
                "dislikes": ["broccoli"],
                "meal_frequency": 3
            },
            "current_date": "2025-01-25"
        }
    }
    
    response = requests.post(url, headers=headers, json=nutrition_request)
    result = response.json()
    
    if result["success"]:
        advice = result["data"]["nutrition_plan"]
        print("🥗 营养建议")
        print(f"每日热量目标: {advice['daily_calories']}卡")
        print(f"蛋白质: {advice['protein_g']}g")
        print(f"碳水化合物: {advice['carbs_g']}g")
        print(f"脂肪: {advice['fat_g']}g")
        
        print("\n🍽️ 今日餐单建议:")
        for meal_name, meal_info in advice["meal_plan"].items():
            print(f"{meal_name}:")
            for food in meal_info["foods"]:
                print(f"  - {food['name']}: {food['amount']}")
            print(f"  热量: {meal_info['calories']}卡\n")
    
    return result

# 获取营养建议
nutrition_result = get_nutrition_advice()
```

## 📊 数据分析功能

### 示例6：运动数据分析

```python
def analyze_workout_data():
    """运动数据分析示例"""
    user_id = "demo_user_001"
    url = f"{BASE_URL}/analytics/workout-summary"
    
    analysis_request = {
        "data": {
            "user_id": user_id,
            "date_range": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-25"
            },
            "analysis_type": "comprehensive"
        }
    }
    
    response = requests.post(url, headers=headers, json=analysis_request)
    result = response.json()
    
    if result["success"]:
        analysis = result["data"]["analysis"]
        print("📊 运动数据分析报告")
        print(f"分析周期: {analysis['period']}")
        print(f"总训练次数: {analysis['total_workouts']}")
        print(f"总训练时长: {analysis['total_duration']}分钟")
        print(f"平均训练强度: {analysis['avg_intensity']}/10")
        
        print("\n📈 进步趋势:")
        for metric, trend in analysis["progress_trends"].items():
            print(f"{metric}: {trend['change']:+.1%} ({trend['direction']})")
        
        print("\n🎯 建议:")
        for suggestion in analysis["recommendations"]:
            print(f"- {suggestion}")
    
    return result

# 分析运动数据
analysis_result = analyze_workout_data()
```

## 🌐 Web界面使用

### 访问Web界面

1. **打开浏览器**，访问 http://localhost:8501
2. **开始对话**：在聊天框中输入消息
3. **查看历史**：在侧边栏查看对话历史
4. **个人设置**：配置个人信息和偏好

### Web界面功能演示

```python
# 模拟Web界面交互
def web_interface_demo():
    """Web界面功能演示"""
    print("🌐 Web界面使用指南")
    print("1. 访问 http://localhost:8501")
    print("2. 在聊天框输入: '我想开始健身'")
    print("3. 跟随AI助手的引导完成信息收集")
    print("4. 获取个性化的健身建议")
    print("5. 在侧边栏查看训练计划和进度")

web_interface_demo()
```

## 🔧 高级功能示例

### 示例7：文件上传功能

```python
def upload_workout_image():
    """上传训练图片示例"""
    url = f"{BASE_URL}/upload/workout-image"
    
    # 模拟图片文件
    files = {
        'file': ('workout.jpg', open('workout_photo.jpg', 'rb'), 'image/jpeg')
    }
    
    data = {
        'user_id': 'demo_user_001',
        'workout_type': 'strength_training',
        'description': '今天的深蹲训练'
    }
    
    response = requests.post(url, files=files, data=data)
    result = response.json()
    
    if result["success"]:
        print("📸 图片上传成功")
        analysis = result["data"]["image_analysis"]
        print(f"识别动作: {analysis['exercise_detected']}")
        print(f"姿势评分: {analysis['form_score']}/10")
        print(f"改进建议: {analysis['suggestions']}")
    
    return result
```

### 示例8：批量操作

```python
def batch_operations_demo():
    """批量操作示例"""
    # 批量创建用户
    users_data = [
        {"name": "张三", "age": 25, "goal": "weight_loss"},
        {"name": "李四", "age": 30, "goal": "muscle_gain"},
        {"name": "王五", "age": 28, "goal": "endurance"}
    ]
    
    created_users = []
    for user_data in users_data:
        url = f"{BASE_URL}/users"
        response = requests.post(url, headers=headers, json={"data": user_data})
        if response.json()["success"]:
            created_users.append(response.json()["data"]["user_id"])
    
    print(f"✅ 批量创建用户成功: {len(created_users)}个")
    
    # 批量生成训练计划
    for user_id in created_users:
        plan_data = {
            "data": {
                "user_id": user_id,
                "plan_type": "beginner",
                "duration_weeks": 8
            }
        }
        
        response = requests.post(f"{BASE_URL}/training/plans", 
                               headers=headers, json=plan_data)
        if response.json()["success"]:
            print(f"为用户 {user_id} 生成训练计划成功")

# 运行批量操作演示
batch_operations_demo()
```

## 🔍 错误处理示例

### 示例9：错误处理和重试

```python
import time
from typing import Optional

def robust_api_call(url: str, data: dict, max_retries: int = 3) -> Optional[dict]:
    """带重试机制的API调用"""
    for attempt in range(max_retries):
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            result = response.json()
            
            if result["success"]:
                return result
            else:
                print(f"API错误 (尝试 {attempt + 1}): {result['error']['message']}")
                
        except requests.exceptions.Timeout:
            print(f"请求超时 (尝试 {attempt + 1})")
        except requests.exceptions.ConnectionError:
            print(f"连接错误 (尝试 {attempt + 1})")
        except Exception as e:
            print(f"未知错误 (尝试 {attempt + 1}): {str(e)}")
        
        if attempt < max_retries - 1:
            time.sleep(2 ** attempt)  # 指数退避
    
    print("所有重试都失败了")
    return None

# 使用示例
result = robust_api_call(
    f"{BASE_URL}/chat/messages",
    {"data": {"message": "测试消息", "user_id": "test_user"}}
)
```

## 📝 完整示例脚本

```python
#!/usr/bin/env python3
"""
智能健身AI助手系统 - 基础使用示例
完整的功能演示脚本
"""

import requests
import json
import time
from datetime import datetime

class FitnessAIClient:
    """健身AI助手客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000/v1"):
        self.base_url = base_url
        self.headers = {"Content-Type": "application/json"}
        self.session = requests.Session()
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url.replace('/v1', '')}/health")
            return response.json().get("status") == "healthy"
        except:
            return False
    
    def send_message(self, message: str, user_id: str) -> dict:
        """发送消息"""
        url = f"{self.base_url}/chat/messages"
        data = {"data": {"message": message, "user_id": user_id}}
        response = self.session.post(url, headers=self.headers, json=data)
        return response.json()
    
    def create_training_plan(self, user_profile: dict, goals: dict) -> dict:
        """创建训练计划"""
        url = f"{self.base_url}/training/plans"
        data = {"data": {"user_profile": user_profile, "goals": goals}}
        response = self.session.post(url, headers=self.headers, json=data)
        return response.json()

def main():
    """主函数 - 完整功能演示"""
    client = FitnessAIClient()
    
    # 1. 健康检查
    print("🔍 系统健康检查...")
    if not client.health_check():
        print("❌ 系统不可用，请检查服务状态")
        return
    print("✅ 系统运行正常")
    
    # 2. 基础对话
    print("\n💬 基础对话演示...")
    user_id = f"demo_user_{int(time.time())}"
    
    messages = [
        "你好，我想开始健身",
        "我是初学者，想要减脂",
        "请为我制定一个训练计划"
    ]
    
    for message in messages:
        print(f"用户: {message}")
        result = client.send_message(message, user_id)
        if result["success"]:
            print(f"AI助手: {result['data']['response'][:100]}...")
        time.sleep(1)
    
    # 3. 训练计划生成
    print("\n🏋️ 训练计划生成演示...")
    user_profile = {
        "age": 25,
        "gender": "male",
        "height": 175,
        "weight": 80,
        "fitness_level": "beginner"
    }
    
    goals = {
        "primary_goal": "weight_loss",
        "target_weight": 75,
        "timeline_weeks": 12
    }
    
    plan_result = client.create_training_plan(user_profile, goals)
    if plan_result["success"]:
        print("✅ 训练计划生成成功")
        plan = plan_result["data"]["training_plan"]
        print(f"计划名称: {plan.get('plan_name', 'N/A')}")
    
    print("\n🎉 演示完成！")

if __name__ == "__main__":
    main()
```

## 📚 更多示例

- [高级功能示例](advanced_features.md) - 复杂功能和集成示例
- [集成示例](integration_examples.md) - 第三方系统集成
- [自定义扩展](custom_extensions.md) - 功能扩展开发

---

**文档版本**: v1.0  
**最后更新**: 2025年1月25日  
**维护团队**: ScienceFit AI团队
