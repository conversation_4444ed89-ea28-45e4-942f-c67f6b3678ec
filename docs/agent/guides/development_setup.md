# 开发环境搭建指南

## 🎯 开发环境概述

本指南将帮助开发者搭建完整的智能健身AI助手系统开发环境，包括代码编辑、调试、测试和部署等全套开发工具链。

## 📋 环境要求

### 系统要求
- **操作系统**: Linux (Ubuntu 20.04+) / macOS 10.15+ / Windows 10+
- **内存**: 最少8GB，推荐16GB+
- **磁盘空间**: 最少20GB可用空间
- **网络**: 稳定的互联网连接

### 必需软件

#### 基础工具
- **Python**: 3.8+ (推荐3.9+)
- **Node.js**: 16+ (用于前端工具)
- **Git**: 2.0+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

#### 开发工具
- **IDE**: VS Code / PyCharm / Vim
- **数据库客户端**: DBeaver / pgAdmin
- **API测试**: Postman / Insomnia
- **版本控制**: Git + GitHub Desktop (可选)

## 🛠️ 安装步骤

### 步骤1：克隆项目

```bash
# 克隆项目代码
git clone https://github.com/sciencefit/ai-assistant.git
cd ai-assistant

# 查看项目结构
tree -L 2
```

### 步骤2：Python环境配置

#### 使用pyenv管理Python版本
```bash
# 安装pyenv (Linux/macOS)
curl https://pyenv.run | bash

# 安装Python 3.9
pyenv install 3.9.16
pyenv local 3.9.16

# 验证Python版本
python --version
```

#### 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Linux/macOS:
source .venv/bin/activate
# Windows:
.venv\Scripts\activate

# 升级pip
pip install --upgrade pip
```

### 步骤3：安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 验证安装
pip list | grep fastapi
```

### 步骤4：环境配置

```bash
# 复制环境配置文件
cp .env.example .env.dev

# 编辑开发环境配置
nano .env.dev
```

**开发环境配置示例**:
```bash
# 环境设置
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/fitness_ai_dev
REDIS_URL=redis://localhost:6379/0

# AI模型配置
OPENAI_API_KEY=your_openai_key_here
DASHSCOPE_API_KEY=your_dashscope_key_here

# 开发工具配置
ENABLE_PROFILER=true
ENABLE_DEBUG_TOOLBAR=true
RELOAD_ON_CHANGE=true

# 测试配置
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/fitness_ai_test
```

### 步骤5：数据库设置

#### 启动数据库服务
```bash
# 使用Docker启动PostgreSQL和Redis
docker-compose -f docker-compose.dev.yml up -d db redis

# 验证数据库连接
docker-compose exec db psql -U postgres -c "SELECT version();"
```

#### 初始化数据库
```bash
# 运行数据库迁移
alembic upgrade head

# 创建开发数据
python scripts/create_dev_data.py

# 验证数据
python -c "
from app.database import get_db
from app.models.user import User
db = next(get_db())
print(f'用户数量: {db.query(User).count()}')
"
```

## 🔧 开发工具配置

### VS Code配置

#### 安装推荐扩展
```json
// .vscode/extensions.json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.flake8",
    "ms-python.black-formatter",
    "ms-python.isort",
    "ms-python.mypy-type-checker",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-docker"
  ]
}
```

#### 工作区设置
```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "./.venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": ["tests/"],
  "files.exclude": {
    "**/__pycache__": true,
    "**/.pytest_cache": true,
    "**/.mypy_cache": true
  }
}
```

#### 调试配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "FastAPI Dev Server",
      "type": "python",
      "request": "launch",
      "program": "-m",
      "args": ["uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/.env.dev"
    },
    {
      "name": "Python: Current File",
      "type": "python",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/.env.dev"
    },
    {
      "name": "Python: Pytest",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": ["${workspaceFolder}/tests/"],
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/.env.dev"
    }
  ]
}
```

### Git配置

#### Git钩子配置
```bash
# 配置pre-commit
cat > .pre-commit-config.yaml << EOF
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
  
  - repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
      - id: black
        language_version: python3
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203]
EOF

# 安装钩子
pre-commit install
```

## 🚀 启动开发服务

### 方式1：直接启动

```bash
# 激活虚拟环境
source .venv/bin/activate

# 启动API服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动Web界面（新终端）
streamlit run app/web/main.py --server.port 8501
```

### 方式2：使用Docker Compose

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps
```

### 方式3：使用开发脚本

```bash
# 使用开发脚本启动
./scripts/dev-start.sh

# 停止开发环境
./scripts/dev-stop.sh

# 重启开发环境
./scripts/dev-restart.sh
```

## 🧪 测试环境

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_api.py

# 运行特定测试函数
pytest tests/test_api.py::test_health_check

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html

# 查看覆盖率报告
open htmlcov/index.html
```

### 测试配置

```python
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --disable-warnings
    --tb=short
    -v
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    api: API tests
```

## 🔍 调试技巧

### 日志调试

```python
# 配置开发日志
import logging

# 设置日志级别
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 在代码中添加调试日志
logger = logging.getLogger(__name__)
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
```

### 断点调试

```python
# 使用pdb调试
import pdb; pdb.set_trace()

# 使用ipdb调试（推荐）
import ipdb; ipdb.set_trace()

# 使用VS Code断点
# 在代码行左侧点击设置断点，然后按F5启动调试
```

### 性能分析

```python
# 使用cProfile分析性能
python -m cProfile -o profile.stats app/main.py

# 使用line_profiler分析行级性能
@profile
def slow_function():
    # 需要分析的代码
    pass

# 运行分析
kernprof -l -v script.py
```

## 📊 开发工作流

### 功能开发流程

1. **创建功能分支**
```bash
git checkout -b feature/new-feature
```

2. **编写代码**
```bash
# 编写功能代码
# 编写测试代码
# 更新文档
```

3. **运行测试**
```bash
pytest tests/
black app/
flake8 app/
mypy app/
```

4. **提交代码**
```bash
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature
```

5. **创建Pull Request**
```bash
# 在GitHub上创建PR
# 等待代码审查
# 合并到主分支
```

### 代码质量检查

```bash
# 代码格式化
black app/ tests/
isort app/ tests/

# 代码风格检查
flake8 app/ tests/

# 类型检查
mypy app/

# 安全检查
bandit -r app/

# 依赖检查
safety check
```

## 🔧 常用开发命令

### 数据库操作

```bash
# 创建新的迁移
alembic revision --autogenerate -m "add new table"

# 应用迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1

# 查看迁移历史
alembic history
```

### 依赖管理

```bash
# 添加新依赖
pip install package_name
pip freeze > requirements.txt

# 更新依赖
pip install --upgrade package_name

# 检查过期依赖
pip list --outdated

# 安全检查
pip-audit
```

### 代码生成

```bash
# 生成API文档
python scripts/generate_api_docs.py

# 生成数据模型
python scripts/generate_models.py

# 生成测试代码
python scripts/generate_tests.py
```

## 🆘 常见问题

### 环境问题

1. **Python版本不匹配**
```bash
# 检查Python版本
python --version
pyenv versions

# 切换Python版本
pyenv local 3.9.16
```

2. **依赖安装失败**
```bash
# 清理pip缓存
pip cache purge

# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple package_name
```

3. **数据库连接失败**
```bash
# 检查数据库状态
docker-compose ps db

# 重启数据库
docker-compose restart db

# 检查连接配置
echo $DATABASE_URL
```

### 开发问题

1. **代码格式化冲突**
```bash
# 统一代码格式
black app/ tests/
isort app/ tests/
```

2. **测试失败**
```bash
# 清理测试缓存
pytest --cache-clear

# 重新创建测试数据库
dropdb fitness_ai_test
createdb fitness_ai_test
alembic upgrade head
```

3. **导入错误**
```bash
# 检查PYTHONPATH
echo $PYTHONPATH

# 设置PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

## 📚 参考资源

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [SQLAlchemy文档](https://docs.sqlalchemy.org/)
- [Pytest文档](https://docs.pytest.org/)
- [Black代码格式化](https://black.readthedocs.io/)
- [VS Code Python扩展](https://code.visualstudio.com/docs/python/python-tutorial)

---

**文档版本**: v1.0  
**最后更新**: 2025年1月25日  
**维护团队**: ScienceFit AI团队
