# 系统监控指南

## 📊 监控概述

智能健身AI助手系统提供全方位的监控体系，包括系统性能监控、业务指标监控、错误追踪和告警机制。本指南将帮助您了解如何有效监控和维护系统。

## 🏗️ 监控架构

### 监控层次

```
┌─────────────────────────────────────────────────────────────┐
│                    监控仪表板                                │
├─────────────────────────────────────────────────────────────┤
│  Grafana  │  自定义面板  │  告警中心  │  报告生成            │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    指标收集层                                │
├─────────────────────────────────────────────────────────────┤
│  系统指标  │  应用指标  │  业务指标  │  自定义指标          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                │
├─────────────────────────────────────────────────────────────┤
│  时序数据库  │  日志存储  │  指标缓存  │  历史归档          │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **指标收集器**: 收集各类系统和业务指标
2. **监控面板**: 实时展示系统状态和性能
3. **告警系统**: 异常情况自动告警
4. **日志分析**: 系统日志收集和分析

## 📈 监控指标

### 1. 系统性能指标

#### CPU监控
```bash
# 查看CPU使用率
curl http://localhost:8000/v1/system/metrics | jq '.cpu'

# 预期输出
{
  "usage_percent": 25.5,
  "load_average": [1.2, 1.1, 1.0],
  "core_count": 4
}
```

#### 内存监控
```bash
# 查看内存使用情况
curl http://localhost:8000/v1/system/metrics | jq '.memory'

# 预期输出
{
  "total_mb": 8192,
  "used_mb": 3072,
  "available_mb": 5120,
  "usage_percent": 37.5
}
```

#### 磁盘监控
```bash
# 查看磁盘使用情况
curl http://localhost:8000/v1/system/metrics | jq '.disk'

# 预期输出
{
  "total_gb": 100,
  "used_gb": 45,
  "available_gb": 55,
  "usage_percent": 45.0
}
```

### 2. 应用性能指标

#### API性能
```bash
# 查看API性能指标
curl http://localhost:8000/v1/system/metrics | jq '.api'

# 预期输出
{
  "requests_per_second": 50,
  "average_response_time_ms": 120,
  "error_rate_percent": 0.5,
  "active_connections": 25
}
```

#### 数据库性能
```bash
# 查看数据库性能
curl http://localhost:8000/v1/system/metrics | jq '.database'

# 预期输出
{
  "connection_pool_size": 20,
  "active_connections": 8,
  "query_avg_time_ms": 15,
  "slow_queries_count": 2
}
```

#### 缓存性能
```bash
# 查看缓存性能
curl http://localhost:8000/v1/system/metrics | jq '.cache'

# 预期输出
{
  "hit_rate_percent": 85.5,
  "total_requests": 10000,
  "cache_size_mb": 256,
  "eviction_count": 50
}
```

### 3. 业务指标

#### 用户活跃度
```bash
# 查看用户活跃度
curl http://localhost:8000/v1/system/metrics | jq '.business.users'

# 预期输出
{
  "active_users_1h": 150,
  "active_users_24h": 1200,
  "new_users_24h": 50,
  "total_users": 10000
}
```

#### AI处理指标
```bash
# 查看AI处理指标
curl http://localhost:8000/v1/system/metrics | jq '.business.ai'

# 预期输出
{
  "total_requests": 5000,
  "success_rate_percent": 98.5,
  "average_processing_time_ms": 250,
  "model_usage": {
    "gpt-4": 3000,
    "qwen": 2000
  }
}
```

## 🔔 告警配置

### 告警规则

#### 系统资源告警
```yaml
# alerts.yml
groups:
  - name: system_alerts
    rules:
      - alert: HighCPUUsage
        expr: cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value }}%"
      
      - alert: HighMemoryUsage
        expr: memory_usage_percent > 90
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率超过90%，当前值: {{ $value }}%"
      
      - alert: DiskSpaceLow
        expr: disk_usage_percent > 85
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘使用率超过85%，当前值: {{ $value }}%"
```

#### 应用性能告警
```yaml
  - name: application_alerts
    rules:
      - alert: HighErrorRate
        expr: api_error_rate_percent > 5
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "API错误率过高"
          description: "API错误率超过5%，当前值: {{ $value }}%"
      
      - alert: SlowResponse
        expr: api_avg_response_time_ms > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应时间过长"
          description: "API平均响应时间超过1秒，当前值: {{ $value }}ms"
      
      - alert: DatabaseConnectionHigh
        expr: db_active_connections > 18
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
          description: "数据库活跃连接数超过18，当前值: {{ $value }}"
```

### 告警通知

#### 邮件通知配置
```python
# app/core/alerting.py
ALERT_CONFIG = {
    "email": {
        "enabled": True,
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "username": "<EMAIL>",
        "password": "your_password",
        "recipients": [
            "<EMAIL>",
            "<EMAIL>"
        ]
    }
}
```

#### 钉钉/企业微信通知
```python
ALERT_CONFIG = {
    "dingtalk": {
        "enabled": True,
        "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=xxx",
        "secret": "your_secret"
    },
    "wechat": {
        "enabled": True,
        "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx"
    }
}
```

## 📊 监控面板

### 系统概览面板

#### 关键指标展示
```json
{
  "dashboard": {
    "title": "系统概览",
    "panels": [
      {
        "title": "系统健康状态",
        "type": "stat",
        "targets": [
          "system_health_score"
        ]
      },
      {
        "title": "API请求量",
        "type": "graph",
        "targets": [
          "api_requests_per_second"
        ]
      },
      {
        "title": "响应时间分布",
        "type": "heatmap",
        "targets": [
          "api_response_time_histogram"
        ]
      },
      {
        "title": "错误率趋势",
        "type": "graph",
        "targets": [
          "api_error_rate_percent"
        ]
      }
    ]
  }
}
```

### 业务监控面板

#### 用户行为分析
```json
{
  "dashboard": {
    "title": "业务监控",
    "panels": [
      {
        "title": "用户活跃度",
        "type": "graph",
        "targets": [
          "active_users_1h",
          "active_users_24h"
        ]
      },
      {
        "title": "对话成功率",
        "type": "stat",
        "targets": [
          "conversation_success_rate"
        ]
      },
      {
        "title": "AI模型使用分布",
        "type": "pie",
        "targets": [
          "model_usage_distribution"
        ]
      }
    ]
  }
}
```

## 🔍 日志监控

### 日志级别

| 级别 | 用途 | 示例 |
|------|------|------|
| DEBUG | 调试信息 | 详细的执行流程 |
| INFO | 一般信息 | 请求处理、状态变更 |
| WARNING | 警告信息 | 性能问题、配置问题 |
| ERROR | 错误信息 | 处理失败、异常情况 |
| CRITICAL | 严重错误 | 系统故障、安全问题 |

### 日志查询

#### 查看实时日志
```bash
# 查看API服务日志
docker-compose logs -f api

# 查看特定时间范围的日志
docker-compose logs --since="2025-01-25T10:00:00" api

# 查看错误日志
docker-compose logs api | grep ERROR
```

#### 日志分析
```bash
# 统计错误数量
docker-compose logs api | grep ERROR | wc -l

# 分析响应时间
docker-compose logs api | grep "response_time" | awk '{print $NF}' | sort -n

# 查看最频繁的错误
docker-compose logs api | grep ERROR | sort | uniq -c | sort -nr
```

### 日志聚合

#### ELK Stack集成
```yaml
# docker-compose.yml
services:
  elasticsearch:
    image: elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"
  
  logstash:
    image: logstash:7.14.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch
  
  kibana:
    image: kibana:7.14.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

## 🚨 故障处理

### 常见故障场景

#### 1. API响应超时
**症状**: API响应时间超过30秒
**排查步骤**:
```bash
# 1. 检查系统资源
curl http://localhost:8000/v1/system/metrics

# 2. 查看API日志
docker-compose logs api | tail -100

# 3. 检查数据库连接
docker-compose exec api python -c "
from app.database import get_db
print('数据库连接正常')
"

# 4. 重启服务
docker-compose restart api
```

#### 2. 数据库连接池耗尽
**症状**: 数据库连接错误
**排查步骤**:
```bash
# 1. 检查连接池状态
curl http://localhost:8000/v1/system/metrics | jq '.database'

# 2. 查看数据库日志
docker-compose logs db | tail -50

# 3. 检查长时间运行的查询
docker-compose exec db psql -U postgres -c "
SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';
"

# 4. 重启数据库服务
docker-compose restart db
```

#### 3. 内存泄漏
**症状**: 内存使用持续增长
**排查步骤**:
```bash
# 1. 监控内存使用趋势
watch -n 5 'curl -s http://localhost:8000/v1/system/metrics | jq .memory'

# 2. 分析内存使用
docker stats ai-assistant-api

# 3. 检查应用内存泄漏
docker-compose exec api python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'内存使用: {process.memory_info().rss / 1024 / 1024:.2f} MB')
"

# 4. 重启应用服务
docker-compose restart api
```

## 📋 监控检查清单

### 日常检查项目

- [ ] 系统健康状态检查
- [ ] API响应时间监控
- [ ] 错误率趋势分析
- [ ] 数据库性能检查
- [ ] 缓存命中率监控
- [ ] 磁盘空间检查
- [ ] 日志错误分析

### 周期性检查项目

#### 每日检查
- [ ] 系统资源使用趋势
- [ ] 用户活跃度统计
- [ ] AI模型性能分析
- [ ] 备份状态确认

#### 每周检查
- [ ] 性能基准对比
- [ ] 容量规划评估
- [ ] 安全日志审计
- [ ] 监控规则优化

#### 每月检查
- [ ] 系统性能报告
- [ ] 容量扩展计划
- [ ] 监控体系优化
- [ ] 灾备演练验证

## 📊 性能基准

### 关键指标基准

| 指标 | 正常范围 | 警告阈值 | 严重阈值 |
|------|---------|---------|---------|
| CPU使用率 | < 70% | 70-80% | > 80% |
| 内存使用率 | < 80% | 80-90% | > 90% |
| 磁盘使用率 | < 80% | 80-90% | > 90% |
| API响应时间 | < 200ms | 200-1000ms | > 1000ms |
| 错误率 | < 1% | 1-5% | > 5% |
| 缓存命中率 | > 80% | 60-80% | < 60% |

---

**文档版本**: v1.0  
**最后更新**: 2025年1月25日  
**维护团队**: ScienceFit AI团队
