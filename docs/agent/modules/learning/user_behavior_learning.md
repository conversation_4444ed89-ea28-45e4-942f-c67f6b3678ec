# 用户行为学习模块

## 📖 模块概述

用户行为学习模块是智能健身AI助手系统的核心组件之一，负责收集、分析和学习用户的行为模式，为个性化服务提供数据基础。该模块通过机器学习算法持续学习用户偏好，不断优化系统响应质量。

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                用户行为学习模块                              │
├─────────────────────────────────────────────────────────────┤
│  数据收集器  │  行为分析器  │  偏好学习器  │  模式识别器    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                数据存储层                                   │
├─────────────────────────────────────────────────────────────┤
│  交互记录  │  用户偏好  │  行为模式  │  学习模型            │
└─────────────────────────────────────────────────────────────┘
```

### 主要类结构

```python
class UserBehaviorLearner:
    """用户行为学习器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None)
    async def record_interaction(self, interaction: UserInteraction) -> bool
    async def learn_user_preferences(self, user_id: str) -> UserPreferences
    async def detect_behavior_patterns(self, user_id: str) -> List[BehaviorPattern]
    def get_learning_summary(self, user_id: str) -> Dict[str, Any]
```

## 🔧 功能特性

### 1. 交互数据收集
- **实时收集**: 自动收集用户的每次交互数据
- **多维度记录**: 记录消息内容、意图、响应时间等
- **隐私保护**: 敏感信息脱敏处理
- **数据验证**: 确保数据质量和完整性

### 2. 用户偏好学习
- **偏好识别**: 自动识别用户的健身偏好
- **权重计算**: 基于交互频率计算偏好权重
- **动态更新**: 实时更新用户偏好模型
- **置信度评估**: 评估偏好学习的可靠性

### 3. 行为模式检测
- **模式识别**: 识别用户的行为模式
- **时间分析**: 分析用户的活跃时间规律
- **习惯发现**: 发现用户的使用习惯
- **趋势预测**: 预测用户行为趋势

### 4. 学习效果评估
- **准确性评估**: 评估学习结果的准确性
- **覆盖度分析**: 分析学习的全面性
- **时效性检查**: 检查学习的时效性
- **改进建议**: 提供学习优化建议

## 📋 API接口

### 记录用户交互

```python
async def record_interaction(
    self, 
    interaction: UserInteraction
) -> bool:
    """
    记录用户交互数据
    
    Args:
        interaction: 用户交互对象
        
    Returns:
        bool: 记录是否成功
    """
```

**使用示例**:
```python
from app.services.ai_assistant.intelligence.learning.user_behavior_learner import UserBehaviorLearner
from app.services.ai_assistant.intelligence.learning.learning_models import create_interaction_from_message

learner = UserBehaviorLearner()

# 创建交互记录
interaction = create_interaction_from_message(
    user_id="user123",
    message="我想制定一个减脂计划",
    intent="training_plan",
    confidence=0.9,
    response_time_ms=150
)

# 记录交互
success = await learner.record_interaction(interaction)
```

### 学习用户偏好

```python
async def learn_user_preferences(
    self, 
    user_id: str
) -> UserPreferences:
    """
    学习用户偏好
    
    Args:
        user_id: 用户ID
        
    Returns:
        UserPreferences: 用户偏好对象
    """
```

**使用示例**:
```python
# 学习用户偏好
preferences = await learner.learn_user_preferences("user123")

print(f"用户偏好: {preferences.preferences}")
print(f"置信度: {preferences.confidence_score}")
print(f"更新时间: {preferences.last_updated}")
```

### 检测行为模式

```python
async def detect_behavior_patterns(
    self, 
    user_id: str
) -> List[BehaviorPattern]:
    """
    检测用户行为模式
    
    Args:
        user_id: 用户ID
        
    Returns:
        List[BehaviorPattern]: 行为模式列表
    """
```

**使用示例**:
```python
# 检测行为模式
patterns = await learner.detect_behavior_patterns("user123")

for pattern in patterns:
    print(f"模式类型: {pattern.pattern_type}")
    print(f"模式描述: {pattern.description}")
    print(f"置信度: {pattern.confidence}")
```

## ⚙️ 配置参数

### 基础配置

```python
LEARNING_CONFIG = {
    "learning_rate": 0.01,           # 学习率
    "min_interactions": 5,           # 最小交互次数
    "preference_threshold": 0.3,     # 偏好阈值
    "pattern_min_frequency": 3,      # 模式最小频率
    "data_retention_days": 90,       # 数据保留天数
    "batch_size": 100,               # 批处理大小
    "update_interval": 3600          # 更新间隔（秒）
}
```

### 高级配置

```python
ADVANCED_CONFIG = {
    "enable_real_time_learning": True,    # 启用实时学习
    "enable_pattern_detection": True,     # 启用模式检测
    "enable_preference_decay": True,      # 启用偏好衰减
    "decay_factor": 0.95,                 # 衰减因子
    "similarity_threshold": 0.8,          # 相似度阈值
    "max_patterns_per_user": 10           # 每用户最大模式数
}
```

## 📊 数据模型

### UserInteraction 用户交互

```python
@dataclass
class UserInteraction:
    user_id: str                    # 用户ID
    message: str                    # 用户消息
    intent: str                     # 识别意图
    confidence: float               # 置信度
    response_time_ms: float         # 响应时间
    timestamp: datetime             # 时间戳
    session_id: str                 # 会话ID
    metadata: Dict[str, Any]        # 元数据
```

### UserPreferences 用户偏好

```python
@dataclass
class UserPreferences:
    user_id: str                    # 用户ID
    preferences: Dict[str, float]   # 偏好权重
    confidence_score: float         # 整体置信度
    last_updated: datetime          # 最后更新时间
    interaction_count: int          # 交互次数
    learning_status: str            # 学习状态
```

### BehaviorPattern 行为模式

```python
@dataclass
class BehaviorPattern:
    pattern_id: str                 # 模式ID
    user_id: str                    # 用户ID
    pattern_type: str               # 模式类型
    description: str                # 模式描述
    frequency: int                  # 出现频率
    confidence: float               # 置信度
    first_seen: datetime            # 首次发现
    last_seen: datetime             # 最后出现
```

## 🧪 使用示例

### 完整使用流程

```python
import asyncio
from app.services.ai_assistant.intelligence.learning.user_behavior_learner import UserBehaviorLearner
from app.services.ai_assistant.intelligence.learning.learning_models import create_interaction_from_message

async def main():
    # 初始化学习器
    learner = UserBehaviorLearner({
        "learning_rate": 0.01,
        "min_interactions": 3
    })
    
    user_id = "user123"
    
    # 模拟用户交互
    interactions = [
        ("我想减脂", "training_plan", 0.9),
        ("推荐一些有氧运动", "exercise_action", 0.8),
        ("制定饮食计划", "diet_advice", 0.85),
        ("我喜欢跑步", "exercise_action", 0.9),
        ("有什么减脂食谱", "diet_advice", 0.8)
    ]
    
    # 记录交互
    for message, intent, confidence in interactions:
        interaction = create_interaction_from_message(
            user_id=user_id,
            message=message,
            intent=intent,
            confidence=confidence,
            response_time_ms=100
        )
        await learner.record_interaction(interaction)
    
    # 学习用户偏好
    preferences = await learner.learn_user_preferences(user_id)
    print(f"用户偏好: {preferences.preferences}")
    
    # 检测行为模式
    patterns = await learner.detect_behavior_patterns(user_id)
    for pattern in patterns:
        print(f"发现模式: {pattern.description}")
    
    # 获取学习摘要
    summary = learner.get_learning_summary(user_id)
    print(f"学习摘要: {summary}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔍 故障排除

### 常见问题

1. **学习效果不佳**
   - 检查交互数据质量
   - 调整学习率参数
   - 增加最小交互次数

2. **偏好识别不准确**
   - 检查偏好阈值设置
   - 验证交互数据标注
   - 调整相似度阈值

3. **模式检测失效**
   - 检查模式最小频率
   - 验证时间窗口设置
   - 调整检测算法参数

### 调试方法

```python
# 启用调试日志
import logging
logging.getLogger("user_behavior_learner").setLevel(logging.DEBUG)

# 获取详细学习信息
summary = learner.get_learning_summary(user_id)
print(f"详细信息: {summary}")

# 检查数据质量
interactions = learner.get_user_interactions(user_id, limit=10)
for interaction in interactions:
    print(f"交互: {interaction}")
```

## 📈 性能优化

### 优化建议

1. **批量处理**: 使用批量学习提高效率
2. **缓存策略**: 缓存常用的学习结果
3. **异步处理**: 使用异步方式处理学习任务
4. **数据清理**: 定期清理过期数据

### 监控指标

- **学习延迟**: 学习过程的响应时间
- **准确率**: 偏好识别的准确性
- **覆盖率**: 用户行为的覆盖程度
- **内存使用**: 学习过程的内存消耗

---

**文档版本**: v1.0  
**最后更新**: 2025年1月25日  
**维护团队**: ScienceFit AI团队
