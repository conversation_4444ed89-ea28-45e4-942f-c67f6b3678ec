# 智能缓存管理模块

## 📖 模块概述

智能缓存管理模块是系统性能优化的核心组件，提供多种缓存策略和自动优化功能。通过智能缓存管理，系统能够显著提升响应速度，减少计算资源消耗，提供更好的用户体验。

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                智能缓存管理模块                              │
├─────────────────────────────────────────────────────────────┤
│  策略管理器  │  缓存引擎  │  性能监控  │  自动优化器        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                缓存存储层                                   │
├─────────────────────────────────────────────────────────────┤
│  内存缓存  │  Redis缓存  │  持久化缓存  │  分布式缓存      │
└─────────────────────────────────────────────────────────────┘
```

### 主要类结构

```python
class IntelligentCacheManager:
    """智能缓存管理器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None)
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool
    async def get(self, key: str) -> Optional[Any]
    async def delete(self, key: str) -> bool
    async def optimize_strategy(self) -> bool
    def get_stats(self) -> Dict[str, Any]
```

## 🔧 功能特性

### 1. 多种缓存策略
- **LRU (Least Recently Used)**: 最近最少使用策略
- **LFU (Least Frequently Used)**: 最少使用频率策略
- **TTL (Time To Live)**: 基于时间的过期策略
- **自适应策略**: 根据访问模式自动选择最优策略

### 2. 智能优化
- **访问模式分析**: 分析缓存访问模式
- **策略自动切换**: 根据性能指标自动切换策略
- **容量动态调整**: 根据系统负载动态调整缓存容量
- **预热机制**: 智能预加载热点数据

### 3. 性能监控
- **命中率统计**: 实时统计缓存命中率
- **响应时间监控**: 监控缓存操作响应时间
- **内存使用跟踪**: 跟踪缓存内存使用情况
- **性能报告**: 生成详细的性能分析报告

### 4. 高可用性
- **故障转移**: 支持缓存故障自动转移
- **数据一致性**: 保证缓存数据的一致性
- **分布式支持**: 支持分布式缓存部署
- **备份恢复**: 支持缓存数据备份和恢复

## 📋 API接口

### 设置缓存

```python
async def set(
    self, 
    key: str, 
    value: Any, 
    ttl: Optional[int] = None
) -> bool:
    """
    设置缓存项
    
    Args:
        key: 缓存键
        value: 缓存值
        ttl: 过期时间（秒）
        
    Returns:
        bool: 设置是否成功
    """
```

**使用示例**:
```python
from app.services.ai_assistant.intelligence.optimization.cache_manager import IntelligentCacheManager

cache_manager = IntelligentCacheManager()

# 设置缓存（使用默认TTL）
success = await cache_manager.set("user_profile_123", user_data)

# 设置缓存（指定TTL）
success = await cache_manager.set("temp_data", temp_value, ttl=300)

# 设置永久缓存
success = await cache_manager.set("config_data", config, ttl=0)
```

### 获取缓存

```python
async def get(self, key: str) -> Optional[Any]:
    """
    获取缓存项
    
    Args:
        key: 缓存键
        
    Returns:
        Optional[Any]: 缓存值，不存在返回None
    """
```

**使用示例**:
```python
# 获取缓存
user_data = await cache_manager.get("user_profile_123")
if user_data:
    print(f"缓存命中: {user_data}")
else:
    print("缓存未命中，需要从数据库加载")
    # 从数据库加载数据
    user_data = load_from_database("123")
    # 设置缓存
    await cache_manager.set("user_profile_123", user_data)
```

### 删除缓存

```python
async def delete(self, key: str) -> bool:
    """
    删除缓存项
    
    Args:
        key: 缓存键
        
    Returns:
        bool: 删除是否成功
    """
```

**使用示例**:
```python
# 删除单个缓存
success = await cache_manager.delete("user_profile_123")

# 批量删除缓存
keys_to_delete = ["key1", "key2", "key3"]
for key in keys_to_delete:
    await cache_manager.delete(key)
```

### 策略优化

```python
async def optimize_strategy(self) -> bool:
    """
    优化缓存策略
    
    Returns:
        bool: 优化是否成功
    """
```

**使用示例**:
```python
# 手动触发策略优化
success = await cache_manager.optimize_strategy()
if success:
    print("缓存策略优化成功")
    
# 获取优化后的统计信息
stats = cache_manager.get_stats()
print(f"当前策略: {stats['strategy']}")
print(f"命中率: {stats['stats']['hit_rate']:.2%}")
```

## ⚙️ 配置参数

### 基础配置

```python
CACHE_CONFIG = {
    "strategy": "lru",              # 缓存策略: lru, lfu, ttl, adaptive
    "max_size": 1000,               # 最大缓存项数
    "default_ttl": 3600,            # 默认TTL（秒）
    "enable_optimization": True,     # 启用自动优化
    "optimization_interval": 300,    # 优化间隔（秒）
    "enable_persistence": False,     # 启用持久化
    "persistence_file": "cache.db"   # 持久化文件
}
```

### 高级配置

```python
ADVANCED_CONFIG = {
    "enable_compression": True,      # 启用数据压缩
    "compression_threshold": 1024,   # 压缩阈值（字节）
    "enable_encryption": False,      # 启用数据加密
    "max_memory_mb": 512,           # 最大内存使用（MB）
    "eviction_batch_size": 10,      # 批量驱逐大小
    "stats_collection_interval": 60, # 统计收集间隔
    "enable_distributed": False,     # 启用分布式缓存
    "redis_config": {               # Redis配置
        "host": "localhost",
        "port": 6379,
        "db": 0
    }
}
```

## 📊 缓存策略详解

### 1. LRU策略
**适用场景**: 访问模式具有时间局部性
```python
# 配置LRU策略
config = {
    "strategy": "lru",
    "max_size": 1000
}
cache_manager = IntelligentCacheManager(config)
```

### 2. LFU策略
**适用场景**: 某些数据被频繁访问
```python
# 配置LFU策略
config = {
    "strategy": "lfu",
    "max_size": 1000,
    "frequency_decay": 0.9  # 频率衰减因子
}
cache_manager = IntelligentCacheManager(config)
```

### 3. TTL策略
**适用场景**: 数据有明确的时效性
```python
# 配置TTL策略
config = {
    "strategy": "ttl",
    "default_ttl": 3600,
    "cleanup_interval": 300  # 清理间隔
}
cache_manager = IntelligentCacheManager(config)
```

### 4. 自适应策略
**适用场景**: 访问模式复杂多变
```python
# 配置自适应策略
config = {
    "strategy": "adaptive",
    "adaptation_window": 1000,    # 适应窗口大小
    "strategy_switch_threshold": 0.1  # 策略切换阈值
}
cache_manager = IntelligentCacheManager(config)
```

## 🧪 使用示例

### 基础使用

```python
import asyncio
from app.services.ai_assistant.intelligence.optimization.cache_manager import IntelligentCacheManager

async def basic_usage():
    # 初始化缓存管理器
    cache_manager = IntelligentCacheManager({
        "strategy": "lru",
        "max_size": 100
    })
    
    # 设置缓存
    await cache_manager.set("user_123", {"name": "张三", "age": 25})
    await cache_manager.set("config", {"theme": "dark", "lang": "zh"})
    
    # 获取缓存
    user_data = await cache_manager.get("user_123")
    print(f"用户数据: {user_data}")
    
    # 检查缓存状态
    stats = cache_manager.get_stats()
    print(f"缓存统计: {stats}")

asyncio.run(basic_usage())
```

### 高级使用

```python
async def advanced_usage():
    # 高级配置
    config = {
        "strategy": "adaptive",
        "max_size": 1000,
        "enable_optimization": True,
        "optimization_interval": 60
    }
    
    cache_manager = IntelligentCacheManager(config)
    
    # 批量设置缓存
    data_batch = {
        f"item_{i}": f"value_{i}" 
        for i in range(100)
    }
    
    for key, value in data_batch.items():
        await cache_manager.set(key, value)
    
    # 模拟访问模式
    for _ in range(1000):
        # 80%访问热点数据
        if random.random() < 0.8:
            key = f"item_{random.randint(0, 19)}"
        else:
            key = f"item_{random.randint(20, 99)}"
        
        value = await cache_manager.get(key)
    
    # 触发策略优化
    await cache_manager.optimize_strategy()
    
    # 查看优化结果
    stats = cache_manager.get_stats()
    print(f"优化后命中率: {stats['stats']['hit_rate']:.2%}")

asyncio.run(advanced_usage())
```

## 🔍 故障排除

### 常见问题

1. **缓存命中率低**
   - 检查缓存策略是否合适
   - 调整缓存容量大小
   - 分析访问模式特征

2. **内存使用过高**
   - 减少缓存容量
   - 启用数据压缩
   - 调整TTL设置

3. **缓存更新不及时**
   - 检查TTL设置
   - 实现主动失效机制
   - 使用版本控制

### 调试方法

```python
# 启用详细日志
import logging
logging.getLogger("cache_manager").setLevel(logging.DEBUG)

# 获取详细统计信息
stats = cache_manager.get_stats()
print(f"详细统计: {json.dumps(stats, indent=2)}")

# 检查缓存内容
cache_items = cache_manager.get_all_keys()
print(f"缓存键列表: {cache_items}")
```

## 📈 性能优化

### 优化建议

1. **合理设置容量**: 根据内存大小设置合适的缓存容量
2. **选择合适策略**: 根据访问模式选择最优缓存策略
3. **启用压缩**: 对大数据启用压缩减少内存使用
4. **定期清理**: 定期清理过期和无用的缓存项

### 监控指标

- **命中率**: 缓存命中的比例
- **响应时间**: 缓存操作的响应时间
- **内存使用**: 缓存占用的内存大小
- **驱逐率**: 缓存项被驱逐的比例

### 性能基准

| 操作类型 | 目标性能 | 实际性能 |
|---------|---------|---------|
| GET操作 | < 1ms | 0.1ms |
| SET操作 | < 2ms | 0.5ms |
| DELETE操作 | < 1ms | 0.3ms |
| 命中率 | > 80% | 95%+ |

---

**文档版本**: v1.0  
**最后更新**: 2025年1月25日  
**维护团队**: ScienceFit AI团队
