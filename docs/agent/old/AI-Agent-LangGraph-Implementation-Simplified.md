# 健身AI助手 LangGraph 实现详情 (简化版)

本文档简要说明了基于LangGraph框架实现的健身AI助手的功能实现情况、处理流程和接口规范，隐去了具体的代码实现。

## 项目总览

健身AI助手是一个基于LangGraph框架构建的智能对话系统，专注于为用户提供个性化的健身指导、训练计划和专业咨询。系统采用图状工作流架构，通过专家节点路由机制实现针对性的功能处理，支持REST API和WebSocket流式响应，并集成了多级缓存和状态管理系统以提升性能。

### 核心特性

- **专家节点系统**：基于用户意图动态路由到专业领域节点处理
- **状态管理**：会话状态的持久化存储和高效检索
- **流式响应**：支持实时的消息流式返回
- **多级缓存**：优化数据库访问和消息历史处理
- **参数收集**：智能识别并收集训练计划所需参数
- **用户画像**：根据历史交互构建用户健身画像

## 实现目录结构

```
app/
├── api/
│   ├── endpoints/
│   │   ├── ai_chat.py              # AI聊天REST API接口
│   │   ├── ai_food_recognition.py  # AI食物识别接口
│   │   ├── websocket.py            # WebSocket流式响应接口
│   │   └── training_plan.py        # 训练计划相关接口
│   └── v1/
│       └── api.py                  # API路由集合
├── core/
│   ├── config.py                   # 应用配置
│   └── logging.py                  # 日志配置
├── db/
│   ├── base_class.py               # SQLAlchemy基类
│   └── session.py                  # 数据库会话管理
├── models/
│   ├── chat_session.py             # 会话模型
│   ├── message.py                  # 消息模型
│   ├── training_plan.py            # 训练计划模型
│   ├── exercise.py                 # 训练动作模型
│   └── user.py                     # 用户模型
├── schemas/
│   ├── chat.py                     # 聊天相关Pydantic模型
│   └── training_plan.py            # 训练计划Pydantic模型
├── services/
│   ├── graph_nodes/                # 各专家节点实现
│   │   ├── router_node.py          # 路由节点
│   │   ├── param_collector_node.py # 参数收集器
│   │   ├── training_plan_expert_node.py  # 训练计划专家
│   │   ├── user_info_collector_node.py   # 用户信息收集器
│   │   ├── fitness_qa_expert_node.py     # 健身咨询专家
│   │   ├── general_chat_expert_node.py   # 通用聊天专家
│   │   ├── image_analysis_expert_node.py # 图像分析专家
│   │   ├── enhanced_exercise_recommendation_expert.py # 增强运动推荐专家
│   │   └── training_progress_expert_node.py # 训练进度专家
│   ├── state_definitions.py        # 状态定义与管理
│   ├── langgraph_service.py        # 图状工作流定义与服务
│   ├── exercise_search_service.py  # 训练动作智能检索服务
│   ├── food_recognition_service.py # 食物识别服务
│   ├── llm_proxy_service.py        # LLM服务封装
│   └── memory_cache_service.py     # 内存缓存服务
├── utils/
│   ├── db_utils.py                 # 数据库工具函数
│   └── token_utils.py              # Token计算工具
└── main.py                         # 应用入口
```

## 功能实现情况

### 已实现功能

1.  **基础架构**
    *   [x] 图状工作流框架搭建
    *   [x] 状态管理系统
    *   [x] 专家节点路由机制
    *   [x] 会话状态持久化
2.  **专家节点**
    *   [x] 路由节点（使用`BAILIAN_APPS["agent-app"]`）
    *   [x] 参数收集器（支持参数类型转换和标准化）
    *   [x] 训练计划专家（使用`MODELS["exercise_generation"]`）
    *   [x] 用户信息收集器
    *   [x] 健身咨询专家（使用`MODELS["fitness_advice"]`）
    *   [x] 饮食咨询专家（使用`MODELS["nutrition_advice"]`）
    *   [x] 通用聊天专家
    *   [x] 图像分析专家（处理食物图像识别结果）
    *   [x] 增强运动推荐专家（智能推荐训练动作）
    *   [x] 训练进度专家（分析训练记录并提供反馈）
3.  **性能优化**
    *   [x] 数据库连接池优化
    *   [x] 批量数据库操作
    *   [x] 多级缓存机制
    *   [x] 消息历史压缩
    *   [x] 数据库检查点存储
4.  **接口实现**
    *   [x] REST API接口
    *   [x] WebSocket流式响应接口
    *   [x] AI食物识别接口（集成到聊天流程）
5.  **高级功能**
    *   [x] 中断处理机制（检测和处理对话中断）
    *   [x] 增强参数提取（智能提取训练相关参数）
    *   [x] 用户信息检查（智能收集和验证用户信息）
    *   [x] 会话上下文维护（压缩和管理会话上下文）
    *   [x] 对话流程优化（多路径对话和批量参数收集）
    *   [x] 回答包装（使用`MODELS["conversation"]`包装所有回答）
    *   [x] 多模态输入支持（食物图像识别）
    *   [x] 训练动作智能检索（基于多维度条件和语义搜索）

### 待实现功能

1.  **专家节点扩展**
    *   [ ] 恢复计划专家
    *   [ ] 饮食计划专家
2.  **高级功能**
    *   [ ] 多语言支持
    *   [ ] 知识库集成
    *   [ ] 训练视频分析

## 当前实现简况 (功能点概述)

### 核心功能模块

#### 1. 状态定义 (`state_definitions.py`)
定义了`ConversationState`模型，用于管理整个会话过程中的各种状态信息，如消息、用户信息、训练参数等。

#### 2. 路由节点 (`graph_nodes/router_node.py`)
负责接收用户消息，调用意图识别模型（如百炼`agent-app`）来判断用户意图，并将请求路由到相应的专家节点进行处理。

#### 3. 中断处理节点 (`graph_nodes/interruption_handler_node.py`)
用于检测对话是否发生中断，并在检测到中断时询问用户是否继续之前的话题，以维护对话的连贯性。

#### 4. 增强参数提取器 (`enhanced_parameter_extractor.py`)
继承自基础的参数提取器，提供更智能的参数提取功能，能够从用户消息和历史对话中提取显式和隐式的训练相关参数，并解决潜在的参数冲突。

#### 5. 健身QA专家节点 (`graph_nodes/fitness_qa_expert_node.py`)
处理用户关于健身、体态、营养等方面的咨询。根据问题类型选择合适的LLM（如`fitness_advice`或`nutrition_advice`）生成初步回答，并使用`conversation`模型进行包装，提供专业友好的回复。

#### 6. 图状工作流定义 (`langgraph_service.py`)
使用LangGraph的`StateGraph`定义了整个AI助手的核心工作流程。包括添加各个节点（中断处理、路由、参数收集、用户信息收集、各专家节点），设置入口节点，并配置节点之间的条件跳转逻辑。最终编译成可执行的图。

#### 7. 对话流程优化器 (`dialog_flow_optimizer.py`)
根据预定义的对话流程（如训练计划、健身问答等），尝试优化对话过程。例如，在用户表达某个意图时，尝试一次性收集所有必要的参数（批量收集），或者在信息足够时自动推进到下一步（如直接生成计划）。

#### 8. REST API实现 (`endpoints/ai_chat.py`)
提供了`/message`端点，接收用户消息，调用`LangGraphService`处理并返回完整响应。

#### 9. WebSocket API实现 (`endpoints/websocket.py` / `endpoints/ai_chat.py`)
提供了`/stream/{session_id}`的WebSocket接口，用于实现流式聊天。接收客户端消息后，调用`LangGraphService`的流式处理方法，将响应（文本片段、元数据更新、事件等）分块发送回客户端。

#### 10. 会话上下文管理器 (`conversation_context_manager.py`)
负责管理和维护会话上下文。当消息历史过长或Token数量超过阈值时，会进行压缩，例如保留最近的消息并生成前面内容的摘要，以防止上下文过大并保持对话连贯性。

#### 11. 图像分析专家节点 (`graph_nodes/image_analysis_expert_node.py`)
处理用户上传的食物图像识别结果，提供营养分析和健身相关建议。与食物识别服务集成，能够根据用户的健身目标提供个性化的饮食建议。

#### 12. 训练动作智能检索服务 (`exercise_search_service.py`)
提供多维度的训练动作检索功能，支持按肌肉群、器材、难度级别等条件组合查询，并实现语义搜索能力，提高检索准确性。服务直接连接到数据库，使用 `app/models/exercise.py` 中定义的数据结构，并利用 `app/services/sql_tool_service.py` 中的身体部位、器材和肌肉分类进行精确匹配。

#### 13. 增强运动推荐专家 (`graph_nodes/enhanced_exercise_recommendation_expert.py`)
基于用户需求和健身目标，智能推荐训练动作，提供详细的动作指导和个性化建议，与训练动作智能检索服务集成。通过 `app/services/conversation/exercise_recommender.py` 和 `app/services/conversation/exercise_helper.py` 实现高质量的训练动作推荐，确保推荐结果符合用户的健身水平和目标。

#### 14. 训练进度专家节点 (`graph_nodes/training_progress_expert_node.py`)
分析用户的训练记录，提供进度报告和改进建议，帮助用户跟踪训练效果并调整训练计划。

## 处理流程详解

### 1. 消息处理流程

```
用户消息 → REST/WebSocket接口 → LangGraph服务 → [状态准备] → 中断处理 → 路由节点 → [参数/用户信息收集] → 专家节点 → 响应生成 → [状态保存] → 返回用户
```

**详细步骤概述：**

1.  **消息接收**: 通过API接收消息及元数据。
2.  **状态准备**: 加载或初始化会话状态(`ConversationState`)。
3.  **中断处理**: 检查并处理潜在的对话中断。
4.  **意图路由**: `router_node`分析意图，决定流向。
   - 检查是否有图像分析结果，如有则路由到`image_analysis_expert_node`
   - 根据意图路由到相应专家节点
5.  **信息收集**: 如需参数或用户信息，流向`param_collector_node`或`user_info_collector_node`。
6.  **专家处理**: 根据意图流向相应的专家节点（`training_plan_expert`, `fitness_qa_expert`, `exercise_recommendation_expert`, `training_progress_expert`, etc.）。
7.  **响应生成**: 专家节点生成结果。
8.  **状态保存**: 更新并保存`ConversationState`（缓存与数据库）。
9.  **响应返回**: 通过API将结果返回给用户（完整或流式）。

### 2. 缓存机制流程

```
请求数据 → 检查内存缓存 → 缓存命中返回 / 缓存未命中 → 查询数据库 → 更新缓存 → 返回数据
```

**详细步骤概述：**

1.  **缓存查询**: 优先查询内存缓存。
2.  **数据库查询**: 缓存未命中则查询数据库。
3.  **缓存更新**: 将数据库结果存入缓存。
4.  **数据返回**: 返回数据。

### 3. 状态压缩流程

```
检查状态大小 (token数/消息数) → 超过阈值 → 保留最近消息 → (可选)生成摘要 → 构建压缩后消息列表 → 返回压缩状态
```

**详细步骤概述：**

1.  **状态评估**: 检查消息历史大小。
2.  **选择性保留**: 如超限，保留配置数量的最新消息。
3.  **(可选)摘要生成**: 对被丢弃的消息生成摘要。
4.  **构建列表**: 组合摘要（如有）和保留的最新消息。
5.  **返回结果**: 返回压缩后的消息列表。

## 接口规范

### 1. REST API (`/api/v1/ai-chat/message`)

*   **方法**: `POST`
*   **功能**: 发送单条消息并获取完整响应。
*   **请求体结构**: 包含`message` (str), `session_id` (str, optional), `user_id` (str), `meta_info` (dict, optional)。
*   **响应体结构**: 包含`response` (str), `session_id` (str), `meta_info` (dict)。

### 2. WebSocket API (`/api/v1/ai-chat/stream/{session_id}`)

*   **方法**: `WS`
*   **功能**: 建立WebSocket连接，进行流式双向通信。
*   **客户端发送结构**: JSON对象，包含`message` (str), `user_id` (str), `meta_info` (dict, optional)。
*   **服务器响应**:
    *   文本片段: 直接发送字符串。
    *   事件消息: JSON对象，包含`event` (str, 如 "meta_info_update", "training_plan", "error") 和 `data` (dict) 或 `message` (str)。

### 3. AI食物识别接口 (`/api/v1/ai-food/analyze-for-chat`)

*   **方法**: `POST`
*   **功能**: 分析食物图片并将结果集成到AI聊天流程中。
*   **请求体结构**: 表单数据，包含`image` (file), `meal_type` (str), `meal_date` (str, optional), `session_id` (str, optional), `optimize` (bool)。
*   **响应体结构**: 包含食物识别结果，同时在后台将结果发送到指定的聊天会话。
*   **实现细节**: 使用 `app/services/food_recognition_service.py` 中的 `analyze_image` 方法处理图像，通过 AI 模型识别食物并提供营养分析。

### 4. AI食物识别Base64接口 (`/api/v1/ai-food/analyze-base64-for-chat`)

*   **方法**: `POST`
*   **功能**: 使用base64编码的图片分析食物，并将结果集成到AI聊天流程中。
*   **请求体结构**: JSON对象，包含`base64_image` (str), `meal_type` (str), `session_id` (str, optional), `meal_date` (str, optional)。
*   **响应体结构**: 包含食物识别结果，同时在后台将结果发送到指定的聊天会话。
*   **实现细节**: 使用 `app/services/food_recognition_service.py` 中的 `analyze_base64_image` 方法处理 base64 编码的图像，通过 `_ai_recognition` 方法调用 AI 模型进行食物识别和营养分析。

## 配置说明

系统配置通过环境变量加载，定义在 `app/core/config.py` 中。关键配置项包括：

*   `LLM_MODEL`: 基础LLM模型名称。
*   `LLM_CHARACTER_MODEL`: 用于角色扮演或特定语气包装的模型。
*   `LLM_INTENT_RECOGNITION_MODEL`: 用于意图识别的模型/应用。
*   `LLM_EXERCISE_GENERATION_MODEL`: 用于生成训练计划的模型。
*   `LLM_PROVIDER`: LLM服务提供商。
*   `LLM_TEMPERATURE`: LLM生成温度。
*   (以及其他数据库、缓存、百炼应用等相关配置)

## 性能指标

优化后，系统在响应时间、数据库负载、内存使用和并发能力方面有显著提升。
(具体数据略)

## 最近优化

### 1. 动作检索和推荐功能优化

* **数据库集成改进**: 优化了 `exercise_search_service.py` 与数据库的集成，确保正确使用 `app/models/exercise.py` 中定义的数据结构。
* **分类数据利用**: 集成了 `app/services/sql_tool_service.py` 中的 `BODY_PART_CATEGORIES`、`EQUIPMENT_CATEGORIES` 和 `MUSCLE_CATEGORIES` 进行更精确的匹配。
* **搜索逻辑增强**: 改进了 `_normalize_body_part` 和 `_normalize_equipment` 方法，提高了名称匹配的准确性。
* **查询优化**: 增强了 `search_by_criteria` 方法，使用 ID 进行精确查询，并添加了热度排序。
* **结果格式化**: 改进了 `_format_exercise` 方法，确保返回完整的训练动作信息。

### 2. 食物识别功能优化

* **参数顺序修复**: 修复了 `analyze_base64_for_chat` 端点中的参数顺序问题，确保带默认值的参数在没有默认值的参数之后。
* **AI 识别增强**: 优化了 `_ai_recognition` 方法中的 JSON 解析和错误处理逻辑。
* **数据验证改进**: 增强了食物项数据的验证和转换过程。

### 3. 候选动作获取优化

* **ID 转换**: 在 `get_candidate_exercises` 函数中添加了字符串到 ID 的转换逻辑。
* **场景处理**: 改进了场景参数处理，自动转换为对应的器材 ID。
* **结果补充**: 确保所有返回的动作都包含必要的字段，如肌肉、组数和次数。
* **条件放宽**: 添加了在未找到结果时自动放宽条件的逻辑。

