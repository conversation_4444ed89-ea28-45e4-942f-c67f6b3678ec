# AI聊天服务架构文档

## 1. 整体架构

### 1.1 核心组件
- ConversationService: 会话服务主类，负责协调各组件实现智能对话
- LLMProxyService: LLM代理服务，处理与语言模型的交互
- SQLToolService: 数据库工具服务，处理数据库相关操作
- MemoryService: 记忆服务，管理对话历史和上下文
- ToolRegistrar: 工具注册器，管理和提供各种功能工具
- LLMLogService: LLM日志服务，记录模型交互日志

### 1.2 专门管理器
- UserProfileManager: 用户信息管理
- TrainingParamManager: 训练参数管理
- ParameterExtractor: 参数提取器
- TrainingPlanManager: 训练计划管理
- CharacterManager: 角色管理
- ConversationStateManager: 会话状态管理
- IntentHandler: 意图处理器

### 1.3 数据模型
- User: 用户信息模型
- Conversation: 会话模型
- Message: 消息模型
- TrainingPlan: 训练计划模型
- Exercise: 训练动作模型

## 2. 主要流程

### 2.1 消息处理主流程
1. 初始化会话
   - 验证/创建用户会话
   - 加载历史消息
   - 初始化状态和元数据

2. 消息处理
   - 保存用户消息
   - 确定当前状态
   - 根据状态处理消息
   - 生成响应
   - 保存AI响应

3. 状态管理
   - 跟踪对话状态
   - 处理状态转换
   - 维护元数据

### 2.2 意图处理流程
1. 意图识别
   - 分析用户输入
   - 识别用户意图
   - 提取关键参数

2. 意图执行
   - 根据意图类型选择处理器
   - 执行相应的业务逻辑
   - 生成响应

3. 特殊意图处理
   - 训练计划生成
   - 健身建议
   - 营养咨询
   - 一般聊天

### 2.3 用户信息收集流程
1. 信息需求识别
   - 检查缺失信息
   - 确定询问顺序

2. 信息收集
   - 生成询问提示
   - 验证用户输入
   - 更新用户信息

3. 重试机制
   - 处理无效输入
   - 提供重试机会
   - 允许跳过选项

### 2.4 训练参数收集流程
1. 参数识别
   - 身体部位识别
   - 训练场景确定
   - 计划类型选择

2. 参数验证
   - 验证参数完整性
   - 处理默认值
   - 确保参数一致性

3. 计划生成准备
   - 收集完整参数集
   - 转换为训练计划请求
   - 调用计划生成服务

## 3. 关键功能点

### 3.1 状态管理
- 维护对话状态
- 处理状态转换
- 保存状态信息
- 恢复中断的流程

### 3.2 上下文管理
- 维护对话历史
- 跟踪用户偏好
- 处理上下文切换
- 管理会话元数据

### 3.3 错误处理
- 输入验证
- 异常捕获
- 重试机制
- 优雅降级

### 3.4 个性化处理
- 用户信息利用
- 训练偏好记忆
- 动态调整响应
- 个性化建议

## 4. 待优化点

### 4.1 功能完善
- [ ] 增强参数提取能力
- [ ] 改进训练计划生成
- [ ] 优化状态转换逻辑
- [ ] 增加更多个性化特性

### 4.2 性能优化
- [ ] 优化数据库查询
- [ ] 改进缓存策略
- [ ] 减少不必要的LLM调用
- [ ] 优化响应生成速度

### 4.3 用户体验
- [ ] 改进错误提示
- [ ] 优化重试机制
- [ ] 增加更多引导选项
- [ ] 提供更自然的对话流程

### 4.4 系统健壮性
- [ ] 增强错误处理
- [ ] 改进日志记录
- [ ] 添加监控指标
- [ ] 优化资源使用

## 5. 开发建议

### 5.1 代码组织
- 遵循模块化原则
- 保持类职责单一
- 使用依赖注入
- 维护清晰的接口

### 5.2 测试策略
- 单元测试覆盖
- 集成测试
- 对话流程测试
- 性能测试

### 5.3 文档维护
- 及时更新文档
- 记录关键决策
- 维护API文档
- 更新测试用例

### 5.4 部署考虑
- 环境配置管理
- 监控告警设置
- 备份恢复策略
- 扩展性规划 