# 集成方案架构图和示例代码

## 系统架构对比

### 当前新版状态机架构
```
用户输入 → ConversationOrchestrator → StateManager → 当前状态
                                                        ↓
                                                   意图识别器
                                                        ↓
                                                   意图处理器
                                                        ↓
                                                    响应生成
```

### 传统意图系统架构
```
用户输入 → EnhancedIntentRecognizer → IntentHandler → 专业化处理器
            ↓                          ↓               ↓
        多层识别策略              意图分发器        流式响应生成
            ↓                          ↓               ↓
        缓存和优化               结构化数据        专业知识库
```

### 集成后的混合架构
```
用户输入 → ConversationOrchestrator → HybridRouter → 智能路由决策
                                         ↓
                    ┌─────────────────────┼─────────────────────┐
                    ↓                    ↓                     ↓
            新版状态机处理        传统系统处理           混合处理模式
                    ↓                    ↓                     ↓
            状态驱动响应          专业化响应             最优响应选择
                    ↓                    ↓                     ↓
                    └─────────────────────┼─────────────────────┘
                                         ↓
                                  UnifiedResponse
                                         ↓
                                    标准化输出
```

## 核心适配器实现示例

### 1. 增强版意图识别器适配器

```python
# app/services/ai_assistant/intent/enhanced_recognizer_adapter.py
import logging
from typing import Dict, Any, Optional
from app.services.ai_assistant.intent.recognizer import IntentResult
from app.services.intent_recognition.enhanced_recognizer import EnhancedIntentRecognizer
from app.services.intent_recognition.models import IntentData

logger = logging.getLogger(__name__)

class EnhancedIntentRecognizerAdapter:
    """增强版意图识别器适配器
    
    将传统系统的增强版意图识别器适配到新版状态机架构中
    """
    
    def __init__(self, llm_proxy):
        """初始化适配器
        
        Args:
            llm_proxy: LLM代理服务
        """
        self.llm_proxy = llm_proxy
        self.enhanced_recognizer = EnhancedIntentRecognizer(llm_proxy)
        
        # 意图映射表：传统系统 -> 新版系统
        self.intent_mapping = {
            "recommend_exercise": "exercise_action",
            "search_exercise": "exercise_action", 
            "daily_workout_plan": "training_plan",
            "weekly_workout_plan": "training_plan",
            "nutrition_advice": "diet_advice",
            "diet_suggestion": "diet_advice",
            "fitness_qa": "fitness_advice",
            "general_chat": "general_chat"
        }
        
        logger.info("增强版意图识别器适配器初始化完成")
    
    async def arecognize(self, message: str, context: Optional[Dict] = None) -> IntentResult:
        """异步识别用户意图
        
        Args:
            message: 用户输入消息
            context: 上下文信息
            
        Returns:
            意图识别结果
        """
        try:
            # 调用传统系统的增强版意图识别器
            intent_data = await self.enhanced_recognizer.recognize_intent(message, context)
            
            # 转换意图类型到新版系统格式
            mapped_intent = self.intent_mapping.get(intent_data.intent, intent_data.intent)
            
            # 转换为新版系统的IntentResult格式
            result = IntentResult(
                intent_type=mapped_intent,
                confidence=intent_data.confidence,
                parameters=intent_data.parameters or {}
            )
            
            logger.info(f"意图识别成功: {intent_data.intent} -> {mapped_intent}, 置信度: {intent_data.confidence}")
            return result
            
        except Exception as e:
            logger.error(f"增强版意图识别失败: {str(e)}")
            # 返回默认结果
            return IntentResult(
                intent_type="general_chat",
                confidence=0.5,
                parameters={}
            )
    
    def recognize(self, message: str, context: Optional[Dict] = None) -> IntentResult:
        """同步识别接口（兼容性）"""
        import asyncio
        return asyncio.run(self.arecognize(message, context))
```

### 2. 意图处理器工厂适配器

```python
# app/services/ai_assistant/intent/handler_factory_adapter.py
import logging
from typing import Dict, Any, AsyncGenerator
from app.services.conversation.intent_handler import IntentHandler
from app.services.intent_recognizer import IntentData

logger = logging.getLogger(__name__)

class IntentHandlerFactoryAdapter:
    """意图处理器工厂适配器
    
    将传统系统的专业化意图处理器适配到新版状态机中
    """
    
    def __init__(self, db, llm_proxy):
        """初始化适配器
        
        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
        """
        self.db = db
        self.llm_proxy = llm_proxy
        self.legacy_handler = IntentHandler(db, llm_proxy)
        
        # 支持的专业化意图类型
        self.specialized_intents = {
            "exercise_action", "training_plan", "diet_advice", "fitness_advice"
        }
        
        logger.info("意图处理器工厂适配器初始化完成")
    
    async def can_handle(self, intent_type: str) -> bool:
        """检查是否可以处理指定意图
        
        Args:
            intent_type: 意图类型
            
        Returns:
            是否可以处理
        """
        return intent_type in self.specialized_intents
    
    async def handle_intent(self, intent_type: str, message: str, context: Dict) -> Dict:
        """处理意图并返回结果
        
        Args:
            intent_type: 意图类型
            message: 用户消息
            context: 上下文信息
            
        Returns:
            处理结果
        """
        try:
            # 构造传统系统需要的数据格式
            intent_data = IntentData(
                intent=self._map_to_legacy_intent(intent_type),
                confidence=0.9,
                parameters=context.get("intent_parameters", {})
            )
            
            user_data = context.get("user_info", {})
            history = context.get("messages", [])
            meta_info = {
                "conversation_id": context.get("conversation_id"),
                "user_id": context.get("user_id")
            }
            
            # 调用传统系统处理器
            result_generator = self.legacy_handler.handle_intent(
                intent_data, meta_info, user_data, history
            )
            
            # 收集流式响应
            content_parts = []
            structured_data = None
            
            async for response in result_generator:
                if response.get("type") == "message":
                    content_parts.append(response.get("content", ""))
                elif response.get("type") == "structured_data":
                    structured_data = response.get("data")
            
            # 组装最终结果
            result = {
                "content": "".join(content_parts),
                "intent_type": intent_type,
                "confidence": 0.9,
                "source": "legacy_system"
            }
            
            if structured_data:
                result["structured_data"] = structured_data
            
            logger.info(f"传统系统处理意图成功: {intent_type}")
            return result
            
        except Exception as e:
            logger.error(f"传统系统处理意图失败: {str(e)}")
            return {
                "content": f"抱歉，处理您的请求时出现了问题: {str(e)}",
                "intent_type": intent_type,
                "confidence": 0.1,
                "source": "legacy_system_error"
            }
    
    def _map_to_legacy_intent(self, new_intent: str) -> str:
        """将新版意图映射到传统系统意图
        
        Args:
            new_intent: 新版系统意图
            
        Returns:
            传统系统意图
        """
        mapping = {
            "exercise_action": "recommend_exercise",
            "training_plan": "daily_workout_plan",
            "diet_advice": "nutrition_advice",
            "fitness_advice": "fitness_qa"
        }
        return mapping.get(new_intent, new_intent)
```

### 3. 混合路由器实现

```python
# app/services/ai_assistant/conversation/hybrid_router.py
import logging
from typing import Dict, Any, Optional
from app.services.ai_assistant.conversation.unified_response import UnifiedResponse

logger = logging.getLogger(__name__)

class HybridIntentRouter:
    """混合意图路由器
    
    智能路由意图到最合适的处理系统
    """
    
    def __init__(self, state_manager, legacy_adapter):
        """初始化路由器
        
        Args:
            state_manager: 状态管理器
            legacy_adapter: 传统系统适配器
        """
        self.state_manager = state_manager
        self.legacy_adapter = legacy_adapter
        
        # 路由规则配置
        self.routing_rules = {
            # 优先使用传统系统的意图（专业化程度高）
            "legacy_priority": {
                "exercise_action", "training_plan", "diet_advice"
            },
            
            # 优先使用新版状态机的意图（上下文管理好）
            "state_machine_priority": {
                "general_chat", "help", "greeting"
            },
            
            # 混合处理的意图（需要综合评估）
            "hybrid_evaluation": {
                "fitness_advice"
            }
        }
        
        logger.info("混合意图路由器初始化完成")
    
    async def route_intent(self, intent: str, message: str, context: Dict) -> Dict:
        """智能路由意图到合适的处理系统
        
        Args:
            intent: 意图类型
            message: 用户消息
            context: 上下文信息
            
        Returns:
            处理结果
        """
        try:
            # 确定路由策略
            routing_strategy = self._determine_routing_strategy(intent, context)
            
            logger.info(f"意图 {intent} 使用路由策略: {routing_strategy}")
            
            if routing_strategy == "legacy":
                # 使用传统系统处理
                result = await self._handle_with_legacy_system(intent, message, context)
            elif routing_strategy == "state_machine":
                # 使用新版状态机处理
                result = await self._handle_with_state_machine(intent, message, context)
            else:
                # 混合处理：同时调用两个系统，选择最佳结果
                result = await self._handle_with_hybrid_evaluation(intent, message, context)
            
            # 统一响应格式
            return UnifiedResponse.format_response(
                content=result.get("content", ""),
                intent=intent,
                confidence=result.get("confidence", 0.5),
                structured_data=result.get("structured_data"),
                source_system=routing_strategy
            )
            
        except Exception as e:
            logger.error(f"路由处理失败: {str(e)}")
            return UnifiedResponse.format_response(
                content=f"抱歉，处理您的请求时出现了问题: {str(e)}",
                intent=intent,
                confidence=0.1,
                source_system="error"
            )
    
    def _determine_routing_strategy(self, intent: str, context: Dict) -> str:
        """确定路由策略
        
        Args:
            intent: 意图类型
            context: 上下文信息
            
        Returns:
            路由策略：'legacy', 'state_machine', 'hybrid'
        """
        # 检查优先级规则
        if intent in self.routing_rules["legacy_priority"]:
            return "legacy"
        elif intent in self.routing_rules["state_machine_priority"]:
            return "state_machine"
        elif intent in self.routing_rules["hybrid_evaluation"]:
            return "hybrid"
        
        # 根据上下文动态决策
        conversation_length = len(context.get("messages", []))
        
        # 长对话优先使用状态机（更好的上下文管理）
        if conversation_length > 10:
            return "state_machine"
        
        # 短对话优先使用传统系统（更快的响应）
        return "legacy"
    
    async def _handle_with_legacy_system(self, intent: str, message: str, context: Dict) -> Dict:
        """使用传统系统处理"""
        return await self.legacy_adapter.handle_intent(intent, message, context)
    
    async def _handle_with_state_machine(self, intent: str, message: str, context: Dict) -> Dict:
        """使用新版状态机处理"""
        conversation_id = context.get("conversation_id")
        current_state = await self.state_manager.get_current_state(conversation_id)
        
        # 调用状态的处理方法
        result = await current_state.handle_message(message, intent, context.get("user_info", {}))
        
        return {
            "content": result.get("response", ""),
            "confidence": result.get("confidence", 0.8),
            "source": "state_machine"
        }
    
    async def _handle_with_hybrid_evaluation(self, intent: str, message: str, context: Dict) -> Dict:
        """混合评估处理"""
        # 并行调用两个系统
        import asyncio
        
        legacy_task = asyncio.create_task(
            self._handle_with_legacy_system(intent, message, context)
        )
        state_machine_task = asyncio.create_task(
            self._handle_with_state_machine(intent, message, context)
        )
        
        # 等待两个结果
        legacy_result, state_machine_result = await asyncio.gather(
            legacy_task, state_machine_task, return_exceptions=True
        )
        
        # 选择最佳结果
        return self._select_best_result(legacy_result, state_machine_result)
    
    def _select_best_result(self, legacy_result: Dict, state_machine_result: Dict) -> Dict:
        """选择最佳结果"""
        # 简单的评分机制
        legacy_score = self._calculate_result_score(legacy_result)
        state_machine_score = self._calculate_result_score(state_machine_result)
        
        if legacy_score > state_machine_score:
            legacy_result["source"] = "hybrid_legacy_selected"
            return legacy_result
        else:
            state_machine_result["source"] = "hybrid_state_machine_selected"
            return state_machine_result
    
    def _calculate_result_score(self, result: Dict) -> float:
        """计算结果评分"""
        if isinstance(result, Exception):
            return 0.0
        
        score = 0.0
        
        # 置信度权重
        score += result.get("confidence", 0.0) * 0.4
        
        # 内容长度权重（适中的长度更好）
        content_length = len(result.get("content", ""))
        if 50 <= content_length <= 500:
            score += 0.3
        elif content_length > 500:
            score += 0.2
        else:
            score += 0.1
        
        # 结构化数据权重
        if result.get("structured_data"):
            score += 0.3
        
        return score
```

## 使用示例

### 在IdleState中集成增强识别器

```python
# 修改 app/services/ai_assistant/conversation/states/idle.py
from app.services.ai_assistant.intent.enhanced_recognizer_adapter import EnhancedIntentRecognizerAdapter

class IdleState(ConversationState):
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        
        # 使用增强版意图识别器
        self.recognizer = EnhancedIntentRecognizerAdapter(self.llm_proxy)
        
        # 其他初始化代码...
```

### 在ConversationOrchestrator中集成混合路由器

```python
# 修改 app/services/ai_assistant/conversation/orchestrator.py
from app.services.ai_assistant.conversation.hybrid_router import HybridIntentRouter
from app.services.ai_assistant.intent.handler_factory_adapter import IntentHandlerFactoryAdapter

class ConversationOrchestrator:
    def __init__(self, llm_proxy=None, cache_service=None, use_bailian=False):
        # 原有初始化代码...
        
        # 初始化传统系统适配器
        self.legacy_adapter = IntentHandlerFactoryAdapter(self.db, self.llm_proxy)
        
        # 初始化混合路由器
        self.hybrid_router = HybridIntentRouter(self.state_manager, self.legacy_adapter)
    
    async def process_message(self, conversation_id: str, message: str, user_info: Dict = None):
        # 识别意图
        intent_result = await self._recognize_intent(message, conversation_id)
        
        # 使用混合路由器处理
        result = await self.hybrid_router.route_intent(
            intent_result["intent"],
            message,
            {
                "conversation_id": conversation_id,
                "user_info": user_info,
                "messages": await self.state_manager.get_conversation_history(conversation_id)
            }
        )
        
        return result
```

这个集成方案提供了一个渐进式的迁移路径，既保持了系统的稳定性，又充分利用了两个系统的优势。
