# 游戏化系统实现文档

## 概述

本文档跟踪智能健身教练小程序游戏化系统的实现进度。该系统基于`docs/gamification.md`中的设计方案，通过游戏化元素提高用户参与度和长期留存率。实现工作分为三个阶段：基础游戏化系统、社交和竞争系统、完善和优化。

## 实施计划

### 第一阶段：基础游戏化系统
- [x] 数据模型和验证模式
  - [x] 双轨等级系统（运动和饮食）
  - [x] 基础卡片系统
  - [x] 虚拟货币系统
  - [x] 基础任务与成就系统
- [x] CRUD操作
  - [x] 等级系统CRUD
  - [x] 卡片系统CRUD
  - [x] 虚拟货币系统CRUD
  - [x] 任务与成就系统CRUD
- [x] 服务层实现
  - [x] 等级服务
  - [x] 卡片服务
  - [x] 虚拟货币服务
  - [x] 任务与成就服务
- [x] API接口实现
  - [x] 等级系统API
  - [x] 卡片系统API
  - [x] 虚拟货币系统API
  - [x] 任务与成就系统API

### 第二阶段：社交和竞争系统
- [ ] 排行榜和PVP系统
- [ ] 好友互动和礼物系统
- [ ] 团队合作机制
- [ ] 健匠商城与实物兑换

### 第三阶段：完善和优化
- [ ] 长期留存策略
- [ ] 性能优化
- [ ] 高级卡片和合成系统

## 当前进度

**状态**: 第一阶段已完成 - 已实现数据库模型、验证模式、CRUD操作、服务层和API接口

## 第一阶段实现详情

### 目录结构
```
app/
├── api/
│   ├── endpoints/
│   │   └── gamification/
│   │       ├── __init__.py        # 已完成
│   │       ├── level.py           # 已完成 - 等级和属性API
│   │       ├── card.py            # 已完成 - 卡片系统API
│   │       ├── currency.py        # 已完成 - 虚拟货币API
│   │       ├── achievement.py     # 已完成 - 成就系统API
│   │       └── task.py            # 已完成 - 任务系统API
├── models/
│   └── gamification/
│       ├── __init__.py            # 已完成
│       ├── level.py               # 已完成
│       ├── card.py                # 已完成
│       ├── currency.py            # 已完成
│       ├── achievement.py         # 已完成
│       └── task.py                # 已完成
├── schemas/
│   └── gamification/
│       ├── __init__.py            # 已完成
│       ├── level.py               # 已完成
│       ├── card.py                # 已完成
│       ├── currency.py            # 已完成
│       ├── currency_extra.py      # 已完成
│       ├── achievement.py         # 已完成
│       └── task.py                # 已完成
├── crud/
│   └── gamification/
│       ├── __init__.py            # 已完成
│       ├── level.py               # 已完成
│       ├── card.py                # 已完成
│       ├── currency.py            # 已完成
│       ├── achievement.py         # 已完成
│       └── task.py                # 已完成
├── services/
│   └── gamification/
│       ├── __init__.py            # 已完成
│       ├── level_service.py       # 已完成
│       ├── card_service.py        # 已完成
│       ├── currency_service.py    # 已完成
│       ├── achievement_service.py # 已完成
│       └── task_service.py        # 已完成
```

### 技术栈
- FastAPI框架
- SQLAlchemy ORM
- Pydantic数据验证
- Redis缓存（排行榜等）

### 服务层功能概述

1. **等级服务 (LevelService)**
   - 用户等级和经验值管理
   - 双轨系统：运动路线和饮食路线
   - 等级提升和属性加成
   - 称号系统管理

2. **卡片服务 (CardService)**
   - 卡片收集和装备管理
   - 卡片合成系统
   - 随机掉落机制
   - 卡片效果计算

3. **虚拟货币服务 (CurrencyService)**
   - 虚拟货币管理（像素杠铃）
   - 交易记录跟踪
   - 商店物品购买
   - 每日获取上限控制

4. **成就服务 (AchievementService)**
   - 成就解锁和进度跟踪
   - 里程碑记录
   - 奖励领取和发放
   - 事件触发成就检查

5. **任务服务 (TaskService)**
   - 日常和周常任务生成
   - 任务进度更新
   - 每日签到系统
   - 奖励领取和发放

### API接口文档

#### 1. 等级系统API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/gamification/level/` | 获取当前用户的等级信息 |
| GET | `/gamification/level/detail` | 获取当前用户的详细等级信息，包括进度百分比 |
| GET | `/gamification/level/attributes` | 获取当前用户的属性信息 |
| GET | `/gamification/level/titles` | 获取当前用户的所有称号 |
| GET | `/gamification/level/active-title` | 获取当前用户激活的称号 |
| POST | `/gamification/level/set-active-title/{title_id}` | 设置当前用户的激活称号 |
| POST | `/gamification/level/add-exercise-experience` | 为当前用户增加运动经验值 |
| POST | `/gamification/level/add-diet-experience` | 为当前用户增加饮食经验值 |

#### 2. 卡片系统API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/gamification/card/all` | 获取所有卡片的基本信息 |
| GET | `/gamification/card/by-type/{card_type}` | 根据卡片类型获取卡片列表 |
| GET | `/gamification/card/detail/{card_id}` | 获取特定卡片的详细信息 |
| GET | `/gamification/card/user-cards` | 获取当前用户拥有的所有卡片 |
| GET | `/gamification/card/equipped` | 获取当前用户已装备的卡片 |
| GET | `/gamification/card/effects` | 获取当前用户已装备卡片的综合效果 |
| POST | `/gamification/card/toggle-equip/{card_id}` | 切换卡片的装备状态 |
| GET | `/gamification/card/synthesis-recipes` | 获取所有可用的卡片合成配方 |
| GET | `/gamification/card/synthesis-recipe/{recipe_id}` | 获取特定合成配方的详细信息 |
| POST | `/gamification/card/synthesize/{recipe_id}` | 使用指定配方合成卡片 |

#### 3. 虚拟货币系统API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/gamification/currency/balance` | 获取当前用户的虚拟货币余额 |
| GET | `/gamification/currency/transactions` | 获取当前用户的交易记录 |
| GET | `/gamification/currency/daily-transactions` | 获取当前用户某一天的交易记录 |
| GET | `/gamification/currency/shop` | 获取商店物品列表 |
| GET | `/gamification/currency/shop/{item_id}` | 获取商店物品详情 |
| POST | `/gamification/currency/purchase` | 购买商店物品 |
| GET | `/gamification/currency/purchases` | 获取当前用户的购买记录 |
| GET | `/gamification/currency/check-daily-limit` | 检查当前用户的当日获取货币是否超过限制 |

#### 4. 成就系统API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/gamification/achievement/all` | 获取所有成就 |
| GET | `/gamification/achievement/by-category/{category}` | 根据类别获取成就列表 |
| GET | `/gamification/achievement/progress` | 获取当前用户的成就进度 |
| GET | `/gamification/achievement/completed` | 获取当前用户已完成的成就 |
| POST | `/gamification/achievement/claim/{achievement_id}` | 领取成就奖励 |
| GET | `/gamification/achievement/milestones` | 获取所有里程碑 |
| GET | `/gamification/achievement/milestone-progress` | 获取当前用户的里程碑进度 |
| GET | `/gamification/achievement/completed-milestones` | 获取当前用户已完成的里程碑 |
| POST | `/gamification/achievement/update-milestone/{milestone_id}` | 更新里程碑活跃日期（打卡） |
| POST | `/gamification/achievement/claim-milestone/{milestone_id}` | 领取里程碑奖励 |
| POST | `/gamification/achievement/trigger-event/{event_type}` | 触发成就检查事件 |

#### 5. 任务系统API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/gamification/task/all` | 获取所有任务 |
| GET | `/gamification/task/by-type/{task_type}` | 根据任务类型获取任务列表 |
| GET | `/gamification/task/by-category/{category}` | 根据任务类别获取任务列表 |
| GET | `/gamification/task/active` | 获取当前用户未完成的任务 |
| GET | `/gamification/task/completed` | 获取当前用户已完成的任务 |
| POST | `/gamification/task/generate-daily` | 为当前用户生成每日任务 |
| POST | `/gamification/task/generate-weekly` | 为当前用户生成每周任务 |
| POST | `/gamification/task/update-progress` | 更新任务进度 |
| POST | `/gamification/task/claim/{task_id}` | 领取任务奖励 |
| GET | `/gamification/task/check-daily-checkin` | 检查今日是否已签到 |
| POST | `/gamification/task/daily-checkin` | 创建签到记录并领取奖励 |
| GET | `/gamification/task/checkin-history` | 获取签到历史记录 |
| POST | `/gamification/task/trigger-event/{event_type}` | 触发任务进度更新事件 |

## 更新日志

**[2023-05-22]**: 完成数据库模型和验证模式的实现
**[2023-05-23]**: 完成CRUD操作和服务层的实现
**[2023-05-25]**: 完成API接口实现 