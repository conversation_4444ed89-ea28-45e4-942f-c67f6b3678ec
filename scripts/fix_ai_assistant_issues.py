#!/usr/bin/env python3
"""
智能健身AI助手系统问题修复脚本

修复以下问题：
1. 状态处理器中的LLM代理回退问题
2. Gradio测试应用中的环境验证问题
3. 意图识别显示不一致问题
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AIAssistantIssuesFixer:
    """AI助手问题修复器"""

    def __init__(self):
        self.project_root = project_root
        self.issues_fixed = []
        self.issues_failed = []

    def fix_llm_proxy_fallback_issue(self):
        """修复LLM代理回退问题"""
        logger.info("🔧 修复LLM代理回退问题...")

        try:
            # 修复健身建议状态处理器
            fitness_advice_file = self.project_root / "app/services/ai_assistant/conversation/states/fitness_advice.py"

            if fitness_advice_file.exists():
                content = fitness_advice_file.read_text(encoding='utf-8')

                # 查找并替换问题代码
                old_code = '''        try:
            self.llm_proxy = LLMProxyFactory.get_provider("qwen")
            logger.info("FitnessAdviceState使用Qwen LLM代理")
        except Exception as e:
            logger.warning(f"无法创建Qwen代理: {str(e)}，使用默认代理")
            from app.services.ai_assistant.llm.proxy import DefaultLLMProxy
            self.llm_proxy = DefaultLLMProxy()'''

                new_code = '''        try:
            # 确保提供商已加载
            LLMProxyFactory.load_providers()
            self.llm_proxy = LLMProxyFactory.get_provider("qwen")
            logger.info("FitnessAdviceState使用Qwen LLM代理")
        except Exception as e:
            logger.error(f"无法创建Qwen代理: {str(e)}")
            # 尝试其他真实LLM提供商，避免使用DefaultLLMProxy
            try:
                available_providers = list(LLMProxyFactory._providers.keys())
                logger.info(f"可用的LLM提供商: {available_providers}")

                # 尝试使用第一个可用的真实提供商
                if available_providers:
                    fallback_provider = available_providers[0]
                    self.llm_proxy = LLMProxyFactory.get_provider(fallback_provider)
                    logger.info(f"FitnessAdviceState使用回退提供商: {fallback_provider}")
                else:
                    raise Exception("没有可用的LLM提供商")
            except Exception as e2:
                logger.error(f"所有LLM提供商都不可用: {str(e2)}")
                # 只有在所有真实提供商都失败时才使用默认代理
                from app.services.ai_assistant.llm.proxy import DefaultLLMProxy
                self.llm_proxy = DefaultLLMProxy()
                logger.warning("使用DefaultLLMProxy作为最后的回退选项")'''

                if old_code in content:
                    content = content.replace(old_code, new_code)
                    fitness_advice_file.write_text(content, encoding='utf-8')
                    self.issues_fixed.append("健身建议状态处理器LLM代理回退逻辑")
                    logger.info("✅ 修复健身建议状态处理器LLM代理回退逻辑")
                else:
                    logger.warning("⚠️ 未找到需要修复的LLM代理回退代码")

        except Exception as e:
            logger.error(f"❌ 修复LLM代理回退问题失败: {e}")
            self.issues_failed.append(f"LLM代理回退问题: {e}")

    def fix_gradio_environment_validation(self):
        """修复Gradio测试应用环境验证问题"""
        logger.info("🔧 修复Gradio环境验证问题...")

        try:
            gradio_file = self.project_root / "tests/comprehensive/interactive/gradio_test_app.py"

            if gradio_file.exists():
                content = gradio_file.read_text(encoding='utf-8')

                # 修复数据库连接验证
                old_db_check = '"数据库连接": self.db_session is not None if hasattr(self, \'db_session\') else False,'
                new_db_check = '"数据库连接": self._check_database_connection(),'

                if old_db_check in content:
                    content = content.replace(old_db_check, new_db_check)

                # 修复认证配置验证
                old_auth_check = '"认证配置": hasattr(self, \'auth_manager\') and self.auth_manager is not None,'
                new_auth_check = '"认证配置": self._check_auth_configuration(),'

                if old_auth_check in content:
                    content = content.replace(old_auth_check, new_auth_check)

                # 添加新的检查方法
                new_methods = '''
    def _check_database_connection(self) -> bool:
        """检查数据库连接"""
        try:
            from app.db.session import get_db, USE_MOCK_SESSION
            if USE_MOCK_SESSION:
                return False

            # 测试数据库连接
            db_gen = get_db()
            db = next(db_gen)
            result = db is not None and hasattr(db, 'execute')
            db_gen.close()
            return result
        except Exception:
            return False

    def _check_auth_configuration(self) -> bool:
        """检查认证配置"""
        try:
            from app.core.config import settings
            return bool(settings.SECRET_KEY and settings.ALGORITHM)
        except Exception:
            return False'''

                # 在类的末尾添加新方法
                if new_methods not in content:
                    # 找到类的最后一个方法
                    class_end_pattern = "    def generate_performance_chart(self) -> go.Figure:"
                    if class_end_pattern in content:
                        # 在generate_performance_chart方法之前插入新方法
                        content = content.replace(class_end_pattern, new_methods + "\n\n" + class_end_pattern)

                gradio_file.write_text(content, encoding='utf-8')
                self.issues_fixed.append("Gradio测试应用环境验证逻辑")
                logger.info("✅ 修复Gradio测试应用环境验证逻辑")

        except Exception as e:
            logger.error(f"❌ 修复Gradio环境验证问题失败: {e}")
            self.issues_failed.append(f"Gradio环境验证问题: {e}")

    def fix_intent_display_mapping(self):
        """修复意图显示映射问题"""
        logger.info("🔧 修复意图显示映射问题...")

        try:
            gradio_file = self.project_root / "tests/comprehensive/interactive/gradio_test_app.py"

            if gradio_file.exists():
                content = gradio_file.read_text(encoding='utf-8')

                # 查找并修复意图映射逻辑
                old_intent_mapping = '"🎯 识别意图": api_data.get(\'intent_type\', api_data.get(\'intent\', \'未知\')),'
                new_intent_mapping = '"🎯 识别意图": self._format_intent_display(api_data),'

                if old_intent_mapping in content:
                    content = content.replace(old_intent_mapping, new_intent_mapping)

                # 添加意图格式化方法
                intent_format_method = '''
    def _format_intent_display(self, api_data: Dict[str, Any]) -> str:
        """格式化意图显示"""
        intent = api_data.get('intent_type') or api_data.get('intent', 'unknown')

        # 意图显示映射
        intent_display_map = {
            'fitness_advice': '健身建议',
            'exercise_action': '运动动作',
            'training_plan': '训练计划',
            'diet_advice': '饮食建议',
            'general_chat': '一般对话',
            'help': '帮助',
            'unknown': '未知'
        }

        return intent_display_map.get(intent, intent)'''

                if intent_format_method not in content:
                    # 在_check_auth_configuration方法之后添加
                    auth_method_end = "        except Exception:\n            return False"
                    if auth_method_end in content:
                        content = content.replace(auth_method_end, auth_method_end + intent_format_method)

                gradio_file.write_text(content, encoding='utf-8')
                self.issues_fixed.append("意图显示映射逻辑")
                logger.info("✅ 修复意图显示映射逻辑")

        except Exception as e:
            logger.error(f"❌ 修复意图显示映射问题失败: {e}")
            self.issues_failed.append(f"意图显示映射问题: {e}")

    def fix_llm_proxy_generate_method(self):
        """修复意图处理器中的LLM代理generate方法调用问题"""
        logger.info("🔧 修复LLM代理generate方法调用问题...")

        # 需要修复的文件列表
        files_to_fix = [
            "app/services/ai_assistant/intent/handlers/training_plan.py",
            "app/services/ai_assistant/intent/handlers/diet_advice.py",
            "app/services/ai_assistant/intent/handlers/exercise_action.py"
        ]

        for file_path in files_to_fix:
            try:
                full_path = self.project_root / file_path
                if full_path.exists():
                    content = full_path.read_text(encoding='utf-8')

                    # 替换同步的generate调用为异步的generate_text调用
                    old_pattern = "self.llm_proxy.generate("
                    new_pattern = "import asyncio\n        response = asyncio.run(self.llm_proxy.generate_text("

                    # 查找所有的generate调用并替换
                    lines = content.split('\n')
                    modified = False

                    for i, line in enumerate(lines):
                        if old_pattern in line and "asyncio.run" not in line:
                            # 提取参数
                            indent = len(line) - len(line.lstrip())
                            spaces = ' ' * indent

                            # 替换这一行
                            if "=" in line:
                                var_name = line.split('=')[0].strip()
                                params = line.split('self.llm_proxy.generate(')[1]
                                lines[i] = f"{spaces}import asyncio"
                                lines.insert(i+1, f"{spaces}{var_name} = asyncio.run(self.llm_proxy.generate_text({params}")
                                modified = True

                    if modified:
                        content = '\n'.join(lines)
                        full_path.write_text(content, encoding='utf-8')
                        self.issues_fixed.append(f"修复{file_path}中的generate方法调用")
                        logger.info(f"✅ 修复{file_path}中的generate方法调用")
                    else:
                        logger.info(f"ℹ️ {file_path}中没有需要修复的generate方法调用")

            except Exception as e:
                logger.error(f"❌ 修复{file_path}失败: {e}")
                self.issues_failed.append(f"修复{file_path}: {e}")

    async def test_fixes(self):
        """测试修复效果"""
        logger.info("🧪 测试修复效果...")

        try:
            # 测试LLM代理
            from app.services.ai_assistant.llm.factory import LLMProxyFactory
            LLMProxyFactory.load_providers()

            # 测试健身建议状态
            from app.services.ai_assistant.conversation.states.fitness_advice import FitnessAdviceState

            context = {"conversation_id": "test_fix"}
            state = FitnessAdviceState(context)

            # 检查LLM代理类型
            llm_proxy_type = type(state.llm_proxy).__name__
            logger.info(f"健身建议状态LLM代理类型: {llm_proxy_type}")

            if llm_proxy_type != "DefaultLLMProxy":
                self.issues_fixed.append("LLM代理类型验证通过")
                logger.info("✅ LLM代理修复验证通过")
            else:
                logger.warning("⚠️ LLM代理仍在使用DefaultLLMProxy")

            # 测试实际响应
            test_response = await state.llm_proxy.chat(
                system_prompt="你是健身教练",
                user_message="胸肌怎么练"
            )

            is_mock_response = any(phrase in test_response for phrase in [
                "模拟", "基于提示", "在实际应用中"
            ])

            if not is_mock_response:
                self.issues_fixed.append("LLM响应质量验证通过")
                logger.info("✅ LLM响应质量验证通过")
            else:
                logger.warning("⚠️ LLM仍在返回模拟响应")

        except Exception as e:
            logger.error(f"❌ 测试修复效果失败: {e}")
            self.issues_failed.append(f"修复效果测试: {e}")

    def run_all_fixes(self):
        """运行所有修复"""
        logger.info("🚀 开始修复智能健身AI助手系统问题...")

        # 1. 修复LLM代理回退问题
        self.fix_llm_proxy_fallback_issue()

        # 2. 修复Gradio环境验证问题
        self.fix_gradio_environment_validation()

        # 3. 修复意图显示映射问题
        self.fix_intent_display_mapping()

        # 4. 修复LLM代理generate方法调用问题
        self.fix_llm_proxy_generate_method()

        # 5. 测试修复效果
        asyncio.run(self.test_fixes())

        # 生成修复报告
        self.generate_fix_report()

    def generate_fix_report(self):
        """生成修复报告"""
        logger.info("\n" + "="*60)
        logger.info("🎯 智能健身AI助手系统问题修复报告")
        logger.info("="*60)

        logger.info(f"\n✅ 成功修复的问题 ({len(self.issues_fixed)}):")
        for issue in self.issues_fixed:
            logger.info(f"  ✓ {issue}")

        if self.issues_failed:
            logger.info(f"\n❌ 修复失败的问题 ({len(self.issues_failed)}):")
            for issue in self.issues_failed:
                logger.info(f"  ✗ {issue}")

        success_rate = len(self.issues_fixed) / (len(self.issues_fixed) + len(self.issues_failed)) * 100 if (self.issues_fixed or self.issues_failed) else 0
        logger.info(f"\n📊 修复成功率: {success_rate:.1f}%")

        if success_rate >= 80:
            logger.info("🎉 修复基本完成！建议重新测试系统功能。")
        else:
            logger.info("⚠️ 部分问题修复失败，请检查详细日志。")


def main():
    """主函数"""
    fixer = AIAssistantIssuesFixer()
    fixer.run_all_fixes()


if __name__ == "__main__":
    main()
