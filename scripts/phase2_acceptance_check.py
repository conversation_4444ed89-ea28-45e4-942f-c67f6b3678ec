#!/usr/bin/env python3
"""
阶段二验收标准检查脚本

验证LangGraph编排层集成的所有验收标准是否达成。
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目根目录到路径
sys.path.append('/home/<USER>/backend')

class Phase2AcceptanceChecker:
    """阶段二验收标准检查器"""
    
    def __init__(self):
        self.project_root = Path('/home/<USER>/backend')
        self.results = []
        self.overall_score = 0
        self.max_score = 0
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有验收检查"""
        print("🎯 阶段二：LangGraph编排层集成 - 验收标准检查")
        print("=" * 60)
        
        # 定义验收标准
        acceptance_criteria = [
            ("LangGraph图构建和编译", self.check_graph_construction),
            ("智能路由功能", self.check_intelligent_routing),
            ("并行处理和结果选择", self.check_parallel_processing),
            ("API层无缝集成", self.check_api_integration),
            ("错误处理和回退机制", self.check_error_handling),
            ("性能要求达标", self.check_performance_requirements),
            ("测试覆盖率", self.check_test_coverage),
            ("文档完整性", self.check_documentation),
            ("配置管理", self.check_configuration),
            ("生产就绪性", self.check_production_readiness)
        ]
        
        # 执行所有检查
        for criterion_name, check_func in acceptance_criteria:
            print(f"\n🔍 检查: {criterion_name}")
            try:
                passed, score, max_score, details = await check_func()
                self.results.append({
                    "criterion": criterion_name,
                    "passed": passed,
                    "score": score,
                    "max_score": max_score,
                    "details": details
                })
                self.overall_score += score
                self.max_score += max_score
                
                status = "✅ 通过" if passed else "❌ 失败"
                print(f"   结果: {status} ({score}/{max_score})")
                if details:
                    for detail in details:
                        print(f"   - {detail}")
                        
            except Exception as e:
                print(f"   ❌ 检查失败: {str(e)}")
                self.results.append({
                    "criterion": criterion_name,
                    "passed": False,
                    "score": 0,
                    "max_score": 10,
                    "details": [f"检查异常: {str(e)}"]
                })
                self.max_score += 10
        
        # 生成总结报告
        return self.generate_final_report()
    
    async def check_graph_construction(self) -> Tuple[bool, int, int, List[str]]:
        """检查LangGraph图构建和编译"""
        details = []
        score = 0
        max_score = 10
        
        try:
            # 检查图文件存在
            graph_files = [
                "app/services/ai_assistant/langgraph/graph/simple_fitness_ai_graph.py",
                "app/services/ai_assistant/langgraph/test_basic_graph.py"
            ]
            
            for file_path in graph_files:
                if (self.project_root / file_path).exists():
                    score += 2
                    details.append(f"图文件存在: {file_path}")
                else:
                    details.append(f"图文件缺失: {file_path}")
            
            # 测试图初始化
            from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
            if basic_test_graph.initialize():
                score += 3
                details.append("图初始化成功")
            else:
                details.append("图初始化失败")
            
            # 测试图执行
            test_result = await basic_test_graph.process_message(
                "测试消息", "test_conv", {"user_id": "test_user"}
            )
            if test_result.get("success", False):
                score += 3
                details.append("图执行成功")
            else:
                details.append("图执行失败")
                
        except Exception as e:
            details.append(f"图构建检查异常: {str(e)}")
        
        return score >= 8, score, max_score, details
    
    async def check_intelligent_routing(self) -> Tuple[bool, int, int, List[str]]:
        """检查智能路由功能"""
        details = []
        score = 0
        max_score = 10
        
        try:
            from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
            
            # 测试健身相关路由
            fitness_result = await basic_test_graph.process_message(
                "我想了解健身训练", "route_test_1", {"user_id": "test_user"}
            )
            
            if fitness_result.get("processing_info", {}).get("system") == "enhanced":
                score += 5
                details.append("健身消息正确路由到enhanced处理器")
            else:
                details.append("健身消息路由错误")
            
            # 测试一般消息路由
            general_result = await basic_test_graph.process_message(
                "今天天气怎么样", "route_test_2", {"user_id": "test_user"}
            )
            
            if general_result.get("processing_info", {}).get("system") == "state_machine":
                score += 5
                details.append("一般消息正确路由到state_machine处理器")
            else:
                details.append("一般消息路由错误")
                
        except Exception as e:
            details.append(f"路由功能检查异常: {str(e)}")
        
        return score >= 8, score, max_score, details
    
    async def check_parallel_processing(self) -> Tuple[bool, int, int, List[str]]:
        """检查并行处理和结果选择"""
        details = []
        score = 0
        max_score = 10
        
        try:
            # 检查并行处理节点存在
            from app.services.ai_assistant.langgraph.nodes.simple_processor_nodes import simple_hybrid_processor_node
            score += 3
            details.append("并行处理节点存在")
            
            # 检查状态工具支持并行结果
            from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
            test_state = StateUtils.create_initial_state("test", "test", "test")
            StateUtils.add_parallel_result(test_state, {"source": "test", "content": "test"})
            
            if len(test_state["parallel_results"]) > 0:
                score += 4
                details.append("并行结果管理功能正常")
            
            # 检查结果选择逻辑
            from app.services.ai_assistant.langgraph.nodes.simple_processor_nodes import simple_result_selector_node
            score += 3
            details.append("结果选择节点存在")
            
        except Exception as e:
            details.append(f"并行处理检查异常: {str(e)}")
        
        return score >= 8, score, max_score, details
    
    async def check_api_integration(self) -> Tuple[bool, int, int, List[str]]:
        """检查API层无缝集成"""
        details = []
        score = 0
        max_score = 10
        
        try:
            from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
            
            # 测试API集成
            response = await conversation_orchestrator.process_message(
                "测试API集成", "api_test", {"user_id": "test_user"}
            )
            
            if response.get("response_content"):
                score += 4
                details.append("API响应正常")
            
            if "langgraph_metadata" in response:
                score += 3
                details.append("LangGraph元数据正确返回")
            
            if response.get("source_system") == "langgraph":
                score += 3
                details.append("LangGraph处理系统正确标识")
            
        except Exception as e:
            details.append(f"API集成检查异常: {str(e)}")
        
        return score >= 8, score, max_score, details
    
    async def check_error_handling(self) -> Tuple[bool, int, int, List[str]]:
        """检查错误处理和回退机制"""
        details = []
        score = 0
        max_score = 10
        
        try:
            from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
            
            # 测试异常输入处理
            test_cases = ["", "x" * 10000, "<script>alert('xss')</script>"]
            
            for test_input in test_cases:
                try:
                    result = await basic_test_graph.process_message(
                        test_input, f"error_test_{len(test_input)}", {"user_id": "test_user"}
                    )
                    if result is not None:
                        score += 2
                        details.append(f"异常输入处理正常: {test_input[:20]}...")
                except Exception:
                    details.append(f"异常输入处理失败: {test_input[:20]}...")
            
            # 检查错误处理工具
            from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
            test_state = StateUtils.create_initial_state("test", "test", "test")
            StateUtils.set_error(test_state, "测试错误")
            
            if test_state["last_error"] == "测试错误":
                score += 4
                details.append("错误状态管理正常")
            
        except Exception as e:
            details.append(f"错误处理检查异常: {str(e)}")
        
        return score >= 6, score, max_score, details
    
    async def check_performance_requirements(self) -> Tuple[bool, int, int, List[str]]:
        """检查性能要求达标"""
        details = []
        score = 0
        max_score = 10
        
        try:
            from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
            
            # 性能测试
            response_times = []
            for i in range(5):
                start_time = time.time()
                result = await basic_test_graph.process_message(
                    "性能测试消息", f"perf_test_{i}", {"user_id": "test_user"}
                )
                end_time = time.time()
                
                if result.get("success", False):
                    response_time = (end_time - start_time) * 1000
                    response_times.append(response_time)
            
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                max_time = max(response_times)
                
                if avg_time < 5000:  # 5秒
                    score += 5
                    details.append(f"平均响应时间达标: {avg_time:.2f}ms")
                else:
                    details.append(f"平均响应时间超标: {avg_time:.2f}ms")
                
                if max_time < 10000:  # 10秒
                    score += 5
                    details.append(f"最大响应时间达标: {max_time:.2f}ms")
                else:
                    details.append(f"最大响应时间超标: {max_time:.2f}ms")
            
        except Exception as e:
            details.append(f"性能检查异常: {str(e)}")
        
        return score >= 8, score, max_score, details
    
    async def check_test_coverage(self) -> Tuple[bool, int, int, List[str]]:
        """检查测试覆盖率"""
        details = []
        score = 0
        max_score = 10
        
        try:
            # 检查测试文件存在
            test_files = [
                "app/services/ai_assistant/langgraph/test_basic_graph.py",
                "app/services/ai_assistant/langgraph/test_comprehensive.py",
                "app/services/ai_assistant/langgraph/test_api_integration.py"
            ]
            
            for test_file in test_files:
                if (self.project_root / test_file).exists():
                    score += 2
                    details.append(f"测试文件存在: {test_file}")
            
            # 运行综合测试
            from app.services.ai_assistant.langgraph.test_comprehensive import LangGraphTestSuite
            test_suite = LangGraphTestSuite()
            
            # 简化测试以避免超时
            unit_result = await test_suite.run_unit_tests()
            if unit_result:
                score += 2
                details.append("单元测试通过")
            
            performance_result = await test_suite.run_performance_tests()
            if performance_result:
                score += 2
                details.append("性能测试通过")
            
        except Exception as e:
            details.append(f"测试覆盖率检查异常: {str(e)}")
        
        return score >= 8, score, max_score, details
    
    async def check_documentation(self) -> Tuple[bool, int, int, List[str]]:
        """检查文档完整性"""
        details = []
        score = 0
        max_score = 10
        
        # 检查文档文件存在
        doc_files = [
            "docs/agent/implementation_logs/phase2_detailed_implementation.md",
            "docs/agent/LangGraph_API_Documentation.md",
            "docs/agent/项目状态报告.md"
        ]
        
        for doc_file in doc_files:
            if (self.project_root / doc_file).exists():
                score += 3
                details.append(f"文档存在: {doc_file}")
            else:
                details.append(f"文档缺失: {doc_file}")
        
        # 检查状态更新
        status_file = self.project_root / "docs/agent/implementation_status.json"
        if status_file.exists():
            try:
                with open(status_file, 'r') as f:
                    status_data = json.load(f)
                
                phase2_status = status_data.get("phase2", {})
                if phase2_status.get("status") == "completed":
                    score += 1
                    details.append("阶段二状态已更新为完成")
            except Exception:
                details.append("状态文件读取失败")
        
        return score >= 8, score, max_score, details
    
    async def check_configuration(self) -> Tuple[bool, int, int, List[str]]:
        """检查配置管理"""
        details = []
        score = 0
        max_score = 10
        
        try:
            from app.core.unified_config import unified_settings
            
            # 检查配置项
            if unified_settings.ENABLE_UNIFIED_ARCHITECTURE:
                score += 3
                details.append("统一架构已启用")
            
            if unified_settings.UNIFIED_ARCH_PHASE == "phase2":
                score += 3
                details.append("架构阶段设置正确")
            
            if unified_settings.ENABLE_LANGGRAPH:
                score += 4
                details.append("LangGraph已启用")
            
        except Exception as e:
            details.append(f"配置检查异常: {str(e)}")
        
        return score >= 8, score, max_score, details
    
    async def check_production_readiness(self) -> Tuple[bool, int, int, List[str]]:
        """检查生产就绪性"""
        details = []
        score = 0
        max_score = 10
        
        try:
            # 检查错误处理
            from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
            score += 2
            details.append("状态工具可用")
            
            # 检查适配器
            from app.services.ai_assistant.langgraph.adapters.state_adapter import StateAdapter
            score += 2
            details.append("状态适配器可用")
            
            # 检查图可用性
            from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
            if basic_test_graph._initialized or basic_test_graph.initialize():
                score += 3
                details.append("图已初始化，生产就绪")
            
            # 检查API集成
            from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
            score += 3
            details.append("API编排器集成完成")
            
        except Exception as e:
            details.append(f"生产就绪性检查异常: {str(e)}")
        
        return score >= 8, score, max_score, details
    
    def generate_final_report(self) -> Dict[str, Any]:
        """生成最终验收报告"""
        passed_count = sum(1 for result in self.results if result["passed"])
        total_count = len(self.results)
        pass_rate = (passed_count / total_count) * 100 if total_count > 0 else 0
        score_rate = (self.overall_score / self.max_score) * 100 if self.max_score > 0 else 0
        
        overall_passed = pass_rate >= 80 and score_rate >= 80
        
        print("\n" + "=" * 60)
        print("📊 阶段二验收结果总结")
        print("=" * 60)
        
        for result in self.results:
            status = "✅" if result["passed"] else "❌"
            print(f"{status} {result['criterion']}: {result['score']}/{result['max_score']}")
        
        print(f"\n🎯 总体结果:")
        print(f"   通过率: {pass_rate:.1f}% ({passed_count}/{total_count})")
        print(f"   得分率: {score_rate:.1f}% ({self.overall_score}/{self.max_score})")
        print(f"   验收状态: {'🎉 通过' if overall_passed else '❌ 未通过'}")
        
        return {
            "overall_passed": overall_passed,
            "pass_rate": pass_rate,
            "score_rate": score_rate,
            "passed_criteria": passed_count,
            "total_criteria": total_count,
            "overall_score": self.overall_score,
            "max_score": self.max_score,
            "detailed_results": self.results,
            "timestamp": time.time()
        }

async def main():
    """主函数"""
    checker = Phase2AcceptanceChecker()
    results = await checker.run_all_checks()
    
    # 保存结果
    results_file = Path('/home/<USER>/backend/docs/agent/phase2_acceptance_results.json')
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细结果已保存到: {results_file}")
    
    # 返回适当的退出码
    exit(0 if results["overall_passed"] else 1)

if __name__ == "__main__":
    asyncio.run(main())
