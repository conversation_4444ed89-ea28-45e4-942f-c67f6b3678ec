#!/usr/bin/env python3
"""
Gradio测试系统启动脚本

提供便捷的方式启动增强版Gradio测试系统，包含环境检查和依赖验证。
"""

import sys
import os
import subprocess
import time
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_packages = [
        "gradio",
        "httpx",
        "pandas",
        "plotly",
        "fastapi",
        "uvicorn"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True


def check_environment():
    """检查环境"""
    print("🌍 检查环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查项目结构
    required_paths = [
        "app/main.py",
        "tests/comprehensive/interactive/enhanced_gradio_test.py",
        "tests/comprehensive/interactive/gradio_test_app.py"
    ]
    
    for path in required_paths:
        full_path = project_root / path
        if not full_path.exists():
            print(f"❌ 缺少文件: {path}")
            return False
        print(f"✅ {path}")
    
    print("✅ 环境检查通过")
    return True


def activate_virtual_environment():
    """激活虚拟环境（如果存在）"""
    venv_paths = [
        project_root / "venv",
        project_root / ".venv",
        project_root / "env"
    ]
    
    for venv_path in venv_paths:
        if venv_path.exists():
            print(f"🐍 发现虚拟环境: {venv_path}")
            
            # 检查是否已在虚拟环境中
            if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
                print("✅ 已在虚拟环境中")
                return True
            
            # 尝试激活虚拟环境
            if os.name == 'nt':  # Windows
                activate_script = venv_path / "Scripts" / "activate.bat"
            else:  # Unix/Linux/macOS
                activate_script = venv_path / "bin" / "activate"
            
            if activate_script.exists():
                print(f"💡 请手动激活虚拟环境:")
                if os.name == 'nt':
                    print(f"   {activate_script}")
                else:
                    print(f"   source {activate_script}")
                return False
    
    print("ℹ️ 未发现虚拟环境，使用系统Python")
    return True


def start_gradio_test(port=7860, host="0.0.0.0", debug=False):
    """启动Gradio测试系统"""
    print("🚀 启动Gradio测试系统...")
    
    # 构建启动命令
    test_script = project_root / "tests" / "comprehensive" / "interactive" / "enhanced_gradio_test.py"
    
    env = os.environ.copy()
    env["PYTHONPATH"] = str(project_root)
    
    try:
        # 启动测试系统
        print(f"📱 启动地址: http://{host}:{port}")
        print("🔧 功能说明:")
        print("  - 实时对话测试: 与AI助手进行实时对话")
        print("  - 性能监控: 查看响应时间和成功率")
        print("  - 场景测试: 运行预设的测试场景")
        print("  - API集成: 通过HTTP API与生产环境交互")
        print("\n⏳ 正在启动，请稍候...")
        
        # 运行测试脚本
        result = subprocess.run([
            sys.executable, str(test_script)
        ], env=env, cwd=str(project_root))
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在退出...")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Gradio测试系统启动脚本")
    parser.add_argument("--port", type=int, default=7860, help="端口号 (默认: 7860)")
    parser.add_argument("--host", default="0.0.0.0", help="主机地址 (默认: 0.0.0.0)")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--skip-checks", action="store_true", help="跳过环境检查")
    
    args = parser.parse_args()
    
    print("🤖 智能健身AI助手 - Gradio测试系统启动器")
    print("=" * 50)
    
    if not args.skip_checks:
        # 环境检查
        if not check_environment():
            print("❌ 环境检查失败，请修复后重试")
            return 1
        
        # 依赖检查
        if not check_dependencies():
            print("❌ 依赖检查失败，请安装缺失的包")
            return 1
        
        # 虚拟环境检查
        if not activate_virtual_environment():
            print("⚠️ 请激活虚拟环境后重试")
            return 1
    
    # 启动测试系统
    success = start_gradio_test(
        port=args.port,
        host=args.host,
        debug=args.debug
    )
    
    if success:
        print("✅ 测试系统已成功启动")
        return 0
    else:
        print("❌ 测试系统启动失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
