#!/usr/bin/env python3
"""
统一智能架构管理器

用于管理统一智能架构集成项目的实施进度、文档同步和质量控制。
"""

import os
import re
import json
import logging
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class PhaseStatus:
    """阶段状态数据类"""
    phase_name: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    status: str = "not_started"  # not_started, in_progress, completed, blocked
    progress: float = 0.0  # 0.0 - 1.0
    tasks_completed: int = 0
    tasks_total: int = 0
    issues: List[str] = None
    notes: str = ""
    
    def __post_init__(self):
        if self.issues is None:
            self.issues = []

class UnifiedArchitectureManager:
    """统一智能架构管理器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.docs_dir = self.project_root / "docs" / "agent"
        self.status_file = self.docs_dir / "implementation_status.json"
        self.phases = self._initialize_phases()
        
        # 确保目录存在
        self.docs_dir.mkdir(parents=True, exist_ok=True)
        (self.docs_dir / "implementation_logs").mkdir(exist_ok=True)
        
        logger.info("统一智能架构管理器初始化完成")
    
    def _initialize_phases(self) -> Dict[str, PhaseStatus]:
        """初始化项目阶段"""
        return {
            "phase1": PhaseStatus(
                phase_name="传统系统集成基础",
                tasks_total=16,  # 4周 * 4个主要任务
                notes="实现传统意图系统与新版状态机的基础集成"
            ),
            "phase2": PhaseStatus(
                phase_name="LangGraph编排层集成",
                tasks_total=20,  # 5周 * 4个主要任务
                notes="引入LangGraph作为智能编排层"
            ),
            "phase3": PhaseStatus(
                phase_name="智能优化和高级特性",
                tasks_total=16,  # 4周 * 4个主要任务
                notes="实现高级智能特性和系统优化"
            ),
            "phase4": PhaseStatus(
                phase_name="生产优化和文档完善",
                tasks_total=12,  # 3周 * 4个主要任务
                notes="生产环境优化和完整文档体系"
            )
        }
    
    def load_status(self) -> Dict[str, PhaseStatus]:
        """加载项目状态"""
        if self.status_file.exists():
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                phases = {}
                for phase_id, phase_data in data.items():
                    phases[phase_id] = PhaseStatus(**phase_data)
                
                logger.info("项目状态加载成功")
                return phases
                
            except Exception as e:
                logger.error(f"加载项目状态失败: {str(e)}")
                return self.phases
        else:
            logger.info("状态文件不存在，使用默认状态")
            return self.phases
    
    def save_status(self, phases: Dict[str, PhaseStatus]):
        """保存项目状态"""
        try:
            data = {}
            for phase_id, phase in phases.items():
                data[phase_id] = asdict(phase)
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info("项目状态保存成功")
            
        except Exception as e:
            logger.error(f"保存项目状态失败: {str(e)}")
    
    def update_phase_progress(self, phase_id: str, tasks_completed: int, 
                            status: str = None, notes: str = None):
        """更新阶段进度"""
        phases = self.load_status()
        
        if phase_id not in phases:
            logger.error(f"阶段 {phase_id} 不存在")
            return False
        
        phase = phases[phase_id]
        phase.tasks_completed = min(tasks_completed, phase.tasks_total)
        phase.progress = phase.tasks_completed / phase.tasks_total
        
        if status:
            phase.status = status
        
        if notes:
            phase.notes = notes
        
        # 自动设置开始和结束时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if phase.status == "in_progress" and not phase.start_date:
            phase.start_date = current_time
        elif phase.status == "completed" and not phase.end_date:
            phase.end_date = current_time
        
        self.save_status(phases)
        logger.info(f"阶段 {phase_id} 进度更新: {phase.progress:.1%}")
        
        # 更新文档
        self.update_documentation(phase_id, phase)
        
        return True
    
    def add_issue(self, phase_id: str, issue: str):
        """添加问题记录"""
        phases = self.load_status()
        
        if phase_id not in phases:
            logger.error(f"阶段 {phase_id} 不存在")
            return False
        
        phases[phase_id].issues.append(f"{datetime.now().strftime('%Y-%m-%d')}: {issue}")
        self.save_status(phases)
        logger.info(f"为阶段 {phase_id} 添加问题记录")
        
        return True
    
    def generate_status_report(self) -> str:
        """生成状态报告"""
        phases = self.load_status()
        
        report = ["# 统一智能架构集成项目状态报告", ""]
        report.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 总体进度
        total_tasks = sum(phase.tasks_total for phase in phases.values())
        completed_tasks = sum(phase.tasks_completed for phase in phases.values())
        overall_progress = completed_tasks / total_tasks if total_tasks > 0 else 0
        
        report.append("## 总体进度")
        report.append(f"- **整体完成度**: {overall_progress:.1%} ({completed_tasks}/{total_tasks})")
        report.append("")
        
        # 各阶段详情
        report.append("## 各阶段详情")
        report.append("")
        
        for phase_id, phase in phases.items():
            status_emoji = {
                "not_started": "⏳",
                "in_progress": "🔄", 
                "completed": "✅",
                "blocked": "🚫"
            }.get(phase.status, "❓")
            
            report.append(f"### {phase.phase_name} ({phase_id})")
            report.append(f"- **状态**: {status_emoji} {phase.status}")
            report.append(f"- **进度**: {phase.progress:.1%} ({phase.tasks_completed}/{phase.tasks_total})")
            
            if phase.start_date:
                report.append(f"- **开始时间**: {phase.start_date}")
            if phase.end_date:
                report.append(f"- **结束时间**: {phase.end_date}")
            
            if phase.notes:
                report.append(f"- **备注**: {phase.notes}")
            
            if phase.issues:
                report.append("- **问题记录**:")
                for issue in phase.issues[-3:]:  # 只显示最近3个问题
                    report.append(f"  - {issue}")
            
            report.append("")
        
        return "\n".join(report)
    
    def update_documentation(self, phase_id: str, phase: PhaseStatus):
        """更新文档"""
        try:
            # 更新主文档
            self._update_main_documentation(phase_id, phase)
            
            # 生成实施日志
            self._generate_implementation_log(phase_id, phase)
            
            # 更新状态报告
            self._update_status_report()
            
            logger.info(f"文档更新完成: {phase_id}")
            
        except Exception as e:
            logger.error(f"文档更新失败: {str(e)}")
    
    def _update_main_documentation(self, phase_id: str, phase: PhaseStatus):
        """更新主文档"""
        main_doc_path = self.docs_dir / "智能健身AI助手系统.md"
        
        if not main_doc_path.exists():
            logger.warning("主文档不存在，跳过更新")
            return
        
        # 读取文档内容
        with open(main_doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新状态标记
        status_emoji = {
            "not_started": "⏳",
            "in_progress": "🔄",
            "completed": "✅", 
            "blocked": "🚫"
        }.get(phase.status, "❓")
        
        pattern = f"- \\*\\*{re.escape(phase.phase_name)}\\*\\*:.*"
        replacement = f"- **{phase.phase_name}**: {status_emoji} {phase.status} ({phase.progress:.1%})"
        
        content = re.sub(pattern, replacement, content)
        
        # 添加更新时间戳
        timestamp_pattern = r"<!-- 最后更新: .* -->"
        new_timestamp = f"<!-- 最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} -->"
        
        if re.search(timestamp_pattern, content):
            content = re.sub(timestamp_pattern, new_timestamp, content)
        else:
            content += f"\n\n{new_timestamp}"
        
        # 写回文件
        with open(main_doc_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_implementation_log(self, phase_id: str, phase: PhaseStatus):
        """生成实施日志"""
        log_file = self.docs_dir / "implementation_logs" / f"{phase_id}_implementation.md"
        
        log_content = f"""# {phase.phase_name} 实施日志

## 基本信息
- **阶段ID**: {phase_id}
- **阶段名称**: {phase.phase_name}
- **当前状态**: {phase.status}
- **完成进度**: {phase.progress:.1%} ({phase.tasks_completed}/{phase.tasks_total})

## 时间线
- **开始时间**: {phase.start_date or '未开始'}
- **结束时间**: {phase.end_date or '进行中'}

## 实施内容
{phase.notes}

## 问题记录
"""
        
        if phase.issues:
            for issue in phase.issues:
                log_content += f"- {issue}\n"
        else:
            log_content += "暂无问题记录\n"
        
        log_content += f"""
## 下一步计划
根据项目时间表继续推进后续任务。

---
*最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(log_content)
    
    def _update_status_report(self):
        """更新状态报告"""
        report_content = self.generate_status_report()
        report_file = self.docs_dir / "项目状态报告.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
    
    def check_documentation_consistency(self) -> bool:
        """检查文档一致性"""
        logger.info("开始文档一致性检查...")
        
        issues = []
        
        # 检查主要文档是否存在
        required_docs = [
            "智能健身AI助手系统.md",
            "统一智能架构集成方案.md",
            "统一架构实施时间表和项目管理.md"
        ]
        
        for doc in required_docs:
            doc_path = self.docs_dir / doc
            if not doc_path.exists():
                issues.append(f"缺少必要文档: {doc}")
        
        # 检查实施日志
        phases = self.load_status()
        for phase_id in phases.keys():
            log_file = self.docs_dir / "implementation_logs" / f"{phase_id}_implementation.md"
            if not log_file.exists():
                issues.append(f"缺少实施日志: {phase_id}")
        
        if issues:
            logger.warning(f"发现文档一致性问题: {issues}")
            return False
        else:
            logger.info("文档一致性检查通过")
            return True
    
    async def run_health_check(self) -> Dict[str, Any]:
        """运行健康检查"""
        logger.info("开始系统健康检查...")
        
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "checks": {}
        }
        
        # 检查项目状态
        phases = self.load_status()
        active_phases = [p for p in phases.values() if p.status == "in_progress"]
        
        health_status["checks"]["project_status"] = {
            "status": "ok" if len(active_phases) <= 2 else "warning",
            "message": f"当前有 {len(active_phases)} 个阶段在进行中",
            "active_phases": [p.phase_name for p in active_phases]
        }
        
        # 检查文档一致性
        doc_consistency = self.check_documentation_consistency()
        health_status["checks"]["documentation"] = {
            "status": "ok" if doc_consistency else "error",
            "message": "文档一致性检查" + ("通过" if doc_consistency else "失败")
        }
        
        # 检查配置文件
        config_file = self.project_root / "app" / "core" / "config.py"
        health_status["checks"]["configuration"] = {
            "status": "ok" if config_file.exists() else "error",
            "message": "配置文件" + ("存在" if config_file.exists() else "缺失")
        }
        
        # 确定总体状态
        check_statuses = [check["status"] for check in health_status["checks"].values()]
        if "error" in check_statuses:
            health_status["overall_status"] = "unhealthy"
        elif "warning" in check_statuses:
            health_status["overall_status"] = "degraded"
        
        logger.info(f"健康检查完成，状态: {health_status['overall_status']}")
        return health_status

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="统一智能架构管理器")
    parser.add_argument("--project-root", default=".", help="项目根目录")
    parser.add_argument("--action", required=True, 
                       choices=["status", "update", "report", "health", "init"],
                       help="执行的操作")
    parser.add_argument("--phase", help="阶段ID (phase1, phase2, phase3, phase4)")
    parser.add_argument("--tasks", type=int, help="已完成任务数")
    parser.add_argument("--status", help="阶段状态")
    parser.add_argument("--notes", help="备注信息")
    parser.add_argument("--issue", help="添加问题记录")
    
    args = parser.parse_args()
    
    manager = UnifiedArchitectureManager(args.project_root)
    
    if args.action == "init":
        # 初始化项目
        manager.save_status(manager.phases)
        print("项目初始化完成")
        
    elif args.action == "status":
        # 显示状态
        phases = manager.load_status()
        for phase_id, phase in phases.items():
            print(f"{phase_id}: {phase.phase_name} - {phase.status} ({phase.progress:.1%})")
    
    elif args.action == "update":
        # 更新进度
        if not args.phase or args.tasks is None:
            print("更新进度需要指定 --phase 和 --tasks")
            return
        
        success = manager.update_phase_progress(
            args.phase, args.tasks, args.status, args.notes
        )
        print("进度更新" + ("成功" if success else "失败"))
    
    elif args.action == "report":
        # 生成报告
        report = manager.generate_status_report()
        print(report)
    
    elif args.action == "health":
        # 健康检查
        health = asyncio.run(manager.run_health_check())
        print(json.dumps(health, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
