#!/usr/bin/env python3
"""
Simple standalone test for sets validation logic
"""

from typing import Union, List, Dict, Any
from pydantic import BaseModel, validator

class TestTrainingTemplateExercise(BaseModel):
    """测试用的训练模板动作模型"""
    exercise_id: int
    sets: Union[int, List[Dict[str, Any]], Dict[str, Any]] = 3
    reps: Union[str, int] = "10"

    @validator('sets', pre=True)
    def validate_sets(cls, v):
        """验证和标准化sets数据"""
        if isinstance(v, list):
            # 如果是列表，返回列表长度
            return len(v)
        elif isinstance(v, dict):
            # 如果是字典，返回默认值
            return 3
        elif isinstance(v, (int, float)):
            # 如果是数字，直接返回整数
            return int(v)
        elif isinstance(v, str) and v.isdigit():
            # 如果是数字字符串，转换为整数
            return int(v)
        else:
            # 其他情况返回默认值
            return 3

    @validator('reps', pre=True)
    def validate_reps(cls, v):
        """验证和标准化reps数据"""
        if isinstance(v, (int, float)):
            return str(int(v))
        return str(v)


def test_validation():
    """测试验证逻辑"""
    
    # 测试1：复杂sets数据（列表形式）
    print("测试1：复杂sets数据（列表形式）")
    try:
        exercise1 = TestTrainingTemplateExercise(
            exercise_id=15,
            sets=[
                {"type": "normal", "weight": 20, "reps": 12},
                {"type": "normal", "weight": 20, "reps": 12},
                {"type": "normal", "weight": 20, "reps": 12}
            ],
            reps="10-12"
        )
        print(f"✅ 成功处理：sets={exercise1.sets}, reps={exercise1.reps}")
    except Exception as e:
        print(f"❌ 失败：{e}")
    
    # 测试2：字典sets数据
    print("\n测试2：字典sets数据")
    try:
        exercise2 = TestTrainingTemplateExercise(
            exercise_id=25,
            sets={"weight": 20, "reps": 12},
            reps=8
        )
        print(f"✅ 成功处理：sets={exercise2.sets}, reps={exercise2.reps}")
    except Exception as e:
        print(f"❌ 失败：{e}")
    
    # 测试3：整数sets数据
    print("\n测试3：整数sets数据")
    try:
        exercise3 = TestTrainingTemplateExercise(
            exercise_id=30,
            sets=4,
            reps=10
        )
        print(f"✅ 成功处理：sets={exercise3.sets}, reps={exercise3.reps}")
    except Exception as e:
        print(f"❌ 失败：{e}")
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    test_validation() 