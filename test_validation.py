#!/usr/bin/env python3
"""
Test script for Pydantic validation of training template data
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/backend')

from app.schemas.training_plan import TrainingTemplateExerciseCreate, TrainingTemplateCreate

def test_schema_validation():
    """测试Pydantic schema对复杂sets数据的验证"""
    
    print("测试1：复杂sets数据（列表形式）")
    complex_sets_data = {
        "exercise_id": 15,
        "sets": [
            {"type": "normal", "weight": 20, "reps": 12},
            {"type": "normal", "weight": 20, "reps": 12},
            {"type": "normal", "weight": 20, "reps": 12}
        ],
        "reps": "10-12",
        "rest_seconds": 60,
        "exercise_type": "weight_reps"
    }
    
    try:
        exercise = TrainingTemplateExerciseCreate(**complex_sets_data)
        print(f"✅ 成功处理复杂sets数据，sets值: {exercise.sets}")
        print(f"   其他字段: exercise_id={exercise.exercise_id}, reps={exercise.reps}")
    except Exception as e:
        print(f"❌ 失败: {str(e)}")
    
    print("\n测试2：简单sets数据（整数）")
    simple_sets_data = {
        "exercise_id": 25,
        "sets": 4,
        "reps": 8,
        "rest_seconds": 90,
        "exercise_type": "weight_reps"
    }
    
    try:
        exercise = TrainingTemplateExerciseCreate(**simple_sets_data)
        print(f"✅ 成功处理简单sets数据，sets值: {exercise.sets}")
        print(f"   其他字段: exercise_id={exercise.exercise_id}, reps={exercise.reps}")
    except Exception as e:
        print(f"❌ 失败: {str(e)}")
    
    print("\n测试3：完整训练模板数据")
    template_data = {
        "name": "测试训练模板",
        "description": "这是一个测试模板",
        "estimated_duration": 60,
        "exercises": [
            {
                "exercise_id": 15,
                "sets": [
                    {"type": "normal", "weight": 20, "reps": 12},
                    {"type": "normal", "weight": 20, "reps": 12},
                    {"type": "normal", "weight": 20, "reps": 12}
                ],
                "reps": "10-12",
                "rest_seconds": 60,
                "exercise_type": "weight_reps"
            },
            {
                "exercise_id": 25,
                "sets": 4,
                "reps": 8,
                "rest_seconds": 90,
                "exercise_type": "weight_reps"
            }
        ]
    }
    
    try:
        template = TrainingTemplateCreate(**template_data)
        print(f"✅ 成功创建训练模板: {template.name}")
        print(f"   包含 {len(template.exercises)} 个动作")
        for i, ex in enumerate(template.exercises):
            print(f"   动作{i+1}: exercise_id={ex.exercise_id}, sets={ex.sets}, reps={ex.reps}")
    except Exception as e:
        print(f"❌ 失败: {str(e)}")
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    test_schema_validation() 