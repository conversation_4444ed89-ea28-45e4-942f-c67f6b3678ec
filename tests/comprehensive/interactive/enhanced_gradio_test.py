#!/usr/bin/env python3
"""
增强版Gradio测试系统 - 实时对话测试界面

基于现有的Gradio测试应用，创建完整的实时对话测试界面，
通过API接口与主系统交互，确保与生产环境保持一致。
"""

import gradio as gr
import asyncio
import json
import time
import pandas as pd
import sys
import os
import uuid
import httpx
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
import plotly.graph_objects as go
import plotly.express as px

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

from tests.comprehensive.interactive.gradio_test_app import GradioTestApp, ChatAPIClient, APIServerManager
from tests.comprehensive.interactive.test_config import (
    TestScenarios, PerformanceThresholds, TestEnvironment,
    default_api_config, default_test_config, default_ui_config
)


class EnhancedGradioTestSystem:
    """增强版Gradio测试系统"""

    def __init__(self):
        # 使用配置文件
        self.api_config = default_api_config
        self.test_config = default_test_config
        self.ui_config = default_ui_config

        # 初始化组件
        self.api_client = ChatAPIClient(base_url=self.api_config.base_url)
        self.api_server = APIServerManager()

        # 数据存储
        self.conversation_history = []
        self.performance_metrics = []
        self.test_sessions = {}
        self.current_session_id = None
        self.test_user_id = self.test_config.test_user_id

        # 测试场景（从配置文件加载）
        self.test_scenarios = TestScenarios.get_all_scenarios()
        self.scenario_categories = TestScenarios.get_scenario_categories()

        # 环境信息
        self.environment_info = TestEnvironment.get_environment_info()
        self.environment_checks = TestEnvironment.validate_environment()

        print("🚀 初始化增强版Gradio测试系统...")
        print(f"📊 加载了 {len(self.test_scenarios)} 个测试场景")
        print(f"🔧 测试用户ID: {self.test_user_id}")
        print(f"🌐 API地址: {self.api_config.base_url}")

    async def initialize_system(self) -> Tuple[bool, str]:
        """初始化系统"""
        try:
            # 检查API服务器状态
            if not self.api_server.is_server_running():
                print("🚀 启动API服务器...")
                if not self.api_server.start_server():
                    return False, "API服务器启动失败"

                # 等待服务器启动
                await asyncio.sleep(5)

            # 验证API连接
            health_result = await self.api_client.check_health()
            if health_result["status"] != "healthy":
                return False, f"API服务器健康检查失败: {health_result}"

            print("✅ 系统初始化完成")
            return True, "系统初始化成功"

        except Exception as e:
            return False, f"系统初始化失败: {str(e)}"

    def generate_session_id(self) -> str:
        """生成新的会话ID"""
        session_id = f"enhanced_test_{uuid.uuid4().hex[:8]}"
        self.current_session_id = session_id
        self.test_sessions[session_id] = {
            "created_at": datetime.now(),
            "messages": [],
            "metrics": []
        }
        return session_id

    async def send_message(self, message: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """发送消息到API"""
        if not session_id:
            session_id = self.current_session_id or self.generate_session_id()

        start_time = time.time()

        try:
            result = await self.api_client.send_message(
                message=message,
                session_id=session_id,
                user_id=self.test_user_id
            )

            response_time = time.time() - start_time

            # 提取响应内容 - 修复：确保获取真实的AI响应内容
            if result["success"]:
                api_data = result.get("data", {})
                # 优先获取response字段，然后是response_content字段
                ai_response = (
                    api_data.get("response") or
                    api_data.get("response_content") or
                    api_data.get("content") or
                    "无响应内容"
                )

                # 如果响应内容是状态机处理信息，尝试从meta_info中获取真实响应
                if "状态机处理" in ai_response or "处理完成" in ai_response:
                    meta_info = api_data.get("meta_info", {})
                    if isinstance(meta_info, dict):
                        # 查找真实的AI响应内容
                        real_response = (
                            meta_info.get("response_content") or
                            meta_info.get("ai_response") or
                            meta_info.get("content")
                        )
                        if real_response and real_response != ai_response:
                            ai_response = real_response
                            print(f"🔧 从meta_info中提取到真实响应: {real_response[:100]}...")

                intent = api_data.get("intent_type") or api_data.get("intent", "unknown")
                confidence = api_data.get("confidence", 0)
            else:
                ai_response = f"API错误: {result.get('error', '未知错误')}"
                intent = "error"
                confidence = 0

            # 记录对话历史
            message_record = {
                "timestamp": datetime.now(),
                "session_id": session_id,
                "user_message": message,
                "ai_response": ai_response,
                "success": result["success"],
                "response_time": response_time,
                "status_code": result.get("status_code", 500),
                "intent": intent,
                "confidence": confidence,
                "raw_api_data": result.get("data", {})  # 保存原始API数据用于调试
            }

            self.conversation_history.append(message_record)

            # 记录到会话
            if session_id in self.test_sessions:
                self.test_sessions[session_id]["messages"].append(message_record)
                self.test_sessions[session_id]["metrics"].append({
                    "response_time": response_time,
                    "success": result["success"],
                    "timestamp": datetime.now()
                })

            # 记录性能指标
            self.performance_metrics.append({
                "timestamp": datetime.now(),
                "response_time": response_time,
                "success": result["success"],
                "intent": intent,
                "confidence": confidence
            })

            return result

        except Exception as e:
            error_record = {
                "timestamp": datetime.now(),
                "session_id": session_id,
                "user_message": message,
                "ai_response": f"系统错误: {str(e)}",
                "success": False,
                "response_time": time.time() - start_time,
                "status_code": 500,
                "intent": "error",
                "confidence": 0
            }

            self.conversation_history.append(error_record)

            return {
                "success": False,
                "error": str(e),
                "status_code": 500
            }

    def get_conversation_display(self) -> List[Tuple[str, str]]:
        """获取对话显示格式"""
        display_history = []
        for record in self.conversation_history[-10:]:  # 显示最近10条
            user_msg = record["user_message"]
            ai_msg = record["ai_response"]

            # 添加状态信息
            status_info = f"[{record['intent']}|{record['confidence']:.2f}|{record['response_time']:.2f}s]"
            if not record["success"]:
                status_info = f"[ERROR|{record['status_code']}]"

            display_history.append((user_msg, f"{ai_msg}\n\n{status_info}"))

        return display_history

    def get_performance_chart(self):
        """生成性能图表"""
        if not self.performance_metrics:
            return None

        df = pd.DataFrame(self.performance_metrics)
        df['timestamp_str'] = df['timestamp'].dt.strftime('%H:%M:%S')

        # 响应时间图表
        fig = go.Figure()

        # 成功请求
        success_data = df[df['success'] == True]
        if not success_data.empty:
            fig.add_trace(go.Scatter(
                x=success_data['timestamp_str'],
                y=success_data['response_time'],
                mode='lines+markers',
                name='成功请求',
                line=dict(color='green'),
                marker=dict(size=6)
            ))

        # 失败请求
        failed_data = df[df['success'] == False]
        if not failed_data.empty:
            fig.add_trace(go.Scatter(
                x=failed_data['timestamp_str'],
                y=failed_data['response_time'],
                mode='markers',
                name='失败请求',
                marker=dict(color='red', size=8, symbol='x')
            ))

        fig.update_layout(
            title='API响应时间趋势',
            xaxis_title='时间',
            yaxis_title='响应时间 (秒)',
            height=400
        )

        return fig

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.performance_metrics:
            return {
                "总请求数": 0,
                "成功率": "0% (无数据)",
                "平均响应时间": "0.00s (无数据)",
                "最快响应": "0.00s (无数据)",
                "最慢响应": "0.00s (无数据)",
                "性能等级": "无数据"
            }

        df = pd.DataFrame(self.performance_metrics)
        total_requests = len(df)
        success_count = len(df[df['success'] == True])
        success_rate = (success_count / total_requests * 100) if total_requests > 0 else 0

        response_times = df['response_time']
        avg_response_time = response_times.mean()
        min_response_time = response_times.min()
        max_response_time = response_times.max()

        # 使用性能阈值评估
        response_time_level = PerformanceThresholds.get_response_time_level(avg_response_time)
        success_rate_level = PerformanceThresholds.get_success_rate_level(success_rate)

        # 计算平均置信度
        confidence_data = df[df['success'] == True]['confidence']
        avg_confidence = confidence_data.mean() if not confidence_data.empty else 0
        confidence_level = PerformanceThresholds.get_confidence_level(avg_confidence)

        return {
            "总请求数": total_requests,
            "成功率": f"{success_rate:.1f}% ({success_rate_level})",
            "平均响应时间": f"{avg_response_time:.2f}s ({response_time_level})",
            "最快响应": f"{min_response_time:.2f}s",
            "最慢响应": f"{max_response_time:.2f}s",
            "平均置信度": f"{avg_confidence:.2f} ({confidence_level})",
            "性能等级": f"响应时间: {response_time_level}, 成功率: {success_rate_level}"
        }

    def get_environment_status(self) -> str:
        """获取环境状态"""
        status_lines = ["🌍 环境状态:"]

        # 基本信息
        status_lines.append(f"Python版本: {self.environment_info['python_version']}")
        status_lines.append(f"平台: {self.environment_info['platform']}")

        # 环境检查
        checks = self.environment_checks
        status_lines.append(f"Python版本检查: {'✅' if checks['python_version_ok'] else '❌'}")
        status_lines.append(f"API密钥配置: {'✅' if checks['has_api_key'] else '❌'}")
        status_lines.append(f"真实模型启用: {'✅' if checks['real_models_enabled'] else '❌'}")

        # API服务器状态
        server_status = "运行中" if self.api_server.is_server_running() else "未运行"
        status_lines.append(f"API服务器: {server_status}")

        return "\n".join(status_lines)

    async def run_scenario_test(self, scenario_name: str, progress_callback=None) -> str:
        """运行场景测试"""
        if scenario_name not in self.test_scenarios:
            return f"❌ 未找到测试场景: {scenario_name}"

        messages = self.test_scenarios[scenario_name]
        session_id = self.generate_session_id()
        results = []

        for i, message in enumerate(messages):
            if progress_callback:
                progress_callback((i + 1) / len(messages), f"测试消息 {i + 1}/{len(messages)}")

            result = await self.send_message(message, session_id)

            if result["success"]:
                results.append(f"✅ {message} -> 成功")
            else:
                results.append(f"❌ {message} -> 失败: {result.get('error', '未知错误')}")

            # 短暂延迟
            await asyncio.sleep(1)

        return f"📊 场景测试完成: {scenario_name}\n" + "\n".join(results)


# 全局测试系统实例
test_system = EnhancedGradioTestSystem()


def create_gradio_interface():
    """创建Gradio界面"""

    async def initialize_system():
        """初始化系统"""
        success, message = await test_system.initialize_system()
        return f"{'✅' if success else '❌'} {message}"

    async def chat_interface(message, history):
        """聊天界面处理函数"""
        if not message.strip():
            return history, ""

        # 发送消息
        result = await test_system.send_message(message)

        # 更新历史 - 修复：确保显示真实的AI响应内容
        if result["success"]:
            api_data = result.get("data", {})

            # 提取真实的AI响应内容
            ai_response = (
                api_data.get("response") or
                api_data.get("response_content") or
                api_data.get("content") or
                "无响应内容"
            )

            # 如果响应是状态机处理信息，尝试获取真实内容
            if "状态机处理" in ai_response or "处理完成" in ai_response:
                meta_info = api_data.get("meta_info", {})
                if isinstance(meta_info, dict):
                    real_response = (
                        meta_info.get("response_content") or
                        meta_info.get("ai_response") or
                        meta_info.get("content")
                    )
                    if real_response and real_response != ai_response:
                        ai_response = real_response
                        print(f"🔧 聊天界面从meta_info中提取到真实响应: {real_response[:100]}...")

            # 如果仍然是状态机信息，显示调试信息
            if "状态机处理" in ai_response or "处理完成" in ai_response:
                print(f"⚠️ 仍然收到状态机处理信息，API数据: {api_data}")
                ai_response = f"⚠️ 系统返回了状态机处理信息而非AI回复。原始响应: {ai_response}\n\n请检查系统配置，确保AI模型正常工作。"

            intent = api_data.get("intent_type") or api_data.get("intent", "unknown")
            confidence = api_data.get("confidence", 0)
            response_time = result.get("response_time", 0)

            status_info = f"\n\n[意图: {intent} | 置信度: {confidence:.2f} | 响应时间: {response_time:.2f}s]"
            history.append([message, ai_response + status_info])
        else:
            error_msg = f"❌ 错误: {result.get('error', '未知错误')}"
            history.append([message, error_msg])

        return history, ""

    def get_performance_chart():
        """获取性能图表"""
        return test_system.get_performance_chart()

    def get_statistics():
        """获取统计信息"""
        stats = test_system.get_statistics()
        return "\n".join([f"{k}: {v}" for k, v in stats.items()])

    async def run_scenario(scenario_name, progress=gr.Progress()):
        """运行场景测试"""
        def progress_callback(value, desc):
            progress(value, desc=desc)

        result = await test_system.run_scenario_test(scenario_name, progress_callback)
        return result

    async def run_all_scenarios(progress=gr.Progress()):
        """运行所有场景测试"""
        all_scenarios = list(test_system.test_scenarios.keys())
        results = []

        for i, scenario_name in enumerate(all_scenarios):
            progress((i + 1) / len(all_scenarios), f"运行场景: {scenario_name}")
            result = await test_system.run_scenario_test(scenario_name)
            results.append(f"📋 {scenario_name}:\n{result}\n")
            await asyncio.sleep(0.5)  # 短暂延迟

        return "\n" + "="*50 + "\n".join(results)

    def generate_new_session():
        """生成新会话"""
        session_id = test_system.generate_session_id()
        return f"✅ 新会话已创建: {session_id}"

    def get_environment_status():
        """获取环境状态"""
        return test_system.get_environment_status()

    def update_scenario_choices(category):
        """根据分类更新场景选择"""
        if category and category in test_system.scenario_categories:
            scenarios = list(test_system.scenario_categories[category].keys())
            return gr.Dropdown.update(choices=scenarios, value=scenarios[0] if scenarios else None)
        return gr.Dropdown.update(choices=list(test_system.test_scenarios.keys()))

    async def quick_health_check():
        """快速健康检查"""
        health_result = await test_system.api_client.check_health()
        if health_result["status"] == "healthy":
            return f"✅ 健康检查通过\n响应时间: {health_result['response_time']:.2f}s"
        else:
            return f"❌ 健康检查失败\n错误: {health_result.get('error', '未知错误')}"

    async def quick_basic_test():
        """快速基础测试"""
        test_message = "你好，我想了解健身"
        result = await test_system.send_message(test_message)
        if result["success"]:
            return f"✅ 基础对话测试通过\n消息: {test_message}\n响应: {result['data'].get('response', '无响应')[:100]}..."
        else:
            return f"❌ 基础对话测试失败\n错误: {result.get('error', '未知错误')}"

    async def quick_stress_test():
        """快速压力测试"""
        stress_messages = ["测试1", "测试2", "测试3", "测试4", "测试5"]
        success_count = 0
        total_time = 0

        for msg in stress_messages:
            result = await test_system.send_message(msg)
            if result["success"]:
                success_count += 1
            total_time += result.get("response_time", 0)

        success_rate = (success_count / len(stress_messages)) * 100
        avg_time = total_time / len(stress_messages)

        return f"🚀 压力测试完成\n成功率: {success_rate:.1f}%\n平均响应时间: {avg_time:.2f}s"

    async def debug_api_response():
        """调试API响应 - 专门用于诊断响应内容问题"""
        test_message = "胸肌怎么练"
        print(f"🔍 调试API响应，测试消息: {test_message}")

        result = await test_system.send_message(test_message)

        debug_info = []
        debug_info.append(f"🔍 API响应调试信息")
        debug_info.append(f"测试消息: {test_message}")
        debug_info.append(f"API调用成功: {result['success']}")

        if result["success"]:
            api_data = result.get("data", {})
            debug_info.append(f"API数据键: {list(api_data.keys())}")

            # 检查所有可能的响应字段
            response_fields = ["response", "response_content", "content"]
            for field in response_fields:
                value = api_data.get(field)
                if value:
                    debug_info.append(f"{field}: {value[:200]}...")

            # 检查meta_info
            meta_info = api_data.get("meta_info", {})
            if isinstance(meta_info, dict):
                debug_info.append(f"meta_info键: {list(meta_info.keys())}")
                for key, value in meta_info.items():
                    if isinstance(value, str) and len(value) > 10:
                        debug_info.append(f"meta_info.{key}: {str(value)[:200]}...")

            # 检查意图和置信度
            debug_info.append(f"意图: {api_data.get('intent_type', 'unknown')}")
            debug_info.append(f"置信度: {api_data.get('confidence', 0)}")

            # 检查当前状态
            debug_info.append(f"当前状态: {api_data.get('current_state', 'unknown')}")

        else:
            debug_info.append(f"API错误: {result.get('error', '未知错误')}")

        return "\n".join(debug_info)

    # 创建Gradio界面
    with gr.Blocks(title="智能健身AI助手 - 增强测试系统", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🤖 智能健身AI助手 - 增强测试系统")
        gr.Markdown("通过API接口与生产环境进行实时对话测试，确保系统功能正常运行。")

        with gr.Tab("💬 实时对话测试"):
            with gr.Row():
                with gr.Column(scale=3):
                    chatbot = gr.Chatbot(
                        label="对话历史",
                        height=500,
                        show_label=True,
                        container=True
                    )

                    with gr.Row():
                        msg_input = gr.Textbox(
                            label="输入消息",
                            placeholder="请输入您的健身相关问题...",
                            scale=4
                        )
                        send_btn = gr.Button("发送", variant="primary", scale=1)
                        clear_btn = gr.Button("清空", scale=1)

                with gr.Column(scale=1):
                    init_btn = gr.Button("🚀 初始化系统", variant="secondary")
                    init_status = gr.Textbox(label="初始化状态", interactive=False)

                    new_session_btn = gr.Button("🆕 新建会话")
                    session_status = gr.Textbox(label="会话状态", interactive=False)

                    stats_display = gr.Textbox(
                        label="实时统计",
                        lines=6,
                        interactive=False
                    )

                    refresh_stats_btn = gr.Button("🔄 刷新统计")

        with gr.Tab("📊 性能监控"):
            with gr.Row():
                performance_chart = gr.Plot(label="响应时间趋势")
                refresh_chart_btn = gr.Button("🔄 刷新图表")

        with gr.Tab("🎯 场景测试"):
            with gr.Row():
                with gr.Column(scale=2):
                    # 场景分类选择
                    category_dropdown = gr.Dropdown(
                        choices=list(test_system.scenario_categories.keys()),
                        label="测试场景分类",
                        value=list(test_system.scenario_categories.keys())[0] if test_system.scenario_categories else None
                    )

                    scenario_dropdown = gr.Dropdown(
                        choices=list(test_system.test_scenarios.keys()),
                        label="选择测试场景",
                        value=list(test_system.test_scenarios.keys())[0] if test_system.test_scenarios else None
                    )

                    with gr.Row():
                        run_scenario_btn = gr.Button("▶️ 运行场景测试", variant="primary", scale=2)
                        run_all_btn = gr.Button("🚀 运行所有场景", variant="secondary", scale=1)

                    scenario_result = gr.Textbox(
                        label="测试结果",
                        lines=12,
                        interactive=False
                    )

                with gr.Column(scale=1):
                    env_status = gr.Textbox(
                        label="环境状态",
                        lines=8,
                        interactive=False
                    )

                    refresh_env_btn = gr.Button("🔄 刷新环境状态")

                    # 快速测试按钮
                    gr.Markdown("### 🚀 快速测试")
                    quick_health_btn = gr.Button("健康检查", size="sm")
                    quick_basic_btn = gr.Button("基础对话", size="sm")
                    quick_stress_btn = gr.Button("压力测试", size="sm")
                    debug_api_btn = gr.Button("🔍 调试API响应", size="sm", variant="secondary")

        # 事件绑定
        init_btn.click(initialize_system, outputs=init_status)
        new_session_btn.click(generate_new_session, outputs=session_status)

        send_btn.click(
            chat_interface,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, msg_input]
        )

        msg_input.submit(
            chat_interface,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, msg_input]
        )

        clear_btn.click(lambda: ([], ""), outputs=[chatbot, msg_input])

        refresh_stats_btn.click(get_statistics, outputs=stats_display)
        refresh_chart_btn.click(get_performance_chart, outputs=performance_chart)

        run_scenario_btn.click(
            run_scenario,
            inputs=scenario_dropdown,
            outputs=scenario_result
        )

        run_all_btn.click(
            run_all_scenarios,
            outputs=scenario_result
        )

        # 分类选择事件
        category_dropdown.change(
            update_scenario_choices,
            inputs=category_dropdown,
            outputs=scenario_dropdown
        )

        # 环境状态事件
        refresh_env_btn.click(get_environment_status, outputs=env_status)

        # 快速测试事件
        quick_health_btn.click(quick_health_check, outputs=scenario_result)
        quick_basic_btn.click(quick_basic_test, outputs=scenario_result)
        quick_stress_btn.click(quick_stress_test, outputs=scenario_result)
        debug_api_btn.click(debug_api_response, outputs=scenario_result)

        # 定期刷新统计信息
        demo.load(get_statistics, outputs=stats_display)
        demo.load(get_environment_status, outputs=env_status)

    return demo


if __name__ == "__main__":
    # 创建并启动Gradio应用
    demo = create_gradio_interface()

    print("🚀 启动增强版Gradio测试系统...")
    print("📱 访问地址: http://localhost:7860")
    print("🔧 使用说明:")
    print("  1. 点击'初始化系统'启动API服务器")
    print("  2. 在'实时对话测试'标签页进行对话测试")
    print("  3. 在'性能监控'标签页查看性能指标")
    print("  4. 在'场景测试'标签页运行预设测试场景")

    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=False,
        show_error=True
    )
