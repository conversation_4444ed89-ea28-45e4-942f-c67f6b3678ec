#!/usr/bin/env python3
"""
最终验证脚本 - 确认Gradio修复完成
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

async def test_gradio_chat_interface():
    """测试Gradio聊天界面"""
    print("🔍 测试Gradio聊天界面...")
    
    try:
        from tests.comprehensive.interactive.enhanced_gradio_test import EnhancedGradioTestSystem
        
        # 创建测试系统
        test_system = EnhancedGradioTestSystem()
        
        # 测试消息处理
        test_cases = [
            "胸肌怎么练",
            "我想了解健身",
            "你好",
            "如何制定训练计划"
        ]
        
        print("📝 测试消息处理...")
        
        for i, message in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {message}")
            
            # 模拟聊天界面的消息处理
            history = []
            
            # 发送消息
            result = await test_system.send_message(message)
            
            # 模拟chat_interface函数的处理逻辑
            if result["success"]:
                api_data = result.get("data", {})
                
                # 提取真实的AI响应内容
                ai_response = (
                    api_data.get("response") or 
                    api_data.get("response_content") or 
                    api_data.get("content") or
                    "无响应内容"
                )
                
                # 检查是否仍然是状态机处理信息
                if "状态机处理" in ai_response or "处理完成" in ai_response:
                    meta_info = api_data.get("meta_info", {})
                    if isinstance(meta_info, dict):
                        real_response = (
                            meta_info.get("response_content") or
                            meta_info.get("ai_response") or
                            meta_info.get("content")
                        )
                        if real_response and real_response != ai_response:
                            ai_response = real_response
                            print(f"  🔧 从meta_info中提取到真实响应")
                
                # 如果仍然是状态机信息，显示警告
                if "状态机处理" in ai_response or "处理完成" in ai_response:
                    print(f"  ⚠️ 仍然收到状态机处理信息")
                    ai_response = f"⚠️ 系统返回了状态机处理信息而非AI回复。"
                
                intent = api_data.get("intent_type") or api_data.get("intent", "unknown")
                confidence = api_data.get("confidence", 0)
                response_time = result.get("response_time", 0)

                status_info = f"\n\n[意图: {intent} | 置信度: {confidence:.2f} | 响应时间: {response_time:.2f}s]"
                history.append([message, ai_response + status_info])
                
                print(f"  ✅ 成功处理")
                print(f"  意图: {intent}")
                print(f"  置信度: {confidence:.2f}")
                print(f"  响应长度: {len(ai_response)} 字符")
                print(f"  响应预览: {ai_response[:150]}...")
                
            else:
                error_msg = f"❌ 错误: {result.get('error', '未知错误')}"
                history.append([message, error_msg])
                print(f"  ❌ 处理失败: {result.get('error', '未知错误')}")
        
        print("\n🎉 Gradio聊天界面测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ Gradio聊天界面测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_debug_function():
    """测试调试功能"""
    print("\n🔧 测试调试功能...")
    
    try:
        from tests.comprehensive.interactive.enhanced_gradio_test import EnhancedGradioTestSystem
        
        test_system = EnhancedGradioTestSystem()
        
        # 测试调试功能
        test_message = "胸肌怎么练"
        print(f"🔍 调试API响应，测试消息: {test_message}")
        
        result = await test_system.send_message(test_message)
        
        debug_info = []
        debug_info.append(f"🔍 API响应调试信息")
        debug_info.append(f"测试消息: {test_message}")
        debug_info.append(f"API调用成功: {result['success']}")
        
        if result["success"]:
            api_data = result.get("data", {})
            debug_info.append(f"API数据键: {list(api_data.keys())}")
            
            # 检查所有可能的响应字段
            response_fields = ["response", "response_content", "content"]
            for field in response_fields:
                value = api_data.get(field)
                if value:
                    debug_info.append(f"{field}: {value[:200]}...")
            
            # 检查meta_info
            meta_info = api_data.get("meta_info", {})
            if isinstance(meta_info, dict):
                debug_info.append(f"meta_info键: {list(meta_info.keys())}")
            
            # 检查意图和置信度
            debug_info.append(f"意图: {api_data.get('intent_type', 'unknown')}")
            debug_info.append(f"置信度: {api_data.get('confidence', 0)}")
            
        else:
            debug_info.append(f"API错误: {result.get('error', '未知错误')}")
        
        print("\n".join(debug_info))
        print("\n✅ 调试功能正常工作")
        return True
        
    except Exception as e:
        print(f"❌ 调试功能测试失败: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🚀 开始最终验证...")
    print("="*60)
    
    # 测试Gradio聊天界面
    chat_success = await test_gradio_chat_interface()
    
    # 测试调试功能
    debug_success = await test_debug_function()
    
    print("\n" + "="*60)
    print("🎯 最终验证结果")
    print("="*60)
    
    if chat_success:
        print("✅ Gradio聊天界面工作正常")
    else:
        print("❌ Gradio聊天界面存在问题")
    
    if debug_success:
        print("✅ 调试功能工作正常")
    else:
        print("❌ 调试功能存在问题")
    
    if chat_success and debug_success:
        print("\n🎉 所有验证通过！Gradio实时测试功能修复完成！")
        print("\n📋 修复总结:")
        print("  ✅ 修复了API响应内容提取逻辑")
        print("  ✅ 改进了LangGraph路由决策")
        print("  ✅ 增强了健身建议处理器")
        print("  ✅ 添加了调试功能")
        print("  ✅ 优化了聊天界面显示")
        
        print("\n📱 使用方法:")
        print("  1. 启动Gradio界面:")
        print("     python tests/comprehensive/interactive/enhanced_gradio_test.py")
        print("  2. 访问 http://localhost:7860")
        print("  3. 在'实时对话测试'标签页测试健身问题")
        print("  4. 使用'🔍 调试API响应'按钮诊断问题")
        
        print("\n🎯 测试建议:")
        print("  - 测试'胸肌怎么练'等具体健身问题")
        print("  - 验证系统返回专业的健身建议而非状态机信息")
        print("  - 检查意图识别和置信度是否正确")
        
    else:
        print("\n⚠️ 部分验证未通过，请检查上述问题")

if __name__ == "__main__":
    asyncio.run(main())
