#!/usr/bin/env python3
"""
简单的Gradio修复测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

async def test_basic_functionality():
    """测试基本功能"""
    print("🔍 测试基本功能...")
    
    try:
        from tests.comprehensive.interactive.enhanced_gradio_test import EnhancedGradioTestSystem
        print("✅ 成功导入EnhancedGradioTestSystem")
        
        # 创建测试系统
        test_system = EnhancedGradioTestSystem()
        print("✅ 成功创建测试系统实例")
        
        # 测试消息发送（不初始化API服务器，只测试逻辑）
        print("🔍 测试消息处理逻辑...")
        
        # 生成会话ID
        session_id = test_system.generate_session_id()
        print(f"✅ 生成会话ID: {session_id}")
        
        # 测试统计功能
        stats = test_system.get_statistics()
        print(f"✅ 获取统计信息: {stats}")
        
        # 测试环境状态
        env_status = test_system.get_environment_status()
        print(f"✅ 获取环境状态: {env_status[:100]}...")
        
        print("\n🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_langgraph_fix():
    """测试LangGraph修复"""
    print("\n🔍 测试LangGraph修复...")
    
    try:
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        print("✅ 成功导入basic_test_graph")
        
        # 测试健身消息处理
        test_message = "胸肌怎么练"
        print(f"📝 测试消息: {test_message}")
        
        result = await basic_test_graph.process_message(
            message=test_message,
            conversation_id="test_conv",
            user_info={"user_id": "test_user", "nickname": "测试用户"}
        )
        
        print(f"✅ LangGraph处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  响应: {result.get('response', '')[:200]}...")
        print(f"  意图: {result.get('intent_type', 'unknown')}")
        print(f"  置信度: {result.get('confidence', 0.0):.2f}")
        
        # 检查是否是有意义的响应
        response = result.get('response', '')
        is_meaningful = (
            len(response) > 50 and 
            "状态机处理" not in response and
            "处理完成" not in response and
            ("胸肌" in response or "训练" in response or "健身" in response)
        )
        
        if is_meaningful:
            print("🎉 LangGraph现在返回有意义的健身建议！")
            return True
        else:
            print("⚠️ LangGraph仍然返回简单的状态信息")
            return False
        
    except Exception as e:
        print(f"❌ LangGraph测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始简单测试...")
    
    # 测试基本功能
    basic_success = await test_basic_functionality()
    
    # 测试LangGraph修复
    langgraph_success = await test_langgraph_fix()
    
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    if basic_success:
        print("✅ 基本功能正常")
    else:
        print("❌ 基本功能存在问题")
    
    if langgraph_success:
        print("✅ LangGraph修复成功")
    else:
        print("⚠️ LangGraph需要进一步修复")
    
    if basic_success and langgraph_success:
        print("\n🎉 所有测试通过！Gradio系统修复成功！")
        print("\n📱 现在可以启动Gradio界面:")
        print("  python tests/comprehensive/interactive/enhanced_gradio_test.py")
    else:
        print("\n⚠️ 部分测试未通过，请检查上述问题")

if __name__ == "__main__":
    asyncio.run(main())
