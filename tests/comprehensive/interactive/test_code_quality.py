#!/usr/bin/env python3
"""
代码质量检查

检查模块化节点文件的代码质量、注释完整性、错误处理和导入依赖
"""

import os
import sys
import ast
import re
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self):
        self.nodes_dir = "app/services/ai_assistant/langgraph/nodes"
        self.main_file = "app/services/ai_assistant/langgraph/enhanced_exercise_graph_refactored.py"
        
    def check_file_structure(self) -> Dict[str, Any]:
        """检查文件结构"""
        print("📁 检查文件结构...")
        
        expected_files = [
            "intent_router.py",
            "user_verification.py", 
            "parameter_collection.py",
            "database_query.py",
            "ai_filtering.py",
            "response_generation.py"
        ]
        
        results = {
            "missing_files": [],
            "existing_files": [],
            "file_sizes": {}
        }
        
        for file_name in expected_files:
            file_path = os.path.join(self.nodes_dir, file_name)
            if os.path.exists(file_path):
                results["existing_files"].append(file_name)
                # 检查文件大小（行数）
                with open(file_path, 'r', encoding='utf-8') as f:
                    line_count = len(f.readlines())
                    results["file_sizes"][file_name] = line_count
                    
                print(f"  ✅ {file_name}: {line_count} 行")
            else:
                results["missing_files"].append(file_name)
                print(f"  ❌ {file_name}: 文件不存在")
        
        # 检查主控制器文件
        if os.path.exists(self.main_file):
            with open(self.main_file, 'r', encoding='utf-8') as f:
                main_lines = len(f.readlines())
                results["file_sizes"]["enhanced_exercise_graph_refactored.py"] = main_lines
                print(f"  ✅ enhanced_exercise_graph_refactored.py: {main_lines} 行")
        
        return results
    
    def check_code_comments(self, file_path: str) -> Dict[str, Any]:
        """检查代码注释完整性"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 统计注释
        comment_lines = 0
        docstring_lines = 0
        code_lines = 0
        
        in_docstring = False
        docstring_char = None
        
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue
                
            # 检查文档字符串
            if '"""' in stripped or "'''" in stripped:
                if not in_docstring:
                    in_docstring = True
                    docstring_char = '"""' if '"""' in stripped else "'''"
                    docstring_lines += 1
                elif docstring_char in stripped:
                    in_docstring = False
                    docstring_lines += 1
                else:
                    docstring_lines += 1
            elif in_docstring:
                docstring_lines += 1
            elif stripped.startswith('#'):
                comment_lines += 1
            elif stripped:
                code_lines += 1
        
        total_lines = comment_lines + docstring_lines + code_lines
        comment_ratio = (comment_lines + docstring_lines) / total_lines if total_lines > 0 else 0
        
        return {
            "comment_lines": comment_lines,
            "docstring_lines": docstring_lines,
            "code_lines": code_lines,
            "total_lines": total_lines,
            "comment_ratio": comment_ratio
        }
    
    def check_error_handling(self, file_path: str) -> Dict[str, Any]:
        """检查错误处理"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            tree = ast.parse(content)
        except SyntaxError:
            return {"syntax_error": True}
        
        try_blocks = 0
        except_blocks = 0
        logger_calls = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Try):
                try_blocks += 1
            elif isinstance(node, ast.ExceptHandler):
                except_blocks += 1
            elif isinstance(node, ast.Call):
                if (isinstance(node.func, ast.Attribute) and 
                    isinstance(node.func.value, ast.Name) and
                    node.func.value.id == 'logger'):
                    logger_calls += 1
        
        return {
            "try_blocks": try_blocks,
            "except_blocks": except_blocks,
            "logger_calls": logger_calls,
            "has_error_handling": try_blocks > 0 and except_blocks > 0
        }
    
    def check_imports(self, file_path: str) -> Dict[str, Any]:
        """检查导入依赖"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            tree = ast.parse(content)
        except SyntaxError:
            return {"syntax_error": True}
        
        imports = []
        from_imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    from_imports.append(f"{module}.{alias.name}")
        
        # 检查关键导入
        has_logging = any("logging" in imp for imp in imports + from_imports)
        has_typing = any("typing" in imp for imp in imports + from_imports)
        has_langgraph = any("langgraph" in imp for imp in imports + from_imports)
        
        return {
            "imports": imports,
            "from_imports": from_imports,
            "total_imports": len(imports) + len(from_imports),
            "has_logging": has_logging,
            "has_typing": has_typing,
            "has_langgraph": has_langgraph
        }
    
    def check_function_documentation(self, file_path: str) -> Dict[str, Any]:
        """检查函数文档"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            tree = ast.parse(content)
        except SyntaxError:
            return {"syntax_error": True}
        
        functions = []
        documented_functions = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append(node.name)
                # 检查是否有文档字符串
                if (node.body and 
                    isinstance(node.body[0], ast.Expr) and
                    isinstance(node.body[0].value, ast.Constant) and
                    isinstance(node.body[0].value.value, str)):
                    documented_functions += 1
        
        documentation_ratio = documented_functions / len(functions) if functions else 0
        
        return {
            "total_functions": len(functions),
            "documented_functions": documented_functions,
            "documentation_ratio": documentation_ratio,
            "functions": functions
        }

def main():
    """主函数"""
    print("🔍 开始代码质量检查...")
    
    checker = CodeQualityChecker()
    
    # 1. 检查文件结构
    structure_result = checker.check_file_structure()
    
    # 2. 检查各个模块文件
    node_files = [
        "intent_router.py",
        "user_verification.py", 
        "parameter_collection.py",
        "database_query.py",
        "ai_filtering.py",
        "response_generation.py"
    ]
    
    quality_results = {}
    
    for file_name in node_files:
        file_path = os.path.join(checker.nodes_dir, file_name)
        if os.path.exists(file_path):
            print(f"\n📝 检查 {file_name}...")
            
            # 注释检查
            comments = checker.check_code_comments(file_path)
            print(f"  注释覆盖率: {comments['comment_ratio']:.1%}")
            
            # 错误处理检查
            error_handling = checker.check_error_handling(file_path)
            print(f"  错误处理: {'✅' if error_handling.get('has_error_handling') else '❌'}")
            print(f"  Try块: {error_handling.get('try_blocks', 0)}")
            print(f"  日志调用: {error_handling.get('logger_calls', 0)}")
            
            # 导入检查
            imports = checker.check_imports(file_path)
            print(f"  导入数量: {imports.get('total_imports', 0)}")
            print(f"  关键导入: logging={'✅' if imports.get('has_logging') else '❌'}, typing={'✅' if imports.get('has_typing') else '❌'}")
            
            # 函数文档检查
            docs = checker.check_function_documentation(file_path)
            print(f"  函数文档率: {docs.get('documentation_ratio', 0):.1%} ({docs.get('documented_functions', 0)}/{docs.get('total_functions', 0)})")
            
            quality_results[file_name] = {
                "comments": comments,
                "error_handling": error_handling,
                "imports": imports,
                "documentation": docs
            }
    
    # 3. 生成质量报告
    print("\n" + "="*60)
    print("📊 代码质量检查报告")
    print("="*60)
    
    # 文件结构评估
    missing_files = len(structure_result["missing_files"])
    total_expected = len(node_files)
    structure_score = ((total_expected - missing_files) / total_expected) * 100
    
    print(f"📁 文件结构: {structure_score:.1f}% ({total_expected - missing_files}/{total_expected})")
    
    # 代码行数检查
    oversized_files = []
    for file_name, line_count in structure_result["file_sizes"].items():
        if line_count > 300:
            oversized_files.append((file_name, line_count))
    
    if oversized_files:
        print(f"⚠️ 超大文件 (>300行):")
        for file_name, line_count in oversized_files:
            print(f"  - {file_name}: {line_count} 行")
    else:
        print("✅ 所有文件大小合理 (<300行)")
    
    # 质量指标汇总
    total_comment_ratio = 0
    total_error_handling = 0
    total_doc_ratio = 0
    valid_files = 0
    
    for file_name, results in quality_results.items():
        if not results["comments"].get("syntax_error"):
            valid_files += 1
            total_comment_ratio += results["comments"]["comment_ratio"]
            total_error_handling += (1 if results["error_handling"].get("has_error_handling") else 0)
            total_doc_ratio += results["documentation"]["documentation_ratio"]
    
    if valid_files > 0:
        avg_comment_ratio = total_comment_ratio / valid_files
        error_handling_ratio = total_error_handling / valid_files
        avg_doc_ratio = total_doc_ratio / valid_files
        
        print(f"\n📊 质量指标:")
        print(f"  平均注释覆盖率: {avg_comment_ratio:.1%}")
        print(f"  错误处理覆盖率: {error_handling_ratio:.1%}")
        print(f"  函数文档覆盖率: {avg_doc_ratio:.1%}")
        
        # 总体评级
        overall_score = (
            structure_score * 0.2 +
            avg_comment_ratio * 100 * 0.3 +
            error_handling_ratio * 100 * 0.3 +
            avg_doc_ratio * 100 * 0.2
        )
        
        if overall_score >= 80:
            grade = "优秀"
        elif overall_score >= 70:
            grade = "良好"
        elif overall_score >= 60:
            grade = "一般"
        else:
            grade = "需改进"
        
        print(f"\n🎯 总体评级: {grade} ({overall_score:.1f}分)")
        
        if overall_score >= 70:
            print("✅ 代码质量达到生产环境标准")
        else:
            print("⚠️ 建议优化代码质量后部署")
            
            # 改进建议
            print("\n💡 改进建议:")
            if avg_comment_ratio < 0.2:
                print("  - 增加代码注释，提高可读性")
            if error_handling_ratio < 0.8:
                print("  - 完善错误处理机制")
            if avg_doc_ratio < 0.8:
                print("  - 为函数添加文档字符串")
            if oversized_files:
                print("  - 进一步拆分大文件")

if __name__ == "__main__":
    main()
