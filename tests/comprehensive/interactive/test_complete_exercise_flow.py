#!/usr/bin/env python3
"""
完整运动动作流程测试

模拟用户从查询"胸肌怎么练"到获得完整动作推荐的整个流程，
包括用户信息收集、参数收集、数据库查询、AI筛选和响应生成。
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

async def test_complete_exercise_flow():
    """测试完整的运动动作查询流程"""
    print("🚀 开始完整运动动作流程测试...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import enhanced_exercise_graph
        
        conversation_id = "complete_flow_test"
        user_info = {
            "user_id": "test_user_complete",
            "user_profile": {}  # 空档案，触发完整流程
        }
        
        print("\n" + "="*60)
        print("📝 步骤1: 用户查询 - '胸肌怎么练'")
        print("="*60)
        
        # 第一步：用户查询
        result1 = await enhanced_exercise_graph.process_message(
            message="胸肌怎么练",
            conversation_id=conversation_id,
            user_info=user_info
        )
        
        print(f"系统响应: {result1.get('response', '')}")
        print(f"意图: {result1.get('intent_type', 'unknown')}")
        print(f"置信度: {result1.get('confidence', 0.0):.2f}")
        
        if "性别" in result1.get('response', ''):
            print("\n" + "="*60)
            print("📝 步骤2: 提供性别信息")
            print("="*60)
            
            # 第二步：提供性别
            result2 = await enhanced_exercise_graph.continue_conversation(
                message="男",
                conversation_id=conversation_id,
                user_info=user_info
            )
            
            print(f"系统响应: {result2.get('response', '')}")
            
            if "年龄" in result2.get('response', ''):
                print("\n" + "="*60)
                print("📝 步骤3: 提供年龄信息")
                print("="*60)
                
                # 第三步：提供年龄
                result3 = await enhanced_exercise_graph.continue_conversation(
                    message="25岁",
                    conversation_id=conversation_id,
                    user_info=user_info
                )
                
                print(f"系统响应: {result3.get('response', '')}")
                
                if "身高" in result3.get('response', ''):
                    print("\n" + "="*60)
                    print("📝 步骤4: 提供身高信息")
                    print("="*60)
                    
                    # 第四步：提供身高
                    result4 = await enhanced_exercise_graph.continue_conversation(
                        message="175cm",
                        conversation_id=conversation_id,
                        user_info=user_info
                    )
                    
                    print(f"系统响应: {result4.get('response', '')}")
                    
                    if "体重" in result4.get('response', ''):
                        print("\n" + "="*60)
                        print("📝 步骤5: 提供体重信息")
                        print("="*60)
                        
                        # 第五步：提供体重
                        result5 = await enhanced_exercise_graph.continue_conversation(
                            message="70kg",
                            conversation_id=conversation_id,
                            user_info=user_info
                        )
                        
                        print(f"系统响应: {result5.get('response', '')}")
                        
                        if "健身目标" in result5.get('response', ''):
                            print("\n" + "="*60)
                            print("📝 步骤6: 提供健身目标")
                            print("="*60)
                            
                            # 第六步：提供健身目标
                            result6 = await enhanced_exercise_graph.continue_conversation(
                                message="增肌",
                                conversation_id=conversation_id,
                                user_info=user_info
                            )
                            
                            print(f"系统响应: {result6.get('response', '')}")
                            
                            if "健身水平" in result6.get('response', ''):
                                print("\n" + "="*60)
                                print("📝 步骤7: 提供健身水平")
                                print("="*60)
                                
                                # 第七步：提供健身水平
                                result7 = await enhanced_exercise_graph.continue_conversation(
                                    message="初级",
                                    conversation_id=conversation_id,
                                    user_info=user_info
                                )
                                
                                print(f"系统响应: {result7.get('response', '')}")
                                
                                if "锻炼" in result7.get('response', '') and ("健身房" in result7.get('response', '') or "居家" in result7.get('response', '')):
                                    print("\n" + "="*60)
                                    print("📝 步骤8: 选择训练场景")
                                    print("="*60)
                                    
                                    # 第八步：选择训练场景
                                    result8 = await enhanced_exercise_graph.continue_conversation(
                                        message="健身房",
                                        conversation_id=conversation_id,
                                        user_info=user_info
                                    )
                                    
                                    print(f"系统响应: {result8.get('response', '')}")
                                    
                                    # 检查最终结果
                                    final_response = result8.get('response', '')
                                    structured_data = result8.get('structured_data', {})
                                    
                                    print("\n" + "="*60)
                                    print("📊 最终结果分析")
                                    print("="*60)
                                    
                                    if len(final_response) > 200 and ("俯卧撑" in final_response or "卧推" in final_response or "动作" in final_response):
                                        print("✅ 成功获得完整的动作推荐！")
                                        print(f"响应长度: {len(final_response)} 字符")
                                        
                                        if structured_data:
                                            recommendations = structured_data.get('exercise_recommendations', [])
                                            print(f"推荐动作数量: {len(recommendations)}")
                                            
                                            if recommendations:
                                                print("推荐的动作:")
                                                for i, exercise in enumerate(recommendations[:3], 1):
                                                    print(f"  {i}. {exercise.get('name', '未知动作')}")
                                        
                                        print("\n🎉 完整流程测试成功！")
                                        return True
                                    else:
                                        print("⚠️ 最终响应不包含预期的动作推荐")
                                        print(f"响应内容: {final_response[:200]}...")
                                        return False
                                else:
                                    print("⚠️ 未询问训练场景，流程可能有问题")
                                    return False
                            else:
                                print("⚠️ 未询问健身水平，流程可能有问题")
                                return False
                        else:
                            print("⚠️ 未询问健身目标，流程可能有问题")
                            return False
                    else:
                        print("⚠️ 未询问体重，流程可能有问题")
                        return False
                else:
                    print("⚠️ 未询问身高，流程可能有问题")
                    return False
            else:
                print("⚠️ 未询问年龄，流程可能有问题")
                return False
        else:
            print("⚠️ 未询问性别，流程可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_with_complete_user_profile():
    """测试已有完整用户档案的情况"""
    print("\n🔄 测试已有完整用户档案的情况...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import enhanced_exercise_graph
        
        # 完整的用户档案
        complete_user_info = {
            "user_id": "test_user_complete_profile",
            "user_profile": {
                "gender": "男",
                "age": 28,
                "height": 180,
                "weight": 75,
                "fitness_goal": "增肌",
                "fitness_level": "中级"
            }
        }
        
        result = await enhanced_exercise_graph.process_message(
            message="胸肌怎么练",
            conversation_id="complete_profile_test",
            user_info=complete_user_info
        )
        
        response = result.get('response', '')
        structured_data = result.get('structured_data', {})
        
        print(f"响应长度: {len(response)} 字符")
        print(f"响应预览: {response[:300]}...")
        
        if structured_data:
            recommendations = structured_data.get('exercise_recommendations', [])
            print(f"推荐动作数量: {len(recommendations)}")
        
        # 检查是否直接给出了动作推荐
        if len(response) > 200 and ("俯卧撑" in response or "卧推" in response or "动作" in response):
            print("✅ 完整用户档案测试成功 - 直接给出动作推荐")
            return True
        elif "锻炼" in response and ("健身房" in response or "居家" in response):
            print("✅ 完整用户档案测试成功 - 询问训练场景")
            return True
        else:
            print("⚠️ 完整用户档案测试需要优化")
            return False
            
    except Exception as e:
        print(f"❌ 完整用户档案测试失败: {str(e)}")
        return False

async def test_gradio_integration():
    """测试Gradio集成"""
    print("\n🔗 测试Gradio集成...")
    
    try:
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        
        # 测试基础图的路由功能
        result = await basic_test_graph.process_message(
            message="胸肌怎么练",
            conversation_id="gradio_test",
            user_info={"user_id": "gradio_user"}
        )
        
        success = result.get("success", False)
        processing_system = result.get("processing_info", {}).get("system", "unknown")
        response = result.get("response", "")
        
        print(f"成功: {success}")
        print(f"处理系统: {processing_system}")
        print(f"响应长度: {len(response)} 字符")
        print(f"响应预览: {response[:150]}...")
        
        if success and processing_system == "enhanced_exercise":
            print("✅ Gradio集成测试成功")
            return True
        else:
            print("⚠️ Gradio集成测试需要优化")
            return False
            
    except Exception as e:
        print(f"❌ Gradio集成测试失败: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🚀 开始完整运动动作流程综合测试...")
    
    # 完整流程测试
    complete_flow_success = await test_complete_exercise_flow()
    
    # 完整用户档案测试
    complete_profile_success = await test_with_complete_user_profile()
    
    # Gradio集成测试
    gradio_success = await test_gradio_integration()
    
    print("\n" + "="*60)
    print("🎯 综合测试结果")
    print("="*60)
    
    if complete_flow_success:
        print("✅ 完整流程测试通过")
    else:
        print("❌ 完整流程测试失败")
    
    if complete_profile_success:
        print("✅ 完整用户档案测试通过")
    else:
        print("❌ 完整用户档案测试失败")
    
    if gradio_success:
        print("✅ Gradio集成测试通过")
    else:
        print("❌ Gradio集成测试失败")
    
    overall_success = complete_flow_success and complete_profile_success and gradio_success
    
    if overall_success:
        print("\n🎉 所有测试通过！增强版运动动作处理系统完全正常！")
        print("\n📱 验证标准达成:")
        print("✅ 识别body_part='胸部'")
        print("✅ 检查用户档案完整性并询问缺失信息")
        print("✅ 询问训练场景")
        print("✅ 从数据库检索胸部训练动作")
        print("✅ 基于用户档案进行AI筛选")
        print("✅ 返回个性化的动作推荐")
        print("\n🚀 现在可以在Gradio界面中体验完整流程:")
        print("  python tests/comprehensive/interactive/enhanced_gradio_test.py")
    else:
        print("\n⚠️ 部分测试未通过，请检查上述问题")

if __name__ == "__main__":
    asyncio.run(main())
