"""
Gradio测试系统配置文件

集中管理测试参数、场景和配置选项
"""

from typing import Dict, List, Any
from dataclasses import dataclass
import os


@dataclass
class APIConfig:
    """API配置"""
    base_url: str = "http://127.0.0.1:8000"
    timeout: float = 30.0
    max_retries: int = 3
    health_check_interval: int = 30


@dataclass
class TestConfig:
    """测试配置"""
    test_user_id: int = 15
    default_session_prefix: str = "gradio_test"
    max_conversation_history: int = 50
    performance_metrics_limit: int = 1000
    auto_refresh_interval: int = 5


@dataclass
class UIConfig:
    """UI配置"""
    gradio_port: int = 7860
    gradio_host: str = "0.0.0.0"
    theme: str = "soft"
    chatbot_height: int = 500
    enable_share: bool = False
    enable_debug: bool = False


class TestScenarios:
    """测试场景配置"""
    
    # 基础对话场景
    BASIC_CONVERSATION = {
        "健身咨询": [
            "你好，我想了解健身",
            "我是初学者，应该从什么开始？",
            "请推荐一些适合新手的运动",
            "每天运动多长时间比较好？"
        ],
        "训练计划": [
            "我想制定一个训练计划",
            "我的目标是增肌",
            "每周可以训练3-4次",
            "请给我一个详细的计划"
        ],
        "营养建议": [
            "请给我一些饮食建议",
            "我想减脂，应该怎么吃？",
            "蛋白质摄入量应该是多少？",
            "有什么健康的零食推荐吗？"
        ],
        "运动指导": [
            "深蹲的正确姿势是什么？",
            "如何避免运动受伤？",
            "训练后如何恢复？",
            "拉伸的重要性是什么？"
        ]
    }
    
    # 高级测试场景
    ADVANCED_SCENARIOS = {
        "多轮对话测试": [
            "我想开始健身",
            "我的目标是减重10公斤",
            "我现在体重70公斤，身高175cm",
            "我每周有3天时间锻炼",
            "请给我制定一个计划",
            "这个计划需要多长时间能看到效果？"
        ],
        "专业咨询测试": [
            "我有膝盖伤病史，还能做深蹲吗？",
            "什么运动对膝盖比较友好？",
            "如何加强膝盖周围的肌肉？",
            "康复训练应该注意什么？"
        ],
        "个性化建议测试": [
            "我是女性，25岁",
            "想要塑形，特别是腰腹部",
            "不喜欢跑步，有其他有氧运动推荐吗？",
            "在家就能做的运动有哪些？"
        ]
    }
    
    # 压力测试场景
    STRESS_TEST_SCENARIOS = {
        "快速连续请求": [
            "你好",
            "健身",
            "计划",
            "营养",
            "运动",
            "恢复",
            "拉伸",
            "减脂",
            "增肌",
            "塑形"
        ],
        "长文本测试": [
            "我是一个30岁的上班族，平时工作比较忙，只有晚上和周末有时间锻炼。我的身高是180cm，体重85kg，体脂率大概在20%左右。我的健身目标是减脂增肌，希望能够在半年内将体脂率降到15%以下，同时增加一些肌肉量。我之前有过一些健身经验，但不是很系统，主要是跑步和一些简单的器械训练。现在想要一个更科学、更系统的训练计划。请问你能帮我制定一个详细的训练和饮食计划吗？",
            "关于饮食方面，我平时比较喜欢吃肉类，但蔬菜吃得比较少。早餐通常是面包或包子，午餐在公司食堂解决，晚餐会自己做一些简单的菜。我想了解一下，在减脂增肌期间，我应该如何调整我的饮食结构？需要补充蛋白粉吗？还有什么其他的营养补剂推荐？"
        ]
    }
    
    @classmethod
    def get_all_scenarios(cls) -> Dict[str, List[str]]:
        """获取所有测试场景"""
        all_scenarios = {}
        all_scenarios.update(cls.BASIC_CONVERSATION)
        all_scenarios.update(cls.ADVANCED_SCENARIOS)
        all_scenarios.update(cls.STRESS_TEST_SCENARIOS)
        return all_scenarios
    
    @classmethod
    def get_scenario_categories(cls) -> Dict[str, Dict[str, List[str]]]:
        """获取分类的测试场景"""
        return {
            "基础对话": cls.BASIC_CONVERSATION,
            "高级场景": cls.ADVANCED_SCENARIOS,
            "压力测试": cls.STRESS_TEST_SCENARIOS
        }


class PerformanceThresholds:
    """性能阈值配置"""
    
    # 响应时间阈值（秒）
    RESPONSE_TIME_EXCELLENT = 1.0
    RESPONSE_TIME_GOOD = 3.0
    RESPONSE_TIME_ACCEPTABLE = 5.0
    RESPONSE_TIME_POOR = 10.0
    
    # 成功率阈值（百分比）
    SUCCESS_RATE_EXCELLENT = 95.0
    SUCCESS_RATE_GOOD = 90.0
    SUCCESS_RATE_ACCEPTABLE = 85.0
    SUCCESS_RATE_POOR = 80.0
    
    # 置信度阈值
    CONFIDENCE_HIGH = 0.8
    CONFIDENCE_MEDIUM = 0.6
    CONFIDENCE_LOW = 0.4
    
    @classmethod
    def get_response_time_level(cls, response_time: float) -> str:
        """获取响应时间等级"""
        if response_time <= cls.RESPONSE_TIME_EXCELLENT:
            return "优秀"
        elif response_time <= cls.RESPONSE_TIME_GOOD:
            return "良好"
        elif response_time <= cls.RESPONSE_TIME_ACCEPTABLE:
            return "可接受"
        elif response_time <= cls.RESPONSE_TIME_POOR:
            return "较差"
        else:
            return "很差"
    
    @classmethod
    def get_success_rate_level(cls, success_rate: float) -> str:
        """获取成功率等级"""
        if success_rate >= cls.SUCCESS_RATE_EXCELLENT:
            return "优秀"
        elif success_rate >= cls.SUCCESS_RATE_GOOD:
            return "良好"
        elif success_rate >= cls.SUCCESS_RATE_ACCEPTABLE:
            return "可接受"
        elif success_rate >= cls.SUCCESS_RATE_POOR:
            return "较差"
        else:
            return "很差"
    
    @classmethod
    def get_confidence_level(cls, confidence: float) -> str:
        """获取置信度等级"""
        if confidence >= cls.CONFIDENCE_HIGH:
            return "高"
        elif confidence >= cls.CONFIDENCE_MEDIUM:
            return "中"
        elif confidence >= cls.CONFIDENCE_LOW:
            return "低"
        else:
            return "很低"


class TestEnvironment:
    """测试环境配置"""
    
    @staticmethod
    def get_environment_info() -> Dict[str, Any]:
        """获取环境信息"""
        return {
            "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
            "platform": os.name,
            "working_directory": os.getcwd(),
            "environment_variables": {
                "ENABLE_REAL_MODELS": os.getenv("ENABLE_REAL_MODELS", "false"),
                "LLM_PROVIDER": os.getenv("LLM_PROVIDER", "default"),
                "QWEN_API_KEY": "已设置" if os.getenv("QWEN_API_KEY") else "未设置",
                "OPENAI_API_KEY": "已设置" if os.getenv("OPENAI_API_KEY") else "未设置"
            }
        }
    
    @staticmethod
    def validate_environment() -> Dict[str, bool]:
        """验证环境配置"""
        checks = {
            "python_version_ok": os.sys.version_info >= (3, 8),
            "has_api_key": bool(os.getenv("QWEN_API_KEY") or os.getenv("OPENAI_API_KEY")),
            "real_models_enabled": os.getenv("ENABLE_REAL_MODELS", "false").lower() == "true"
        }
        return checks


# 默认配置实例
default_api_config = APIConfig()
default_test_config = TestConfig()
default_ui_config = UIConfig()

# 导出配置
__all__ = [
    "APIConfig",
    "TestConfig", 
    "UIConfig",
    "TestScenarios",
    "PerformanceThresholds",
    "TestEnvironment",
    "default_api_config",
    "default_test_config",
    "default_ui_config"
]
