#!/usr/bin/env python3
"""
完整端到端测试

验证重构后系统的所有核心功能，包括多轮对话、数据库集成、错误处理等
"""

import asyncio
import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

async def test_complete_exercise_workflow():
    """测试完整的运动动作查询工作流"""
    print("🔄 测试完整运动动作查询工作流...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph_refactored import enhanced_exercise_graph_refactored
        
        conversation_id = "e2e_complete_workflow"
        user_info = {
            "user_id": "15",
            "user_profile": {
                "gender": "男",
                "age": 25,
                "height": 175,
                "weight": 70,
                "fitness_goal": "增肌",
                "fitness_level": "初级"
            }
        }
        
        # 步骤1：初始查询
        print("\n📝 步骤1: 初始查询 - 胸肌怎么练")
        start_time = time.time()
        result1 = await enhanced_exercise_graph_refactored.process_message(
            message="胸肌怎么练",
            conversation_id=conversation_id,
            user_info=user_info
        )
        step1_time = time.time() - start_time
        
        print(f"  响应时间: {step1_time:.2f}s")
        print(f"  成功: {result1.get('success', False)}")
        print(f"  响应: {result1.get('response', '')[:100]}...")
        
        # 检查是否询问训练场景
        if "健身房" in result1.get('response', '') or "居家" in result1.get('response', ''):
            print("\n📝 步骤2: 用户选择训练场景 - 健身房")
            start_time = time.time()
            result2 = await enhanced_exercise_graph_refactored.continue_conversation(
                message="健身房",
                conversation_id=conversation_id,
                user_info=user_info
            )
            step2_time = time.time() - start_time
            
            print(f"  响应时间: {step2_time:.2f}s")
            print(f"  成功: {result2.get('success', False)}")
            print(f"  响应长度: {len(result2.get('response', ''))} 字符")
            
            # 检查结构化数据
            structured_data = result2.get('structured_data', {})
            recommendations = structured_data.get('exercise_recommendations', [])
            
            workflow_success = (
                result1.get('success', False) and
                result2.get('success', False) and
                len(recommendations) > 0 and
                step1_time < 1.0 and
                step2_time < 1.0
            )
            
            print(f"\n✅ 完整工作流测试: {'成功' if workflow_success else '失败'}")
            print(f"  推荐动作数量: {len(recommendations)}")
            print(f"  总响应时间: {step1_time + step2_time:.2f}s")
            
            return workflow_success
        else:
            print("⚠️ 系统未按预期询问训练场景")
            return False
            
    except Exception as e:
        print(f"❌ 完整工作流测试异常: {str(e)}")
        return False

async def test_multi_turn_conversation():
    """测试多轮对话状态管理"""
    print("\n🔄 测试多轮对话状态管理...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph_refactored import enhanced_exercise_graph_refactored
        
        conversation_id = "e2e_multi_turn"
        user_info = {
            "user_id": "15",
            "user_profile": {}  # 空档案，触发信息收集
        }
        
        # 第一轮：初始查询
        print("\n📝 第一轮: 初始查询")
        result1 = await enhanced_exercise_graph_refactored.process_message(
            message="腹肌怎么练",
            conversation_id=conversation_id,
            user_info=user_info
        )
        
        print(f"  响应: {result1.get('response', '')[:100]}...")
        
        # 检查是否询问用户信息
        if "性别" in result1.get('response', ''):
            print("\n📝 第二轮: 提供性别信息")
            result2 = await enhanced_exercise_graph_refactored.continue_conversation(
                message="男",
                conversation_id=conversation_id,
                user_info=user_info
            )
            
            print(f"  响应: {result2.get('response', '')[:100]}...")
            
            # 继续多轮对话...
            turns = [
                ("25岁", "年龄"),
                ("175cm", "身高"),
                ("70kg", "体重"),
                ("增肌", "健身目标"),
                ("初级", "健身水平")
            ]
            
            conversation_success = True
            for i, (user_input, expected_field) in enumerate(turns, 3):
                print(f"\n📝 第{i}轮: 提供{expected_field}")
                try:
                    result = await enhanced_exercise_graph_refactored.continue_conversation(
                        message=user_input,
                        conversation_id=conversation_id,
                        user_info=user_info
                    )
                    
                    if not result.get('success', False):
                        conversation_success = False
                        break
                        
                    print(f"  响应: {result.get('response', '')[:100]}...")
                    
                except Exception as e:
                    print(f"  ❌ 第{i}轮失败: {str(e)}")
                    conversation_success = False
                    break
            
            print(f"\n✅ 多轮对话测试: {'成功' if conversation_success else '失败'}")
            return conversation_success
        else:
            print("⚠️ 系统未按预期询问用户信息")
            return False
            
    except Exception as e:
        print(f"❌ 多轮对话测试异常: {str(e)}")
        return False

async def test_database_integration_stability():
    """测试数据库集成稳定性"""
    print("\n💾 测试数据库集成稳定性...")
    
    try:
        from app.services.ai_assistant.langgraph.nodes.database_query import query_exercises_from_database
        
        # 测试多种查询组合
        test_queries = [
            ("胸部", "健身房"),
            ("腹部", "居家"),
            ("背部", "健身房"),
            ("腿部", "户外"),
            ("肩部", "健身房"),
            ("手臂", "居家")
        ]
        
        query_results = []
        total_time = 0
        
        for body_part, scenario in test_queries:
            start_time = time.time()
            exercises = await query_exercises_from_database(body_part, scenario)
            query_time = time.time() - start_time
            total_time += query_time
            
            query_results.append({
                "body_part": body_part,
                "scenario": scenario,
                "count": len(exercises),
                "time": query_time,
                "success": len(exercises) > 0
            })
            
            print(f"  {body_part} + {scenario}: {len(exercises)}个动作 ({query_time:.3f}s)")
        
        # 分析结果
        successful_queries = len([r for r in query_results if r["success"]])
        avg_query_time = total_time / len(test_queries)
        max_query_time = max(r["time"] for r in query_results)
        
        stability_success = (
            successful_queries >= len(test_queries) * 0.8 and  # 80%成功率
            avg_query_time < 0.5 and  # 平均查询时间<0.5s
            max_query_time < 1.0  # 最大查询时间<1s
        )
        
        print(f"\n✅ 数据库集成稳定性: {'成功' if stability_success else '失败'}")
        print(f"  成功查询: {successful_queries}/{len(test_queries)}")
        print(f"  平均查询时间: {avg_query_time:.3f}s")
        print(f"  最大查询时间: {max_query_time:.3f}s")
        
        return stability_success
        
    except Exception as e:
        print(f"❌ 数据库集成稳定性测试异常: {str(e)}")
        return False

async def test_error_handling_recovery():
    """测试错误处理和异常恢复"""
    print("\n🛡️ 测试错误处理和异常恢复...")
    
    try:
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        
        # 测试场景
        error_scenarios = [
            {
                "name": "无效用户ID",
                "message": "胸肌怎么练",
                "user_info": {"user_id": "invalid_id", "user_profile": {}},
                "expected_recovery": True
            },
            {
                "name": "空消息",
                "message": "",
                "user_info": {"user_id": "15", "user_profile": {}},
                "expected_recovery": True
            },
            {
                "name": "超长消息",
                "message": "胸肌怎么练" * 1000,
                "user_info": {"user_id": "15", "user_profile": {}},
                "expected_recovery": True
            },
            {
                "name": "特殊字符",
                "message": "胸肌怎么练？！@#$%^&*()",
                "user_info": {"user_id": "15", "user_profile": {}},
                "expected_recovery": True
            }
        ]
        
        recovery_results = []
        
        for scenario in error_scenarios:
            print(f"\n📝 测试: {scenario['name']}")
            
            try:
                start_time = time.time()
                result = await basic_test_graph.process_message(
                    message=scenario["message"],
                    conversation_id=f"error_test_{scenario['name']}",
                    user_info=scenario["user_info"]
                )
                response_time = time.time() - start_time
                
                # 检查系统是否优雅处理错误
                graceful_handling = (
                    result.get("success", False) and
                    len(result.get("response", "")) > 0 and
                    response_time < 2.0  # 错误处理也应该快速
                )
                
                recovery_results.append({
                    "scenario": scenario["name"],
                    "graceful": graceful_handling,
                    "response_time": response_time,
                    "has_response": len(result.get("response", "")) > 0
                })
                
                status = "✅" if graceful_handling else "❌"
                print(f"  {status} 优雅处理: {graceful_handling}")
                print(f"  响应时间: {response_time:.2f}s")
                print(f"  有响应: {len(result.get('response', ''))} 字符")
                
            except Exception as e:
                print(f"  ❌ 异常未被捕获: {str(e)}")
                recovery_results.append({
                    "scenario": scenario["name"],
                    "graceful": False,
                    "response_time": 0,
                    "has_response": False
                })
        
        # 分析错误处理能力
        graceful_count = len([r for r in recovery_results if r["graceful"]])
        error_handling_success = graceful_count >= len(error_scenarios) * 0.8
        
        print(f"\n✅ 错误处理和异常恢复: {'成功' if error_handling_success else '失败'}")
        print(f"  优雅处理: {graceful_count}/{len(error_scenarios)}")
        
        return error_handling_success
        
    except Exception as e:
        print(f"❌ 错误处理测试异常: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🚀 开始完整端到端测试...")
    
    # 执行所有测试
    tests = [
        ("完整运动动作工作流", test_complete_exercise_workflow),
        ("多轮对话状态管理", test_multi_turn_conversation),
        ("数据库集成稳定性", test_database_integration_stability),
        ("错误处理和异常恢复", test_error_handling_recovery)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 生成测试报告
    print("\n" + "="*60)
    print("📊 完整端到端测试结果")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, passed in results:
        status = "✅" if passed else "❌"
        print(f"{status} {test_name}")
        if passed:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        print("\n🎉 完整端到端测试成功！")
        print("✅ 所有核心功能正常工作")
        print("✅ 多轮对话状态管理稳定")
        print("✅ 数据库集成性能良好")
        print("✅ 错误处理机制完善")
    else:
        print(f"\n⚠️ 完整端到端测试部分成功 (通过率: {success_rate:.1f}%)")
        print("需要进一步优化的方面:")
        for test_name, passed in results:
            if not passed:
                print(f"  - {test_name}")

if __name__ == "__main__":
    asyncio.run(main())
