#!/usr/bin/env python3
"""
增强版运动动作处理图测试脚本

验证新的LangGraph原生节点实现是否能正确处理运动动作意图，
包括参数收集、用户信息验证、数据库查询、AI筛选和响应生成。
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

async def test_enhanced_exercise_graph():
    """测试增强版运动动作处理图"""
    print("🚀 开始测试增强版运动动作处理图...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import enhanced_exercise_graph
        
        # 测试用例
        test_cases = [
            {
                "name": "胸肌训练查询",
                "message": "胸肌怎么练",
                "user_info": {
                    "user_id": "test_user_1",
                    "user_profile": {
                        "fitness_level": "初级",
                        "fitness_goal": "增肌",
                        "age": 25,
                        "gender": "男"
                    }
                },
                "expected_intent": "exercise_action"
            },
            {
                "name": "腹肌训练查询",
                "message": "如何训练腹肌",
                "user_info": {
                    "user_id": "test_user_2",
                    "user_profile": {
                        "fitness_level": "中级",
                        "fitness_goal": "减脂",
                        "age": 30,
                        "gender": "女"
                    }
                },
                "expected_intent": "exercise_action"
            },
            {
                "name": "缺失用户信息",
                "message": "背部怎么练",
                "user_info": {
                    "user_id": "test_user_3",
                    "user_profile": {}  # 空的用户档案
                },
                "expected_intent": "exercise_action"
            },
            {
                "name": "一般问候",
                "message": "你好",
                "user_info": {
                    "user_id": "test_user_4",
                    "user_profile": {}
                },
                "expected_intent": "general_chat"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}: {test_case['name']}")
            print(f"   消息: {test_case['message']}")
            
            start_time = time.time()
            
            # 执行测试
            result = await enhanced_exercise_graph.process_message(
                message=test_case["message"],
                conversation_id=f"test_conv_{i}",
                user_info=test_case["user_info"]
            )
            
            response_time = time.time() - start_time
            
            # 分析结果
            success = result.get("success", False)
            response = result.get("response", "")
            intent = result.get("intent_type", "unknown")
            confidence = result.get("confidence", 0.0)
            structured_data = result.get("structured_data", {})
            
            # 检查是否符合预期
            intent_match = intent == test_case["expected_intent"]
            has_meaningful_response = len(response) > 50
            
            test_result = {
                "name": test_case["name"],
                "success": success,
                "intent_match": intent_match,
                "has_meaningful_response": has_meaningful_response,
                "response_length": len(response),
                "response_time": response_time,
                "intent": intent,
                "confidence": confidence,
                "structured_data_keys": list(structured_data.keys()) if structured_data else []
            }
            
            # 显示结果
            status = "✅" if success and intent_match and has_meaningful_response else "⚠️"
            print(f"   {status} 状态: {'成功' if success else '失败'}")
            print(f"   意图: {intent} ({'✅' if intent_match else '❌'} 预期: {test_case['expected_intent']})")
            print(f"   置信度: {confidence:.2f}")
            print(f"   响应长度: {len(response)} 字符")
            print(f"   响应时间: {response_time:.2f}s")
            print(f"   响应预览: {response[:100]}...")
            
            if structured_data:
                print(f"   结构化数据: {list(structured_data.keys())}")
            
            results.append(test_result)
            
            # 短暂延迟
            await asyncio.sleep(1)
        
        # 生成测试报告
        print("\n" + "="*60)
        print("📊 测试结果汇总")
        print("="*60)
        
        total_tests = len(results)
        successful_tests = len([r for r in results if r["success"]])
        intent_matches = len([r for r in results if r["intent_match"]])
        meaningful_responses = len([r for r in results if r["has_meaningful_response"]])
        
        print(f"总测试数: {total_tests}")
        print(f"成功执行: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
        print(f"意图匹配: {intent_matches}/{total_tests} ({intent_matches/total_tests*100:.1f}%)")
        print(f"有意义响应: {meaningful_responses}/{total_tests} ({meaningful_responses/total_tests*100:.1f}%)")
        
        avg_response_time = sum(r["response_time"] for r in results) / len(results)
        print(f"平均响应时间: {avg_response_time:.2f}s")
        
        # 详细结果
        print("\n📋 详细结果:")
        for result in results:
            status = "✅" if result["success"] and result["intent_match"] and result["has_meaningful_response"] else "❌"
            print(f"  {status} {result['name']}: {result['intent']} ({result['confidence']:.2f})")
        
        # 判断整体成功
        overall_success = (
            successful_tests == total_tests and
            intent_matches >= total_tests * 0.8 and  # 80%的意图匹配率
            meaningful_responses >= total_tests * 0.8  # 80%的有意义响应率
        )
        
        if overall_success:
            print("\n🎉 增强版运动动作处理图测试成功！")
            print("✅ 所有核心功能正常工作")
            print("✅ 意图识别准确")
            print("✅ 响应内容有意义")
        else:
            print("\n⚠️ 增强版运动动作处理图测试部分成功")
            print("需要进一步优化的方面:")
            if successful_tests < total_tests:
                print(f"  - 执行成功率: {successful_tests}/{total_tests}")
            if intent_matches < total_tests * 0.8:
                print(f"  - 意图匹配率: {intent_matches}/{total_tests}")
            if meaningful_responses < total_tests * 0.8:
                print(f"  - 有意义响应率: {meaningful_responses}/{total_tests}")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_multi_turn_conversation():
    """测试多轮对话功能"""
    print("\n🔄 测试多轮对话功能...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import enhanced_exercise_graph
        
        conversation_id = "multi_turn_test"
        user_info = {
            "user_id": "test_user_multi",
            "user_profile": {}  # 空档案，触发用户信息收集
        }
        
        # 第一轮：初始查询
        print("\n📝 第一轮: 初始查询")
        result1 = await enhanced_exercise_graph.process_message(
            message="胸肌怎么练",
            conversation_id=conversation_id,
            user_info=user_info
        )
        
        print(f"响应: {result1.get('response', '')[:200]}...")
        
        # 检查是否询问用户信息
        response1 = result1.get('response', '')
        if "性别" in response1 or "年龄" in response1 or "健身水平" in response1:
            print("✅ 系统正确询问用户信息")
            
            # 第二轮：提供用户信息
            print("\n📝 第二轮: 提供用户信息")
            result2 = await enhanced_exercise_graph.continue_conversation(
                message="男，25岁",
                conversation_id=conversation_id,
                user_info=user_info
            )
            
            print(f"响应: {result2.get('response', '')[:200]}...")
            
            # 第三轮：继续提供信息
            print("\n📝 第三轮: 继续提供信息")
            result3 = await enhanced_exercise_graph.continue_conversation(
                message="初级水平，想要增肌",
                conversation_id=conversation_id,
                user_info=user_info
            )
            
            print(f"响应: {result3.get('response', '')[:200]}...")
            
            print("✅ 多轮对话测试完成")
            return True
        else:
            print("⚠️ 系统没有询问用户信息，可能用户档案已完整")
            return True
            
    except Exception as e:
        print(f"❌ 多轮对话测试失败: {str(e)}")
        return False

async def test_integration_with_gradio():
    """测试与Gradio界面的集成"""
    print("\n🔗 测试与Gradio界面的集成...")
    
    try:
        # 测试基础图是否能正确路由到增强运动处理器
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        
        result = await basic_test_graph.process_message(
            message="胸肌怎么练",
            conversation_id="gradio_integration_test",
            user_info={"user_id": "gradio_test_user"}
        )
        
        success = result.get("success", False)
        processing_system = result.get("processing_info", {}).get("system", "unknown")
        response = result.get("response", "")
        
        print(f"集成测试结果:")
        print(f"  成功: {success}")
        print(f"  处理系统: {processing_system}")
        print(f"  响应长度: {len(response)} 字符")
        print(f"  响应预览: {response[:150]}...")
        
        # 检查是否正确路由到增强运动处理器
        if processing_system == "enhanced_exercise" and success:
            print("✅ Gradio集成测试成功")
            return True
        else:
            print("⚠️ Gradio集成测试需要优化")
            return False
            
    except Exception as e:
        print(f"❌ Gradio集成测试失败: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🚀 开始增强版运动动作处理图综合测试...")
    
    # 基础功能测试
    basic_success = await test_enhanced_exercise_graph()
    
    # 多轮对话测试
    multi_turn_success = await test_multi_turn_conversation()
    
    # Gradio集成测试
    integration_success = await test_integration_with_gradio()
    
    print("\n" + "="*60)
    print("🎯 综合测试结果")
    print("="*60)
    
    if basic_success:
        print("✅ 基础功能测试通过")
    else:
        print("❌ 基础功能测试失败")
    
    if multi_turn_success:
        print("✅ 多轮对话测试通过")
    else:
        print("❌ 多轮对话测试失败")
    
    if integration_success:
        print("✅ Gradio集成测试通过")
    else:
        print("❌ Gradio集成测试失败")
    
    overall_success = basic_success and multi_turn_success and integration_success
    
    if overall_success:
        print("\n🎉 所有测试通过！增强版运动动作处理图实现成功！")
        print("\n📱 现在可以在Gradio界面中测试:")
        print("  python tests/comprehensive/interactive/enhanced_gradio_test.py")
        print("  然后访问 http://localhost:7860")
        print("  输入'胸肌怎么练'测试完整流程")
    else:
        print("\n⚠️ 部分测试未通过，请检查上述问题")

if __name__ == "__main__":
    asyncio.run(main())
