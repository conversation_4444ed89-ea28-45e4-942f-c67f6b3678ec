#!/usr/bin/env python3
"""
最终验证测试

验证增强版运动动作处理图的核心功能是否按预期工作。
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

async def test_exercise_intent_recognition():
    """测试运动意图识别"""
    print("🔍 测试运动意图识别...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import exercise_intent_router_node
        from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
        from langchain_core.messages import HumanMessage
        
        # 创建测试状态
        state = StateUtils.create_initial_state(
            conversation_id="test_intent",
            user_id="test_user",
            message="胸肌怎么练"
        )
        
        # 添加用户消息
        human_message = HumanMessage(content="胸肌怎么练")
        state["messages"] = [human_message]
        
        # 执行路由节点
        result_state = await exercise_intent_router_node(state)
        
        # 检查结果
        routing_decision = result_state.get("routing_decision", {})
        route = routing_decision.get("route", "")
        confidence = routing_decision.get("confidence", 0.0)
        intent = result_state.get("intent", "")
        
        print(f"  路由: {route}")
        print(f"  意图: {intent}")
        print(f"  置信度: {confidence:.2f}")
        
        if route == "user_info_verification" and intent == "exercise_action" and confidence > 0.8:
            print("✅ 运动意图识别测试通过")
            return True
        else:
            print("❌ 运动意图识别测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 运动意图识别测试异常: {str(e)}")
        return False

async def test_user_info_verification():
    """测试用户信息验证"""
    print("\n🔍 测试用户信息验证...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import user_info_verification_node
        from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
        
        # 测试1: 空用户档案
        print("  测试1: 空用户档案")
        state1 = StateUtils.create_initial_state()
        state1["user_profile"] = {}
        state1["flow_state"] = {"stage": "user_info_verification"}
        
        result1 = await user_info_verification_node(state1)
        
        stage1 = result1.get("flow_state", {}).get("stage", "")
        response1 = result1.get("response_content", "")
        
        print(f"    阶段: {stage1}")
        print(f"    响应: {response1[:50]}...")
        
        if stage1 == "collecting_user_info" and "性别" in response1:
            print("    ✅ 空用户档案测试通过")
            test1_pass = True
        else:
            print("    ❌ 空用户档案测试失败")
            test1_pass = False
        
        # 测试2: 完整用户档案
        print("  测试2: 完整用户档案")
        state2 = StateUtils.create_initial_state()
        state2["user_profile"] = {
            "gender": "男",
            "age": 25,
            "height": 175,
            "weight": 70,
            "fitness_goal": "增肌",
            "fitness_level": "初级"
        }
        state2["flow_state"] = {"stage": "user_info_verification"}
        
        result2 = await user_info_verification_node(state2)
        
        stage2 = result2.get("flow_state", {}).get("stage", "")
        
        print(f"    阶段: {stage2}")
        
        if stage2 == "parameter_collection":
            print("    ✅ 完整用户档案测试通过")
            test2_pass = True
        else:
            print("    ❌ 完整用户档案测试失败")
            test2_pass = False
        
        return test1_pass and test2_pass
        
    except Exception as e:
        print(f"❌ 用户信息验证测试异常: {str(e)}")
        return False

async def test_parameter_collection():
    """测试参数收集"""
    print("\n🔍 测试参数收集...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import parameter_collection_node
        from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
        
        # 创建测试状态
        state = StateUtils.create_initial_state()
        state["flow_state"] = {
            "stage": "parameter_collection",
            "collected_params": {
                "body_part": "胸部"  # 已有身体部位
            }
        }
        
        result = await parameter_collection_node(state)
        
        stage = result.get("flow_state", {}).get("stage", "")
        response = result.get("response_content", "")
        
        print(f"  阶段: {stage}")
        print(f"  响应: {response[:100]}...")
        
        if stage == "collecting_training_params" and ("健身房" in response or "居家" in response):
            print("✅ 参数收集测试通过")
            return True
        else:
            print("❌ 参数收集测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 参数收集测试异常: {str(e)}")
        return False

async def test_database_query():
    """测试数据库查询"""
    print("\n🔍 测试数据库查询...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import database_query_node
        from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
        
        # 创建测试状态
        state = StateUtils.create_initial_state()
        state["training_params"] = {
            "body_part": "胸部",
            "scenario": "健身房"
        }
        state["flow_state"] = {"stage": "database_query"}
        
        result = await database_query_node(state)
        
        stage = result.get("flow_state", {}).get("stage", "")
        candidate_exercises = result.get("flow_state", {}).get("candidate_exercises", [])
        
        print(f"  阶段: {stage}")
        print(f"  候选动作数量: {len(candidate_exercises)}")
        
        if candidate_exercises:
            print(f"  第一个动作: {candidate_exercises[0].get('name', '未知')}")
        
        if stage == "ai_filtering" and len(candidate_exercises) > 0:
            print("✅ 数据库查询测试通过")
            return True
        else:
            print("❌ 数据库查询测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库查询测试异常: {str(e)}")
        return False

async def test_ai_filtering():
    """测试AI筛选"""
    print("\n🔍 测试AI筛选...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import ai_filtering_node
        from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
        
        # 创建测试状态
        state = StateUtils.create_initial_state()
        state["user_profile"] = {
            "fitness_level": "初级",
            "fitness_goal": "增肌"
        }
        state["training_params"] = {
            "body_part": "胸部",
            "scenario": "健身房"
        }
        state["flow_state"] = {
            "stage": "ai_filtering",
            "candidate_exercises": [
                {
                    "id": 1,
                    "name": "俯卧撑",
                    "level": 1,
                    "description": "基础胸肌训练"
                },
                {
                    "id": 2,
                    "name": "卧推",
                    "level": 2,
                    "description": "胸肌力量训练"
                }
            ]
        }
        
        result = await ai_filtering_node(state)
        
        stage = result.get("flow_state", {}).get("stage", "")
        filtered_exercises = result.get("flow_state", {}).get("filtered_exercises", [])
        
        print(f"  阶段: {stage}")
        print(f"  筛选后动作数量: {len(filtered_exercises)}")
        
        if filtered_exercises:
            first_exercise = filtered_exercises[0]
            print(f"  第一个动作: {first_exercise.get('name', '未知')}")
            print(f"  训练参数: {first_exercise.get('sets', 0)}组 × {first_exercise.get('reps', '0')}次")
        
        if stage == "response_generation" and len(filtered_exercises) > 0:
            print("✅ AI筛选测试通过")
            return True
        else:
            print("❌ AI筛选测试失败")
            return False
            
    except Exception as e:
        print(f"❌ AI筛选测试异常: {str(e)}")
        return False

async def test_response_generation():
    """测试响应生成"""
    print("\n🔍 测试响应生成...")
    
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph import response_generation_node
        from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
        
        # 创建测试状态
        state = StateUtils.create_initial_state()
        state["user_profile"] = {
            "fitness_level": "初级",
            "fitness_goal": "增肌",
            "name": "测试用户"
        }
        state["training_params"] = {
            "body_part": "胸部",
            "scenario": "健身房"
        }
        state["flow_state"] = {
            "stage": "response_generation",
            "filtered_exercises": [
                {
                    "id": 1,
                    "name": "俯卧撑",
                    "description": "基础胸肌训练动作",
                    "sets": 3,
                    "reps": "10-12",
                    "rest_seconds": 75,
                    "instructions": ["双手撑地", "身体保持直线", "推起至起始位置"],
                    "notes": "根据您的增肌目标定制"
                }
            ]
        }
        state["messages"] = []
        
        result = await response_generation_node(state)
        
        response = result.get("response_content", "")
        structured_data = result.get("structured_data", {})
        confidence = result.get("confidence", 0.0)
        
        print(f"  响应长度: {len(response)} 字符")
        print(f"  置信度: {confidence:.2f}")
        print(f"  结构化数据键: {list(structured_data.keys())}")
        print(f"  响应预览: {response[:150]}...")
        
        if len(response) > 100 and "俯卧撑" in response and confidence > 0.9:
            print("✅ 响应生成测试通过")
            return True
        else:
            print("❌ 响应生成测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 响应生成测试异常: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🚀 开始最终验证测试...")
    
    # 执行各个组件测试
    tests = [
        ("运动意图识别", test_exercise_intent_recognition),
        ("用户信息验证", test_user_info_verification),
        ("参数收集", test_parameter_collection),
        ("数据库查询", test_database_query),
        ("AI筛选", test_ai_filtering),
        ("响应生成", test_response_generation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 最终验证结果")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, passed in results:
        status = "✅" if passed else "❌"
        print(f"{status} {test_name}")
        if passed:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎉 最终验证成功！增强版运动动作处理图核心功能正常！")
        print("\n✅ 验证标准达成:")
        print("  - 正确识别运动动作意图")
        print("  - 智能验证用户信息完整性")
        print("  - 有序收集训练参数")
        print("  - 模拟数据库查询功能")
        print("  - AI筛选和个性化推荐")
        print("  - 生成结构化响应")
        
        print("\n🚀 系统已准备就绪，可以在Gradio界面中测试完整流程！")
    else:
        print(f"\n⚠️ 最终验证部分成功 (通过率: {success_rate:.1f}%)")
        print("需要进一步优化的组件:")
        for test_name, passed in results:
            if not passed:
                print(f"  - {test_name}")

if __name__ == "__main__":
    asyncio.run(main())
