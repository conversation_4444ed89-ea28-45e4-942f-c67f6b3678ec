#!/usr/bin/env python3
"""
Gradio测试修复验证脚本

验证修复后的Gradio测试系统是否能正确返回AI助手响应内容
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

from tests.comprehensive.interactive.enhanced_gradio_test import EnhancedGradioTestSystem


async def test_api_response_fix():
    """测试API响应修复效果"""
    print("🔍 测试Gradio API响应修复效果...")
    
    # 创建测试系统
    test_system = EnhancedGradioTestSystem()
    
    # 初始化系统
    print("🚀 初始化测试系统...")
    success, message = await test_system.initialize_system()
    if not success:
        print(f"❌ 系统初始化失败: {message}")
        return False
    
    print(f"✅ 系统初始化成功: {message}")
    
    # 测试健身相关问题
    test_cases = [
        "胸肌怎么练",
        "我想了解健身",
        "你好",
        "如何制定训练计划"
    ]
    
    results = []
    
    for i, test_message in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_message}")
        
        start_time = time.time()
        result = await test_system.send_message(test_message)
        response_time = time.time() - start_time
        
        if result["success"]:
            api_data = result.get("data", {})
            ai_response = (
                api_data.get("response") or 
                api_data.get("response_content") or 
                api_data.get("content") or
                "无响应内容"
            )
            
            intent = api_data.get("intent_type") or api_data.get("intent", "unknown")
            confidence = api_data.get("confidence", 0)
            
            # 检查是否仍然是状态机处理信息
            is_state_machine_info = (
                "状态机处理" in ai_response or 
                "处理完成" in ai_response or
                len(ai_response) < 50
            )
            
            test_result = {
                "message": test_message,
                "success": True,
                "response_length": len(ai_response),
                "response_preview": ai_response[:200] + "..." if len(ai_response) > 200 else ai_response,
                "intent": intent,
                "confidence": confidence,
                "response_time": response_time,
                "is_meaningful": not is_state_machine_info,
                "status": "✅ 有意义的AI响应" if not is_state_machine_info else "⚠️ 仍然是状态机信息"
            }
            
            print(f"  状态: {test_result['status']}")
            print(f"  意图: {intent} (置信度: {confidence:.2f})")
            print(f"  响应长度: {len(ai_response)} 字符")
            print(f"  响应预览: {ai_response[:100]}...")
            print(f"  响应时间: {response_time:.2f}s")
            
        else:
            test_result = {
                "message": test_message,
                "success": False,
                "error": result.get("error", "未知错误"),
                "response_time": response_time,
                "status": "❌ API调用失败"
            }
            
            print(f"  状态: {test_result['status']}")
            print(f"  错误: {test_result['error']}")
        
        results.append(test_result)
        
        # 短暂延迟
        await asyncio.sleep(1)
    
    # 生成测试报告
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    total_tests = len(results)
    successful_tests = len([r for r in results if r["success"]])
    meaningful_responses = len([r for r in results if r.get("is_meaningful", False)])
    
    print(f"总测试数: {total_tests}")
    print(f"成功调用: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"有意义响应: {meaningful_responses}/{total_tests} ({meaningful_responses/total_tests*100:.1f}%)")
    
    if meaningful_responses == total_tests:
        print("\n🎉 所有测试都返回了有意义的AI响应！修复成功！")
        return True
    elif meaningful_responses > 0:
        print(f"\n⚠️ 部分测试返回了有意义的响应，但仍有 {total_tests - meaningful_responses} 个测试需要改进")
        return False
    else:
        print("\n❌ 所有测试仍然返回状态机处理信息，修复未成功")
        return False


async def test_debug_function():
    """测试调试功能"""
    print("\n🔧 测试调试功能...")
    
    test_system = EnhancedGradioTestSystem()
    
    # 初始化系统
    success, message = await test_system.initialize_system()
    if not success:
        print(f"❌ 系统初始化失败: {message}")
        return False
    
    # 测试调试功能
    test_message = "胸肌怎么练"
    print(f"🔍 调试API响应，测试消息: {test_message}")
    
    result = await test_system.send_message(test_message)
    
    debug_info = []
    debug_info.append(f"🔍 API响应调试信息")
    debug_info.append(f"测试消息: {test_message}")
    debug_info.append(f"API调用成功: {result['success']}")
    
    if result["success"]:
        api_data = result.get("data", {})
        debug_info.append(f"API数据键: {list(api_data.keys())}")
        
        # 检查所有可能的响应字段
        response_fields = ["response", "response_content", "content"]
        for field in response_fields:
            value = api_data.get(field)
            if value:
                debug_info.append(f"{field}: {value[:200]}...")
        
        # 检查meta_info
        meta_info = api_data.get("meta_info", {})
        if isinstance(meta_info, dict):
            debug_info.append(f"meta_info键: {list(meta_info.keys())}")
            for key, value in meta_info.items():
                if isinstance(value, str) and len(value) > 10:
                    debug_info.append(f"meta_info.{key}: {str(value)[:200]}...")
        
        # 检查意图和置信度
        debug_info.append(f"意图: {api_data.get('intent_type', 'unknown')}")
        debug_info.append(f"置信度: {api_data.get('confidence', 0)}")
        
        # 检查当前状态
        debug_info.append(f"当前状态: {api_data.get('current_state', 'unknown')}")
        
    else:
        debug_info.append(f"API错误: {result.get('error', '未知错误')}")
    
    print("\n".join(debug_info))
    return True


async def main():
    """主函数"""
    print("🚀 开始Gradio测试修复验证...")
    
    try:
        # 测试API响应修复
        api_fix_success = await test_api_response_fix()
        
        # 测试调试功能
        debug_success = await test_debug_function()
        
        print("\n" + "="*60)
        print("🎯 验证结果总结")
        print("="*60)
        
        if api_fix_success:
            print("✅ API响应修复成功 - 系统现在返回有意义的AI助手响应")
        else:
            print("⚠️ API响应修复部分成功 - 仍需进一步优化")
        
        if debug_success:
            print("✅ 调试功能正常工作")
        else:
            print("❌ 调试功能存在问题")
        
        if api_fix_success and debug_success:
            print("\n🎉 Gradio测试系统修复完成！现在可以正常使用了。")
            print("\n📱 启动方式:")
            print("  python tests/comprehensive/interactive/enhanced_gradio_test.py")
            print("  然后访问 http://localhost:7860")
        else:
            print("\n⚠️ 修复未完全成功，请检查上述问题。")
            
    except Exception as e:
        print(f"\n❌ 验证过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
