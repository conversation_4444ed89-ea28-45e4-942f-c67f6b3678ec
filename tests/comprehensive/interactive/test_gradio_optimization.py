#!/usr/bin/env python3
"""
Gradio测试应用优化验证脚本

验证优化后的Gradio测试应用是否正确使用生产API进行测试
"""

import asyncio
import sys
import os
import time
import httpx
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

from tests.comprehensive.interactive.gradio_test_app import GradioTestApp, ChatAPIClient


class GradioOptimizationValidator:
    """Gradio优化验证器"""
    
    def __init__(self):
        self.api_client = ChatAPIClient()
        self.test_results = []
        
    async def validate_production_api_integration(self) -> Dict[str, Any]:
        """验证生产API集成"""
        print("🔍 验证生产API集成...")
        
        test_message = "你好，我想了解健身"
        session_id = f"validation_test_{int(time.time())}"
        
        try:
            # 测试API调用
            result = await self.api_client.send_message(
                message=test_message,
                session_id=session_id,
                user_id=15
            )
            
            if result["success"]:
                api_data = result["data"]
                return {
                    "status": "success",
                    "api_call": "✅ 成功",
                    "response_time": result.get("response_time", 0),
                    "status_code": result["status_code"],
                    "intent": api_data.get("intent_type", "unknown"),
                    "confidence": api_data.get("confidence", 0),
                    "response_length": len(api_data.get("response", "")),
                    "message": "生产API集成正常"
                }
            else:
                return {
                    "status": "failed",
                    "api_call": "❌ 失败",
                    "error": result["error"],
                    "status_code": result.get("status_code", 500),
                    "message": f"API调用失败: {result['error']}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "api_call": "❌ 异常",
                "error": str(e),
                "message": f"API调用异常: {str(e)}"
            }
    
    async def validate_scenario_test_optimization(self) -> Dict[str, Any]:
        """验证场景测试优化"""
        print("🎯 验证场景测试优化...")
        
        try:
            # 创建测试应用实例
            app = GradioTestApp()
            
            # 初始化应用
            await app.initialize()
            
            if not app.setup_complete:
                return {
                    "status": "failed",
                    "message": "应用初始化失败",
                    "setup_complete": False
                }
            
            # 验证测试环境
            env_checks = app._validate_test_environment()
            
            # 测试场景测试方法
            test_scenarios = list(app.test_scenarios.keys())
            if not test_scenarios:
                return {
                    "status": "failed",
                    "message": "没有可用的测试场景",
                    "available_scenarios": 0
                }
            
            # 选择第一个场景进行测试
            scenario_name = test_scenarios[0]
            
            # 模拟进度回调
            class MockProgress:
                def __call__(self, progress, desc=""):
                    print(f"进度: {progress:.1%} - {desc}")
            
            # 运行场景测试（只测试第一条消息）
            messages = app.test_scenarios[scenario_name]
            if messages:
                test_message = messages[0]
                session_id = f"scenario_validation_{int(time.time())}"
                
                start_time = time.time()
                result = await app.api_client.send_message(
                    message=test_message,
                    session_id=session_id,
                    user_id=app.test_user_id
                )
                response_time = time.time() - start_time
                
                return {
                    "status": "success",
                    "message": "场景测试优化验证成功",
                    "setup_complete": app.setup_complete,
                    "environment_checks": env_checks,
                    "available_scenarios": len(test_scenarios),
                    "test_scenario": scenario_name,
                    "test_message": test_message,
                    "api_success": result["success"],
                    "response_time": response_time,
                    "uses_production_api": True  # 确认使用生产API
                }
            else:
                return {
                    "status": "failed",
                    "message": "测试场景为空",
                    "scenario_name": scenario_name
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"场景测试验证异常: {str(e)}",
                "error": str(e)
            }
    
    async def validate_performance_tracking(self) -> Dict[str, Any]:
        """验证性能跟踪功能"""
        print("📊 验证性能跟踪功能...")
        
        try:
            app = GradioTestApp()
            await app.initialize()
            
            # 发送几条测试消息
            test_messages = [
                "你好",
                "我想了解健身",
                "请推荐训练计划"
            ]
            
            session_id = f"performance_test_{int(time.time())}"
            
            for i, message in enumerate(test_messages):
                result = await app.api_client.send_message(
                    message=message,
                    session_id=session_id,
                    user_id=app.test_user_id
                )
                
                # 手动跟踪性能（模拟应用内的跟踪）
                app._track_api_performance(result, message, session_id)
                
                # 短暂延迟
                await asyncio.sleep(0.5)
            
            # 获取统计信息
            stats = app.get_test_statistics()
            
            return {
                "status": "success",
                "message": "性能跟踪功能正常",
                "total_requests": stats["总请求数"],
                "success_rate": stats["成功率"],
                "avg_response_time": stats["平均响应时间"],
                "performance_metrics_count": len(app.performance_metrics),
                "tracking_enabled": True
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"性能跟踪验证异常: {str(e)}",
                "error": str(e)
            }
    
    async def run_validation(self) -> Dict[str, Any]:
        """运行完整验证"""
        print("🚀 开始Gradio优化验证...")
        
        results = {
            "validation_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "tests": {}
        }
        
        # 1. 验证生产API集成
        api_result = await self.validate_production_api_integration()
        results["tests"]["production_api"] = api_result
        
        # 2. 验证场景测试优化
        scenario_result = await self.validate_scenario_test_optimization()
        results["tests"]["scenario_optimization"] = scenario_result
        
        # 3. 验证性能跟踪
        performance_result = await self.validate_performance_tracking()
        results["tests"]["performance_tracking"] = performance_result
        
        # 计算总体状态
        all_success = all(
            test_result.get("status") == "success" 
            for test_result in results["tests"].values()
        )
        
        results["overall_status"] = "success" if all_success else "failed"
        results["summary"] = self._generate_summary(results["tests"])
        
        return results
    
    def _generate_summary(self, tests: Dict[str, Any]) -> str:
        """生成验证摘要"""
        summary_lines = ["📋 验证摘要:"]
        
        for test_name, test_result in tests.items():
            status_icon = "✅" if test_result.get("status") == "success" else "❌"
            test_display_name = {
                "production_api": "生产API集成",
                "scenario_optimization": "场景测试优化", 
                "performance_tracking": "性能跟踪"
            }.get(test_name, test_name)
            
            summary_lines.append(f"  {status_icon} {test_display_name}: {test_result.get('message', '未知')}")
        
        return "\n".join(summary_lines)


async def main():
    """主函数"""
    validator = GradioOptimizationValidator()
    
    try:
        results = await validator.run_validation()
        
        print("\n" + "="*60)
        print("🎯 Gradio测试应用优化验证结果")
        print("="*60)
        
        print(f"\n⏰ 验证时间: {results['validation_time']}")
        print(f"🏆 总体状态: {'✅ 成功' if results['overall_status'] == 'success' else '❌ 失败'}")
        
        print(f"\n{results['summary']}")
        
        print("\n📊 详细结果:")
        for test_name, test_result in results["tests"].items():
            print(f"\n🔍 {test_name}:")
            for key, value in test_result.items():
                if key != "message":
                    print(f"  {key}: {value}")
        
        print("\n" + "="*60)
        
        if results['overall_status'] == 'success':
            print("🎉 所有验证通过！Gradio测试应用已成功优化为使用生产API。")
        else:
            print("⚠️ 部分验证失败，请检查上述详细结果。")
            
    except Exception as e:
        print(f"\n❌ 验证过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
