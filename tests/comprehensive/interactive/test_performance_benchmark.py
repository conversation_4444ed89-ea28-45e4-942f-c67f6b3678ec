#!/usr/bin/env python3
"""
性能基准测试

测量平均响应时间、并发处理能力、内存使用和资源消耗
"""

import asyncio
import sys
import os
import time
import psutil
import statistics
from concurrent.futures import ThreadPoolExecutor
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.initial_memory
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            current_memory = self.process.memory_info().rss / 1024 / 1024
            self.peak_memory = max(self.peak_memory, current_memory)
            time.sleep(0.1)
    
    def get_memory_usage(self):
        """获取内存使用情况"""
        current_memory = self.process.memory_info().rss / 1024 / 1024
        return {
            "initial_mb": self.initial_memory,
            "current_mb": current_memory,
            "peak_mb": self.peak_memory,
            "increase_mb": current_memory - self.initial_memory
        }

async def test_response_time_benchmark():
    """测试平均响应时间基准"""
    print("⏱️ 测试平均响应时间基准...")
    
    try:
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        
        # 测试场景
        test_cases = [
            {
                "message": "胸肌怎么练",
                "user_info": {
                    "user_id": "15",
                    "user_profile": {
                        "gender": "男", "age": 25, "height": 175, "weight": 70,
                        "fitness_goal": "增肌", "fitness_level": "初级"
                    }
                }
            },
            {
                "message": "腹肌怎么练",
                "user_info": {
                    "user_id": "15",
                    "user_profile": {}
                }
            },
            {
                "message": "你好",
                "user_info": {
                    "user_id": "15",
                    "user_profile": {}
                }
            }
        ]
        
        # 预热
        print("  🔥 预热系统...")
        for case in test_cases:
            await basic_test_graph.process_message(
                message=case["message"],
                conversation_id="warmup",
                user_info=case["user_info"]
            )
        
        # 基准测试
        print("  📊 执行基准测试...")
        response_times = []
        
        for i in range(10):  # 10次测试
            for j, case in enumerate(test_cases):
                start_time = time.time()
                
                result = await basic_test_graph.process_message(
                    message=case["message"],
                    conversation_id=f"benchmark_{i}_{j}",
                    user_info=case["user_info"]
                )
                
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                if not result.get("success", False):
                    print(f"    ⚠️ 测试 {i}-{j} 失败")
        
        # 统计分析
        avg_time = statistics.mean(response_times)
        median_time = statistics.median(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        std_dev = statistics.stdev(response_times) if len(response_times) > 1 else 0
        
        # 性能评估
        performance_grade = "优秀" if avg_time < 0.5 else "良好" if avg_time < 1.0 else "一般" if avg_time < 2.0 else "需优化"
        target_met = avg_time < 1.0  # 目标：1秒内
        
        print(f"\n📊 响应时间基准测试结果:")
        print(f"  测试次数: {len(response_times)}")
        print(f"  平均响应时间: {avg_time:.3f}s")
        print(f"  中位数响应时间: {median_time:.3f}s")
        print(f"  最快响应时间: {min_time:.3f}s")
        print(f"  最慢响应时间: {max_time:.3f}s")
        print(f"  标准差: {std_dev:.3f}s")
        print(f"  性能评级: {performance_grade}")
        print(f"  目标达成: {'✅' if target_met else '❌'} (目标: <1.0s)")
        
        return {
            "success": True,
            "avg_time": avg_time,
            "target_met": target_met,
            "performance_grade": performance_grade,
            "stats": {
                "mean": avg_time,
                "median": median_time,
                "min": min_time,
                "max": max_time,
                "std_dev": std_dev
            }
        }
        
    except Exception as e:
        print(f"❌ 响应时间基准测试异常: {str(e)}")
        return {"success": False, "error": str(e)}

async def test_concurrent_processing():
    """测试并发处理能力"""
    print("\n🔄 测试并发处理能力...")
    
    try:
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        
        # 并发测试参数
        concurrent_levels = [1, 3, 5, 10]  # 不同并发级别
        
        results = {}
        
        for concurrent_count in concurrent_levels:
            print(f"\n  📊 测试并发级别: {concurrent_count}")
            
            # 创建并发任务
            tasks = []
            for i in range(concurrent_count):
                task = basic_test_graph.process_message(
                    message="胸肌怎么练",
                    conversation_id=f"concurrent_{concurrent_count}_{i}",
                    user_info={
                        "user_id": "15",
                        "user_profile": {
                            "gender": "男", "age": 25, "height": 175, "weight": 70,
                            "fitness_goal": "增肌", "fitness_level": "初级"
                        }
                    }
                )
                tasks.append(task)
            
            # 执行并发测试
            start_time = time.time()
            concurrent_results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            # 分析结果
            successful_requests = 0
            failed_requests = 0
            
            for result in concurrent_results:
                if isinstance(result, Exception):
                    failed_requests += 1
                elif result.get("success", False):
                    successful_requests += 1
                else:
                    failed_requests += 1
            
            success_rate = (successful_requests / concurrent_count) * 100
            avg_response_time = total_time / concurrent_count
            throughput = concurrent_count / total_time  # 请求/秒
            
            results[concurrent_count] = {
                "total_time": total_time,
                "success_rate": success_rate,
                "avg_response_time": avg_response_time,
                "throughput": throughput,
                "successful": successful_requests,
                "failed": failed_requests
            }
            
            print(f"    总时间: {total_time:.2f}s")
            print(f"    成功率: {success_rate:.1f}%")
            print(f"    平均响应时间: {avg_response_time:.3f}s")
            print(f"    吞吐量: {throughput:.2f} 请求/秒")
        
        # 评估并发性能
        max_concurrent = max([level for level, result in results.items() if result["success_rate"] >= 90])
        concurrent_performance = "优秀" if max_concurrent >= 10 else "良好" if max_concurrent >= 5 else "一般"
        
        print(f"\n📊 并发处理能力测试结果:")
        print(f"  最大稳定并发: {max_concurrent} (成功率≥90%)")
        print(f"  并发性能评级: {concurrent_performance}")
        
        return {
            "success": True,
            "max_concurrent": max_concurrent,
            "performance_grade": concurrent_performance,
            "detailed_results": results
        }
        
    except Exception as e:
        print(f"❌ 并发处理能力测试异常: {str(e)}")
        return {"success": False, "error": str(e)}

async def test_memory_usage():
    """测试内存使用和资源消耗"""
    print("\n💾 测试内存使用和资源消耗...")
    
    try:
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        
        # 启动性能监控
        monitor = PerformanceMonitor()
        monitor.start_monitoring()
        
        initial_memory = monitor.get_memory_usage()
        print(f"  初始内存使用: {initial_memory['current_mb']:.1f} MB")
        
        # 执行大量请求来测试内存使用
        print("  🔄 执行内存压力测试...")
        
        for i in range(50):  # 50次请求
            await basic_test_graph.process_message(
                message=f"胸肌怎么练 {i}",
                conversation_id=f"memory_test_{i}",
                user_info={
                    "user_id": "15",
                    "user_profile": {
                        "gender": "男", "age": 25, "height": 175, "weight": 70,
                        "fitness_goal": "增肌", "fitness_level": "初级"
                    }
                }
            )
            
            if i % 10 == 0:
                current_memory = monitor.get_memory_usage()
                print(f"    请求 {i}: {current_memory['current_mb']:.1f} MB")
        
        # 停止监控并获取最终结果
        monitor.stop_monitoring()
        final_memory = monitor.get_memory_usage()
        
        # 内存使用分析
        memory_increase = final_memory["increase_mb"]
        peak_memory = final_memory["peak_mb"]
        memory_efficiency = "优秀" if memory_increase < 50 else "良好" if memory_increase < 100 else "需优化"
        
        print(f"\n📊 内存使用测试结果:")
        print(f"  初始内存: {final_memory['initial_mb']:.1f} MB")
        print(f"  最终内存: {final_memory['current_mb']:.1f} MB")
        print(f"  峰值内存: {peak_memory:.1f} MB")
        print(f"  内存增长: {memory_increase:.1f} MB")
        print(f"  内存效率评级: {memory_efficiency}")
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"  当前CPU使用率: {cpu_percent:.1f}%")
        
        return {
            "success": True,
            "memory_increase_mb": memory_increase,
            "peak_memory_mb": peak_memory,
            "memory_efficiency": memory_efficiency,
            "cpu_percent": cpu_percent
        }
        
    except Exception as e:
        print(f"❌ 内存使用测试异常: {str(e)}")
        return {"success": False, "error": str(e)}

async def main():
    """主函数"""
    print("🚀 开始性能基准测试...")
    
    # 执行所有性能测试
    tests = [
        ("响应时间基准", test_response_time_benchmark),
        ("并发处理能力", test_concurrent_processing),
        ("内存使用和资源消耗", test_memory_usage)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results[test_name] = {"success": False, "error": str(e)}
    
    # 生成性能报告
    print("\n" + "="*60)
    print("📊 性能基准测试总结")
    print("="*60)
    
    # 响应时间评估
    response_result = results.get("响应时间基准", {})
    if response_result.get("success"):
        avg_time = response_result.get("avg_time", 0)
        target_met = response_result.get("target_met", False)
        print(f"✅ 响应时间: {avg_time:.3f}s ({'达标' if target_met else '未达标'})")
    else:
        print("❌ 响应时间测试失败")
    
    # 并发能力评估
    concurrent_result = results.get("并发处理能力", {})
    if concurrent_result.get("success"):
        max_concurrent = concurrent_result.get("max_concurrent", 0)
        print(f"✅ 最大并发: {max_concurrent} 请求")
    else:
        print("❌ 并发处理测试失败")
    
    # 内存使用评估
    memory_result = results.get("内存使用和资源消耗", {})
    if memory_result.get("success"):
        memory_increase = memory_result.get("memory_increase_mb", 0)
        efficiency = memory_result.get("memory_efficiency", "未知")
        print(f"✅ 内存增长: {memory_increase:.1f}MB ({efficiency})")
    else:
        print("❌ 内存使用测试失败")
    
    # 总体性能评级
    successful_tests = len([r for r in results.values() if r.get("success")])
    total_tests = len(results)
    
    if successful_tests == total_tests:
        if (response_result.get("target_met", False) and 
            concurrent_result.get("max_concurrent", 0) >= 5 and
            memory_result.get("memory_increase_mb", 999) < 100):
            print("\n🎉 性能基准测试: 优秀")
            print("✅ 系统性能完全满足生产环境要求")
        else:
            print("\n✅ 性能基准测试: 良好")
            print("✅ 系统性能基本满足生产环境要求")
    else:
        print(f"\n⚠️ 性能基准测试: 部分通过 ({successful_tests}/{total_tests})")
    
    print(f"\n📋 性能优化建议:")
    if not response_result.get("target_met", True):
        print("  - 优化响应时间，目标<1秒")
    if concurrent_result.get("max_concurrent", 10) < 5:
        print("  - 提高并发处理能力")
    if memory_result.get("memory_increase_mb", 0) > 100:
        print("  - 优化内存使用，减少内存泄漏")

if __name__ == "__main__":
    asyncio.run(main())
