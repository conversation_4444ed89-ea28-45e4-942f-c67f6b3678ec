#!/usr/bin/env python3
"""
生产环境就绪测试

验证重构后的增强版运动动作处理图是否已准备好部署到生产环境
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

async def test_production_readiness():
    """测试生产环境就绪状态"""
    print("🚀 开始生产环境就绪测试...")
    
    try:
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        
        # 模拟真实用户场景
        test_scenarios = [
            {
                "name": "新用户首次查询",
                "message": "胸肌怎么练",
                "user_info": {
                    "user_id": "15",  # 使用真实用户ID
                    "user_profile": {}  # 空档案，触发信息收集
                },
                "expected_flow": "user_info_collection"
            },
            {
                "name": "老用户完整档案查询",
                "message": "腹肌怎么练",
                "user_info": {
                    "user_id": "15",
                    "user_profile": {
                        "gender": "男",
                        "age": 28,
                        "height": 175,
                        "weight": 70,
                        "fitness_goal": "增肌",
                        "fitness_level": "中级"
                    }
                },
                "expected_flow": "direct_recommendation"
            },
            {
                "name": "非运动查询处理",
                "message": "今天天气怎么样",
                "user_info": {
                    "user_id": "15",
                    "user_profile": {}
                },
                "expected_flow": "general_chat"
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📝 场景 {i}: {scenario['name']}")
            print(f"   用户消息: {scenario['message']}")
            
            start_time = time.time()
            
            # 执行测试
            result = await basic_test_graph.process_message(
                message=scenario["message"],
                conversation_id=f"prod_test_{i}",
                user_info=scenario["user_info"]
            )
            
            response_time = time.time() - start_time
            
            # 分析结果
            success = result.get("success", False)
            intent = result.get("intent_type", "unknown")
            confidence = result.get("confidence", 0.0)
            response = result.get("response", "")
            processing_system = result.get("processing_info", {}).get("system", "unknown")
            
            # 验证预期流程
            flow_correct = False
            if scenario["expected_flow"] == "user_info_collection":
                flow_correct = "性别" in response or "年龄" in response or "身高" in response
            elif scenario["expected_flow"] == "direct_recommendation":
                flow_correct = "健身房" in response or "居家" in response or len(response) > 100
            elif scenario["expected_flow"] == "general_chat":
                flow_correct = intent == "general_chat"
            
            # 性能检查
            performance_ok = response_time < 2.0  # 2秒内响应
            
            scenario_result = {
                "name": scenario["name"],
                "success": success,
                "intent_correct": intent in ["exercise_action", "general_chat"],
                "flow_correct": flow_correct,
                "performance_ok": performance_ok,
                "response_time": response_time,
                "confidence": confidence,
                "processing_system": processing_system,
                "response_length": len(response)
            }
            
            # 显示结果
            status = "✅" if all([success, scenario_result["intent_correct"], flow_correct, performance_ok]) else "⚠️"
            print(f"   {status} 状态: {'成功' if success else '失败'}")
            print(f"   意图: {intent} ({'✅' if scenario_result['intent_correct'] else '❌'})")
            print(f"   流程: {'✅' if flow_correct else '❌'} ({scenario['expected_flow']})")
            print(f"   性能: {'✅' if performance_ok else '❌'} ({response_time:.2f}s)")
            print(f"   处理系统: {processing_system}")
            print(f"   响应长度: {len(response)} 字符")
            print(f"   响应预览: {response[:100]}...")
            
            results.append(scenario_result)
        
        # 生成生产就绪报告
        print("\n" + "="*60)
        print("📊 生产环境就绪评估")
        print("="*60)
        
        total_scenarios = len(results)
        successful_scenarios = len([r for r in results if r["success"]])
        correct_intents = len([r for r in results if r["intent_correct"]])
        correct_flows = len([r for r in results if r["flow_correct"]])
        good_performance = len([r for r in results if r["performance_ok"]])
        
        print(f"场景执行成功率: {successful_scenarios}/{total_scenarios} ({successful_scenarios/total_scenarios*100:.1f}%)")
        print(f"意图识别准确率: {correct_intents}/{total_scenarios} ({correct_intents/total_scenarios*100:.1f}%)")
        print(f"流程处理正确率: {correct_flows}/{total_scenarios} ({correct_flows/total_scenarios*100:.1f}%)")
        print(f"性能达标率: {good_performance}/{total_scenarios} ({good_performance/total_scenarios*100:.1f}%)")
        
        avg_response_time = sum(r["response_time"] for r in results) / len(results)
        print(f"平均响应时间: {avg_response_time:.2f}s")
        
        # 生产就绪标准
        production_ready_criteria = [
            successful_scenarios >= total_scenarios * 0.8,  # 80%成功率
            correct_intents >= total_scenarios * 0.9,       # 90%意图准确率
            correct_flows >= total_scenarios * 0.8,         # 80%流程正确率
            good_performance >= total_scenarios * 0.9,      # 90%性能达标
            avg_response_time < 1.5                         # 平均响应时间<1.5s
        ]
        
        production_ready = all(production_ready_criteria)
        
        print(f"\n🎯 生产就绪标准检查:")
        print(f"  ✅ 成功率 ≥ 80%: {'✅' if production_ready_criteria[0] else '❌'}")
        print(f"  ✅ 意图准确率 ≥ 90%: {'✅' if production_ready_criteria[1] else '❌'}")
        print(f"  ✅ 流程正确率 ≥ 80%: {'✅' if production_ready_criteria[2] else '❌'}")
        print(f"  ✅ 性能达标率 ≥ 90%: {'✅' if production_ready_criteria[3] else '❌'}")
        print(f"  ✅ 平均响应时间 < 1.5s: {'✅' if production_ready_criteria[4] else '❌'}")
        
        if production_ready:
            print("\n🎉 生产环境就绪测试通过！")
            print("✅ 系统已满足生产环境部署标准")
            print("✅ 核心功能稳定可靠")
            print("✅ 性能指标达标")
            print("✅ 用户体验良好")
            
            print("\n🚀 可以安全部署到生产环境！")
            print("\n📋 部署建议:")
            print("  1. 配置生产环境数据库连接")
            print("  2. 设置监控和日志收集")
            print("  3. 配置负载均衡和容错机制")
            print("  4. 准备回滚方案")
            print("  5. 进行灰度发布测试")
        else:
            print("\n⚠️ 生产环境就绪测试部分通过")
            print("建议优化以下方面后再部署:")
            
            if not production_ready_criteria[0]:
                print("  - 提高系统成功率")
            if not production_ready_criteria[1]:
                print("  - 优化意图识别准确率")
            if not production_ready_criteria[2]:
                print("  - 完善流程处理逻辑")
            if not production_ready_criteria[3]:
                print("  - 优化系统性能")
            if not production_ready_criteria[4]:
                print("  - 减少响应时间")
        
        return production_ready
        
    except Exception as e:
        print(f"❌ 生产环境就绪测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_system_health():
    """测试系统健康状态"""
    print("\n🏥 系统健康检查...")
    
    health_checks = []
    
    # 检查1: 模块导入
    try:
        from app.services.ai_assistant.langgraph.enhanced_exercise_graph_refactored import enhanced_exercise_graph_refactored
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        health_checks.append(("模块导入", True, "所有核心模块导入成功"))
    except Exception as e:
        health_checks.append(("模块导入", False, f"模块导入失败: {str(e)}"))
    
    # 检查2: 数据库连接
    try:
        from app.db.session import get_db
        db = next(get_db())
        health_checks.append(("数据库连接", True, "数据库连接正常"))
    except Exception as e:
        health_checks.append(("数据库连接", False, f"数据库连接失败: {str(e)}"))
    
    # 检查3: LLM服务
    try:
        from app.services.llm_proxy_service import LLMProxyService
        llm_service = LLMProxyService()
        health_checks.append(("LLM服务", True, "LLM服务初始化成功"))
    except Exception as e:
        health_checks.append(("LLM服务", False, f"LLM服务初始化失败: {str(e)}"))
    
    # 显示健康检查结果
    print("\n📋 系统健康检查结果:")
    healthy_count = 0
    for check_name, is_healthy, message in health_checks:
        status = "✅" if is_healthy else "❌"
        print(f"  {status} {check_name}: {message}")
        if is_healthy:
            healthy_count += 1
    
    health_score = (healthy_count / len(health_checks)) * 100
    print(f"\n💯 系统健康评分: {health_score:.1f}%")
    
    return health_score >= 80

async def main():
    """主函数"""
    print("🚀 开始生产环境就绪综合测试...")
    
    # 系统健康检查
    health_ok = await test_system_health()
    
    # 生产就绪测试
    production_ready = await test_production_readiness()
    
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    
    if health_ok:
        print("✅ 系统健康状态良好")
    else:
        print("❌ 系统健康状态需要关注")
    
    if production_ready:
        print("✅ 生产环境部署就绪")
    else:
        print("⚠️ 生产环境部署需要优化")
    
    overall_ready = health_ok and production_ready
    
    if overall_ready:
        print("\n🎉 系统已完全准备好部署到生产环境！")
        print("\n🚀 下一步行动:")
        print("  1. 准备生产环境配置")
        print("  2. 执行部署流程")
        print("  3. 监控系统运行状态")
        print("  4. 收集用户反馈")
    else:
        print("\n⚠️ 建议完成优化后再部署到生产环境")

if __name__ == "__main__":
    asyncio.run(main())
