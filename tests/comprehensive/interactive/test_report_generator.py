"""
测试报告生成器

为Gradio测试系统生成详细的测试报告，包含性能分析、问题诊断和改进建议。
"""

import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import plotly.graph_objects as go
import plotly.express as px
from dataclasses import dataclass


@dataclass
class TestReport:
    """测试报告数据结构"""
    title: str
    generated_at: datetime
    test_duration: timedelta
    total_requests: int
    success_rate: float
    avg_response_time: float
    performance_metrics: List[Dict[str, Any]]
    conversation_history: List[Dict[str, Any]]
    environment_info: Dict[str, Any]
    issues: List[Dict[str, Any]]
    recommendations: List[str]


class TestReportGenerator:
    """测试报告生成器"""
    
    def __init__(self, output_dir: str = "docs/agent/tests"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_report(
        self,
        performance_metrics: List[Dict[str, Any]],
        conversation_history: List[Dict[str, Any]],
        environment_info: Dict[str, Any],
        test_sessions: Dict[str, Any],
        report_title: str = "Gradio测试系统执行报告"
    ) -> TestReport:
        """生成测试报告"""
        
        # 计算基础统计
        total_requests = len(performance_metrics)
        if total_requests == 0:
            return self._generate_empty_report(report_title, environment_info)
        
        df = pd.DataFrame(performance_metrics)
        success_count = len(df[df['success'] == True])
        success_rate = (success_count / total_requests) * 100
        avg_response_time = df['response_time'].mean()
        
        # 计算测试持续时间
        if conversation_history:
            start_time = min(record['timestamp'] for record in conversation_history)
            end_time = max(record['timestamp'] for record in conversation_history)
            test_duration = end_time - start_time
        else:
            test_duration = timedelta(0)
        
        # 分析问题
        issues = self._analyze_issues(df, conversation_history)
        
        # 生成建议
        recommendations = self._generate_recommendations(df, issues, environment_info)
        
        report = TestReport(
            title=report_title,
            generated_at=datetime.now(),
            test_duration=test_duration,
            total_requests=total_requests,
            success_rate=success_rate,
            avg_response_time=avg_response_time,
            performance_metrics=performance_metrics,
            conversation_history=conversation_history,
            environment_info=environment_info,
            issues=issues,
            recommendations=recommendations
        )
        
        return report
    
    def _generate_empty_report(self, title: str, environment_info: Dict[str, Any]) -> TestReport:
        """生成空报告"""
        return TestReport(
            title=title,
            generated_at=datetime.now(),
            test_duration=timedelta(0),
            total_requests=0,
            success_rate=0.0,
            avg_response_time=0.0,
            performance_metrics=[],
            conversation_history=[],
            environment_info=environment_info,
            issues=[{"type": "warning", "message": "无测试数据", "severity": "low"}],
            recommendations=["请先运行一些测试以生成有意义的报告"]
        )
    
    def _analyze_issues(self, df: pd.DataFrame, conversation_history: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析问题"""
        issues = []
        
        if df.empty:
            return issues
        
        # 分析响应时间问题
        slow_requests = df[df['response_time'] > 5.0]
        if len(slow_requests) > 0:
            issues.append({
                "type": "performance",
                "message": f"发现 {len(slow_requests)} 个慢请求（>5秒）",
                "severity": "medium",
                "details": {
                    "slow_request_count": len(slow_requests),
                    "avg_slow_time": slow_requests['response_time'].mean(),
                    "max_slow_time": slow_requests['response_time'].max()
                }
            })
        
        # 分析失败请求
        failed_requests = df[df['success'] == False]
        if len(failed_requests) > 0:
            failure_rate = len(failed_requests) / len(df) * 100
            severity = "high" if failure_rate > 20 else "medium" if failure_rate > 10 else "low"
            issues.append({
                "type": "reliability",
                "message": f"请求失败率: {failure_rate:.1f}% ({len(failed_requests)}/{len(df)})",
                "severity": severity,
                "details": {
                    "failed_request_count": len(failed_requests),
                    "failure_rate": failure_rate
                }
            })
        
        # 分析置信度问题
        success_df = df[df['success'] == True]
        if not success_df.empty and 'confidence' in success_df.columns:
            low_confidence = success_df[success_df['confidence'] < 0.6]
            if len(low_confidence) > 0:
                issues.append({
                    "type": "quality",
                    "message": f"发现 {len(low_confidence)} 个低置信度响应（<0.6）",
                    "severity": "medium",
                    "details": {
                        "low_confidence_count": len(low_confidence),
                        "avg_confidence": low_confidence['confidence'].mean()
                    }
                })
        
        # 分析意图识别问题
        if 'intent' in df.columns:
            unknown_intents = df[df['intent'] == 'unknown']
            if len(unknown_intents) > 0:
                issues.append({
                    "type": "intent_recognition",
                    "message": f"发现 {len(unknown_intents)} 个未识别意图",
                    "severity": "medium",
                    "details": {
                        "unknown_intent_count": len(unknown_intents)
                    }
                })
        
        return issues
    
    def _generate_recommendations(
        self, 
        df: pd.DataFrame, 
        issues: List[Dict[str, Any]], 
        environment_info: Dict[str, Any]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于问题生成建议
        for issue in issues:
            if issue["type"] == "performance":
                recommendations.append("优化API响应时间：检查数据库查询、缓存策略和网络延迟")
                recommendations.append("考虑增加服务器资源或使用负载均衡")
            
            elif issue["type"] == "reliability":
                recommendations.append("提高系统稳定性：检查错误日志，修复导致失败的根本原因")
                recommendations.append("实施重试机制和熔断器模式")
            
            elif issue["type"] == "quality":
                recommendations.append("改进AI模型质量：优化提示词，增加训练数据")
                recommendations.append("实施置信度阈值过滤机制")
            
            elif issue["type"] == "intent_recognition":
                recommendations.append("优化意图识别：扩展意图训练数据，改进NLU模型")
                recommendations.append("添加意图澄清对话流程")
        
        # 基于环境信息生成建议
        env_vars = environment_info.get("environment_variables", {})
        if not env_vars.get("real_models_enabled"):
            recommendations.append("启用真实模型测试以获得更准确的性能数据")
        
        if env_vars.get("QWEN_API_KEY") == "未设置" and env_vars.get("OPENAI_API_KEY") == "未设置":
            recommendations.append("配置API密钥以启用完整的AI功能测试")
        
        # 通用建议
        if not df.empty:
            if df['response_time'].mean() > 3.0:
                recommendations.append("考虑实施缓存策略以提高响应速度")
            
            if len(df) < 10:
                recommendations.append("增加测试用例数量以获得更全面的性能评估")
        
        return list(set(recommendations))  # 去重
    
    def save_report_markdown(self, report: TestReport, filename: Optional[str] = None) -> str:
        """保存Markdown格式报告"""
        if filename is None:
            timestamp = report.generated_at.strftime("%Y%m%d_%H%M%S")
            filename = f"gradio_test_report_{timestamp}.md"
        
        filepath = self.output_dir / filename
        
        # 生成Markdown内容
        content = self._generate_markdown_content(report)
        
        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return str(filepath)
    
    def _generate_markdown_content(self, report: TestReport) -> str:
        """生成Markdown内容"""
        content = f"""# {report.title}

**生成时间**: {report.generated_at.strftime('%Y年%m月%d日 %H:%M:%S')}  
**测试持续时间**: {report.test_duration}  

## 📊 测试概览

| 指标 | 数值 |
|------|------|
| 总请求数 | {report.total_requests} |
| 成功率 | {report.success_rate:.1f}% |
| 平均响应时间 | {report.avg_response_time:.2f}秒 |

## 🎯 性能分析

### 响应时间分布
"""
        
        if report.performance_metrics:
            df = pd.DataFrame(report.performance_metrics)
            
            # 响应时间统计
            response_times = df['response_time']
            content += f"""
- **最快响应**: {response_times.min():.2f}秒
- **最慢响应**: {response_times.max():.2f}秒
- **中位数**: {response_times.median():.2f}秒
- **95%分位数**: {response_times.quantile(0.95):.2f}秒

### 成功率分析
"""
            
            success_count = len(df[df['success'] == True])
            failed_count = len(df[df['success'] == False])
            
            content += f"""
- **成功请求**: {success_count}
- **失败请求**: {failed_count}
- **成功率**: {(success_count / len(df) * 100):.1f}%
"""
        
        # 问题分析
        content += "\n## ⚠️ 问题分析\n\n"
        if report.issues:
            for issue in report.issues:
                severity_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(issue["severity"], "⚪")
                content += f"### {severity_emoji} {issue['message']}\n"
                content += f"**类型**: {issue['type']}  \n"
                content += f"**严重程度**: {issue['severity']}  \n"
                if 'details' in issue:
                    content += f"**详细信息**: {json.dumps(issue['details'], ensure_ascii=False, indent=2)}\n"
                content += "\n"
        else:
            content += "✅ 未发现明显问题\n\n"
        
        # 改进建议
        content += "## 💡 改进建议\n\n"
        if report.recommendations:
            for i, rec in enumerate(report.recommendations, 1):
                content += f"{i}. {rec}\n"
        else:
            content += "暂无特定建议\n"
        
        # 环境信息
        content += f"""
## 🌍 环境信息

- **Python版本**: {report.environment_info.get('python_version', '未知')}
- **平台**: {report.environment_info.get('platform', '未知')}
- **工作目录**: {report.environment_info.get('working_directory', '未知')}

### 环境变量
"""
        
        env_vars = report.environment_info.get('environment_variables', {})
        for key, value in env_vars.items():
            content += f"- **{key}**: {value}\n"
        
        content += f"""
---
*报告由Gradio测试系统自动生成*  
*生成时间: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return content
    
    def save_report_json(self, report: TestReport, filename: Optional[str] = None) -> str:
        """保存JSON格式报告"""
        if filename is None:
            timestamp = report.generated_at.strftime("%Y%m%d_%H%M%S")
            filename = f"gradio_test_report_{timestamp}.json"
        
        filepath = self.output_dir / filename
        
        # 转换为可序列化的字典
        report_dict = {
            "title": report.title,
            "generated_at": report.generated_at.isoformat(),
            "test_duration_seconds": report.test_duration.total_seconds(),
            "total_requests": report.total_requests,
            "success_rate": report.success_rate,
            "avg_response_time": report.avg_response_time,
            "performance_metrics": report.performance_metrics,
            "conversation_history": [
                {**record, "timestamp": record["timestamp"].isoformat()}
                for record in report.conversation_history
            ],
            "environment_info": report.environment_info,
            "issues": report.issues,
            "recommendations": report.recommendations
        }
        
        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, ensure_ascii=False, indent=2)
        
        return str(filepath)
