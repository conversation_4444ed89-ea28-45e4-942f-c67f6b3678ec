"""
统一智能架构测试基类

提供统一智能架构集成测试的基础功能。
"""

import pytest
import asyncio
from typing import Dict, Any
from unittest.mock import Mock, AsyncMock

class UnifiedArchitectureTestBase:
    """统一智能架构测试基类"""
    
    @pytest.fixture
    def mock_llm_proxy(self):
        """模拟LLM代理"""
        mock = AsyncMock()
        mock.generate_text.return_value = "测试响应"
        return mock
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        mock = Mock()
        return mock
    
    @pytest.fixture
    def sample_user_profile(self):
        """示例用户配置"""
        return {
            "id": "test_user_123",
            "name": "测试用户",
            "fitness_level": "beginner",
            "goals": ["增肌", "减脂"]
        }
    
    @pytest.fixture
    def sample_context(self):
        """示例上下文"""
        return {
            "conversation_id": "test_conv_123",
            "user_id": "test_user_123",
            "messages": [],
            "intent_parameters": {}
        }
    
    async def assert_response_quality(self, response: Dict[str, Any]):
        """断言响应质量"""
        assert "content" in response
        assert "confidence" in response
        assert response["confidence"] >= 0.0
        assert response["confidence"] <= 1.0
        assert len(response["content"]) > 0
    
    async def measure_response_time(self, async_func, *args, **kwargs):
        """测量响应时间"""
        import time
        start_time = time.time()
        result = await async_func(*args, **kwargs)
        end_time = time.time()
        
        response_time = end_time - start_time
        return result, response_time
