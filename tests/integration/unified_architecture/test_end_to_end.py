"""
端到端集成测试

测试从API调用到响应输出的完整流程，验证统一智能架构的集成效果。
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from tests.integration.unified_architecture.base_test import UnifiedArchitectureTestBase

class TestEndToEndIntegration(UnifiedArchitectureTestBase):
    """端到端集成测试"""

    @pytest.fixture
    def test_client(self):
        """测试客户端fixture"""
        from app.main import app
        return TestClient(app)

    @pytest.fixture
    def mock_user(self):
        """模拟用户fixture"""
        user = Mock()
        user.id = 1
        user.nickname = "测试用户"
        user.age = 25
        user.gender = "male"
        user.height = 175
        user.weight = 70
        user.fitness_goal = "增肌"
        user.experience_level = "beginner"
        user.activity_level = "moderate"
        return user

    @pytest.fixture
    def auth_headers(self):
        """认证头fixture"""
        return {"Authorization": "Bearer test_token"}

    @pytest.mark.asyncio
    async def test_api_v2_message_without_unified_architecture(self, test_client, mock_user, auth_headers):
        """测试V2 API在未启用统一架构时的消息处理"""
        with patch('app.api.deps.get_current_active_user', return_value=mock_user), \
             patch('app.api.deps.get_db') as mock_db, \
             patch('app.core.unified_config.unified_settings') as mock_settings:

            # 配置统一架构为未启用
            mock_settings.ENABLE_UNIFIED_ARCHITECTURE = False

            # 模拟数据库操作
            mock_db_session = Mock()
            mock_db.return_value = mock_db_session

            # 模拟会话和消息创建
            with patch('app.crud.crud_conversation.get_by_session_id', return_value=None), \
                 patch('app.crud.crud_conversation.create_with_session_id') as mock_create_conv, \
                 patch('app.crud.crud_message.create_with_conversation') as mock_create_msg, \
                 patch('app.services.ai_assistant.conversation.orchestrator.conversation_orchestrator.process_message') as mock_process:

                # 配置模拟返回值
                mock_conversation = Mock()
                mock_conversation.id = 1
                mock_conversation.session_id = "test_session"
                mock_create_conv.return_value = mock_conversation

                mock_message = Mock()
                mock_message.id = 1
                mock_create_msg.return_value = mock_message

                mock_process.return_value = {
                    "response_content": "这是一个测试响应",
                    "intent": "general_chat",
                    "confidence": 0.8,
                    "current_state": "idle",
                    "success": True
                }

                # 发送请求
                response = test_client.post(
                    "/api/v2/chat/message",
                    json={
                        "message": "你好",
                        "session_id": "test_session"
                    },
                    headers=auth_headers
                )

                # 验证响应
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["response"] == "这是一个测试响应"
                assert data["intent_type"] == "general_chat"
                assert data["confidence"] == 0.8

    @pytest.mark.asyncio
    async def test_api_v2_message_with_unified_architecture(self, test_client, mock_user, auth_headers):
        """测试V2 API在启用统一架构时的消息处理"""
        with patch('app.api.deps.get_current_active_user', return_value=mock_user), \
             patch('app.api.deps.get_db') as mock_db, \
             patch('app.core.unified_config.unified_settings') as mock_settings:

            # 配置统一架构为启用
            mock_settings.ENABLE_UNIFIED_ARCHITECTURE = True
            mock_settings.UNIFIED_ARCH_PHASE = "phase1"
            mock_settings.ENABLE_HYBRID_ROUTER = True

            # 模拟数据库操作
            mock_db_session = Mock()
            mock_db.return_value = mock_db_session

            # 模拟会话和消息创建
            with patch('app.crud.crud_conversation.get_by_session_id', return_value=None), \
                 patch('app.crud.crud_conversation.create_with_session_id') as mock_create_conv, \
                 patch('app.crud.crud_message.create_with_conversation') as mock_create_msg, \
                 patch('app.services.ai_assistant.conversation.orchestrator.conversation_orchestrator.process_message') as mock_process:

                # 配置模拟返回值
                mock_conversation = Mock()
                mock_conversation.id = 1
                mock_conversation.session_id = "test_session"
                mock_create_conv.return_value = mock_conversation

                mock_message = Mock()
                mock_message.id = 1
                mock_create_msg.return_value = mock_message

                # 模拟统一架构的响应
                mock_process.return_value = {
                    "response_content": "这是统一架构的响应",
                    "intent": "exercise_action",
                    "confidence": 0.9,
                    "current_state": "idle",
                    "source_system": "hybrid_router",
                    "success": True
                }

                # 发送请求
                response = test_client.post(
                    "/api/v2/chat/message",
                    json={
                        "message": "推荐胸部训练",
                        "session_id": "test_session"
                    },
                    headers=auth_headers
                )

                # 验证响应
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["response"] == "这是统一架构的响应"
                assert data["intent_type"] == "exercise_action"
                assert data["confidence"] == 0.9

    @pytest.mark.asyncio
    async def test_unified_architecture_status_endpoint(self, test_client, mock_user, auth_headers):
        """测试统一架构状态端点"""
        with patch('app.api.deps.get_current_active_user', return_value=mock_user), \
             patch('app.core.unified_config.unified_settings') as mock_settings:

            # 配置统一架构设置
            mock_settings.ENABLE_UNIFIED_ARCHITECTURE = True
            mock_settings.UNIFIED_ARCH_PHASE = "phase1"
            mock_settings.ENABLE_ENHANCED_RECOGNIZER = True
            mock_settings.ENABLE_HYBRID_ROUTER = True
            mock_settings.ENABLE_LANGGRAPH = False
            mock_settings.LANGGRAPH_CHECKPOINT_TTL = 3600
            mock_settings.UNIFIED_ARCH_CACHE_TTL = 300
            mock_settings.LANGSMITH_TRACING = False
            mock_settings.LANGSMITH_PROJECT = "fitness-ai-assistant"

            # 发送请求
            response = test_client.get(
                "/api/v2/chat/unified-architecture/status",
                headers=auth_headers
            )

            # 验证响应
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert data["data"]["enabled"] is True
            assert data["data"]["phase"] == "phase1"
            assert data["data"]["features"]["enhanced_recognizer"] is True
            assert data["data"]["features"]["hybrid_router"] is True
            assert data["data"]["features"]["langgraph"] is False

    @pytest.mark.asyncio
    async def test_conversation_orchestrator_with_hybrid_router(self):
        """测试对话协调器与混合路由器的集成"""
        from app.services.ai_assistant.conversation.orchestrator import ConversationOrchestrator

        with patch('app.core.unified_config.unified_settings') as mock_settings, \
             patch('app.services.ai_assistant.conversation.orchestrator.conversation_state_manager') as mock_state_manager:

            # 配置统一架构
            mock_settings.ENABLE_UNIFIED_ARCHITECTURE = True
            mock_settings.ENABLE_HYBRID_ROUTER = True

            # 模拟状态管理器
            mock_state = AsyncMock()
            mock_state.handle_message.return_value = {
                "response": "状态机响应",
                "confidence": 0.8
            }
            mock_state_manager.get_current_state.return_value = mock_state

            # 模拟LLM代理
            mock_llm_proxy = AsyncMock()

            # 创建协调器实例
            orchestrator = ConversationOrchestrator(
                llm_proxy=mock_llm_proxy,
                knowledge_retriever=Mock(),
                cache_service=Mock()
            )

            # 模拟混合路由器
            mock_hybrid_router = AsyncMock()
            mock_hybrid_router.route.return_value = {
                "content": "混合路由器响应",
                "intent": "exercise_action",
                "confidence": 0.9,
                "source_system": "hybrid_router",
                "success": True
            }
            orchestrator.hybrid_router = mock_hybrid_router

            # 测试消息处理
            user_info = {"user_id": "1", "nickname": "测试用户"}
            response = await orchestrator.process_message(
                message="推荐胸部训练",
                conversation_id="test_conv",
                user_info=user_info
            )

            # 验证结果
            assert response["response_content"] == "混合路由器响应"
            assert response["source_system"] == "hybrid_router"
            assert response["success"] is True

            # 验证混合路由器被调用
            mock_hybrid_router.route.assert_called_once()

    @pytest.mark.asyncio
    async def test_performance_benchmark(self):
        """性能基准测试"""
        from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator

        # 模拟用户信息
        user_info = {
            "user_id": "1",
            "nickname": "测试用户",
            "fitness_goal": "增肌"
        }

        # 测试消息列表
        test_messages = [
            "你好",
            "推荐胸部训练",
            "制定训练计划",
            "营养建议",
            "健身问答"
        ]

        response_times = []

        for message in test_messages:
            start_time = time.time()

            try:
                # 模拟处理消息
                with patch.object(conversation_orchestrator, 'process_message') as mock_process:
                    mock_process.return_value = {
                        "response_content": f"响应: {message}",
                        "intent": "general_chat",
                        "confidence": 0.8,
                        "success": True
                    }

                    response = await conversation_orchestrator.process_message(
                        message=message,
                        conversation_id="test_conv",
                        user_info=user_info
                    )

                    end_time = time.time()
                    response_time = end_time - start_time
                    response_times.append(response_time)

                    # 验证响应
                    assert response["success"] is True

            except Exception as e:
                pytest.fail(f"处理消息 '{message}' 时出错: {str(e)}")

        # 计算性能指标
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)

        # 性能断言（这些值应该根据实际需求调整）
        assert avg_response_time < 2.0, f"平均响应时间过长: {avg_response_time:.2f}s"
        assert max_response_time < 5.0, f"最大响应时间过长: {max_response_time:.2f}s"

        print(f"性能测试结果:")
        print(f"  平均响应时间: {avg_response_time:.3f}s")
        print(f"  最大响应时间: {max_response_time:.3f}s")
        print(f"  最小响应时间: {min_response_time:.3f}s")

    @pytest.mark.asyncio
    async def test_error_handling_and_fallback(self):
        """测试错误处理和回退机制"""
        from app.services.ai_assistant.conversation.orchestrator import ConversationOrchestrator

        with patch('app.core.unified_config.unified_settings') as mock_settings:
            # 配置统一架构
            mock_settings.ENABLE_UNIFIED_ARCHITECTURE = True
            mock_settings.ENABLE_HYBRID_ROUTER = True

            # 模拟LLM代理
            mock_llm_proxy = AsyncMock()

            # 创建协调器实例
            orchestrator = ConversationOrchestrator(
                llm_proxy=mock_llm_proxy,
                knowledge_retriever=Mock(),
                cache_service=Mock()
            )

            # 模拟混合路由器失败
            mock_hybrid_router = AsyncMock()
            mock_hybrid_router.route.side_effect = Exception("路由器错误")
            orchestrator.hybrid_router = mock_hybrid_router

            # 模拟状态管理器作为回退
            with patch('app.services.ai_assistant.conversation.orchestrator.conversation_state_manager') as mock_state_manager:
                mock_state = AsyncMock()
                mock_state.handle_message.return_value = {
                    "response": "回退响应",
                    "confidence": 0.7,
                    "success": True
                }
                mock_state_manager.get_current_state.return_value = mock_state

                # 测试错误处理
                user_info = {"user_id": "1"}
                response = await orchestrator._process_with_hybrid_router(
                    intent="test_intent",
                    message="测试消息",
                    conversation_id="test_conv",
                    user_info=user_info,
                    conversation_history=[]
                )

                # 验证回退机制工作
                # 回退到状态机时，响应格式可能不同
                assert "response" in response or "response_content" in response
                response_content = response.get("response_content", response.get("response", ""))
                # 验证回退机制确实工作了（有响应内容且不为空）
                assert response_content is not None
                assert len(response_content) > 0
                # 验证混合路由器确实被调用了（并且失败了）
                mock_hybrid_router.route.assert_called_once()
