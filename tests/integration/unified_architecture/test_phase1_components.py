"""
阶段一组件集成测试

测试增强版意图识别器适配器、传统处理器工厂适配器和混合路由器的功能。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from tests.integration.unified_architecture.base_test import UnifiedArchitectureTestBase

class TestPhase1Components(UnifiedArchitectureTestBase):
    """阶段一组件测试"""

    @pytest.fixture
    def enhanced_adapter(self, mock_llm_proxy):
        """增强版意图识别器适配器fixture"""
        from app.services.ai_assistant.intent.adapters.enhanced_recognizer_adapter import EnhancedIntentRecognizerAdapter

        adapter = EnhancedIntentRecognizerAdapter(mock_llm_proxy)

        # 模拟初始化成功
        with patch('app.services.intent_recognition.enhanced_recognizer.EnhancedIntentRecognizer') as mock_recognizer:
            mock_instance = AsyncMock()
            mock_instance.recognize_intent.return_value = Mock(
                intent="recommend_exercise",
                confidence=0.9,
                parameters={"body_part": "胸部"}
            )
            mock_recognizer.return_value = mock_instance
            adapter.enhanced_recognizer = mock_instance
            adapter.initialized = True

            return adapter

    @pytest.fixture
    def legacy_adapter(self, mock_db_session, mock_llm_proxy):
        """传统处理器工厂适配器fixture"""
        from app.services.ai_assistant.intent.adapters.handler_factory_adapter import LegacyHandlerFactoryAdapter

        adapter = LegacyHandlerFactoryAdapter(mock_db_session, mock_llm_proxy)

        # 模拟初始化成功
        with patch('app.services.conversation.intent_handler.IntentHandler') as mock_handler:
            mock_instance = AsyncMock()

            async def mock_handle_intent(*args, **kwargs):
                yield {"type": "message", "content": "这是一个测试响应"}
                yield {"type": "structured_data", "data": {"exercise": "俯卧撑"}}

            mock_instance.handle_intent = mock_handle_intent
            mock_handler.return_value = mock_instance
            adapter.legacy_handler = mock_instance
            adapter.initialized = True

            return adapter

    @pytest.fixture
    def hybrid_router(self, enhanced_adapter, legacy_adapter):
        """混合路由器fixture"""
        from app.services.ai_assistant.conversation.routers.hybrid_router import HybridIntentRouter

        # 模拟状态管理器
        mock_state_manager = AsyncMock()
        mock_state = AsyncMock()
        mock_state.handle_message.return_value = {
            "response": "状态机响应",
            "confidence": 0.8
        }
        mock_state_manager.get_current_state.return_value = mock_state

        router = HybridIntentRouter(mock_state_manager, enhanced_adapter, legacy_adapter)
        return router

    @pytest.mark.asyncio
    async def test_enhanced_adapter_initialization(self, enhanced_adapter):
        """测试增强版适配器初始化"""
        assert enhanced_adapter.is_ready()
        assert enhanced_adapter.name == "EnhancedIntentRecognizerAdapter"

        # 测试健康检查
        health = await enhanced_adapter.health_check()
        assert health["status"] == "healthy"
        assert health["enhanced_recognizer_available"] is True

    @pytest.mark.asyncio
    async def test_enhanced_adapter_recognition(self, enhanced_adapter):
        """测试增强版适配器意图识别"""
        input_data = {
            "message": "我想练胸肌",
            "context": {"user_id": "test_user"}
        }

        result = await enhanced_adapter.process(input_data)

        assert result["intent_type"] == "exercise_action"  # 映射后的意图
        assert result["original_intent"] == "recommend_exercise"  # 原始意图
        assert result["confidence"] == 0.9
        assert result["source"] == "enhanced_recognizer"
        assert "parameters" in result

    @pytest.mark.asyncio
    async def test_enhanced_adapter_arecognize_interface(self, enhanced_adapter):
        """测试增强版适配器兼容接口"""
        result = await enhanced_adapter.arecognize("我想练胸肌", {"user_id": "test_user"})

        assert result.intent_type == "exercise_action"
        assert result.confidence == 0.9
        assert isinstance(result.parameters, dict)

    @pytest.mark.asyncio
    async def test_enhanced_adapter_batch_recognize(self, enhanced_adapter):
        """测试增强版适配器批量识别"""
        messages = ["我想练胸肌", "制定训练计划", "营养建议"]

        results = await enhanced_adapter.batch_recognize(messages)

        assert len(results) == 3
        for result in results:
            assert "message" in result
            assert "intent_type" in result
            assert "confidence" in result

    @pytest.mark.asyncio
    async def test_legacy_adapter_initialization(self, legacy_adapter):
        """测试传统适配器初始化"""
        assert legacy_adapter.is_ready()
        assert legacy_adapter.name == "LegacyHandlerFactoryAdapter"

        # 测试健康检查
        health = await legacy_adapter.health_check()
        assert health["status"] == "healthy"
        assert health["legacy_handler_available"] is True

    @pytest.mark.asyncio
    async def test_legacy_adapter_can_handle(self, legacy_adapter):
        """测试传统适配器意图支持检查"""
        # 支持的意图
        assert await legacy_adapter.can_handle("exercise_action") is True
        assert await legacy_adapter.can_handle("training_plan") is True
        assert await legacy_adapter.can_handle("diet_advice") is True

        # 不支持的意图
        assert await legacy_adapter.can_handle("general_chat") is False
        assert await legacy_adapter.can_handle("unknown_intent") is False

    @pytest.mark.asyncio
    async def test_legacy_adapter_handle_intent(self, legacy_adapter):
        """测试传统适配器意图处理"""
        input_data = {
            "intent_type": "exercise_action",
            "message": "推荐胸部训练",
            "context": {
                "conversation_id": "test_conv",
                "user_id": "test_user",
                "user_profile": {"name": "测试用户"}
            }
        }

        result = await legacy_adapter.process(input_data)

        assert result["intent_type"] == "exercise_action"
        assert result["confidence"] == 0.9
        assert result["source"] == "legacy_system"
        assert "content" in result
        assert "structured_data" in result

    @pytest.mark.asyncio
    async def test_legacy_adapter_test_intent_handling(self, legacy_adapter):
        """测试传统适配器意图处理测试功能"""
        test_result = await legacy_adapter.test_intent_handling("exercise_action")

        assert test_result["success"] is True
        assert test_result["intent_type"] == "exercise_action"
        assert "result" in test_result

    @pytest.mark.asyncio
    async def test_hybrid_router_initialization(self, hybrid_router):
        """测试混合路由器初始化"""
        assert hybrid_router.name == "HybridIntentRouter"

        # 检查路由规则
        rules = hybrid_router.get_routing_rules()
        assert "enhanced_priority" in rules
        assert "legacy_priority" in rules
        assert "state_machine_priority" in rules

    @pytest.mark.asyncio
    async def test_hybrid_router_legacy_routing(self, hybrid_router):
        """测试混合路由器传统系统路由"""
        context = {
            "conversation_id": "test_conv",
            "user_id": "test_user",
            "messages": [],
            "user_profile": {}
        }

        result = await hybrid_router.route("exercise_action", "推荐胸部训练", context)

        assert result["success"] is True
        assert result["intent"] == "exercise_action"
        assert "source_system" in result
        assert "processing_info" in result.get("metadata", {})

    @pytest.mark.asyncio
    async def test_hybrid_router_state_machine_routing(self, hybrid_router):
        """测试混合路由器状态机路由"""
        context = {
            "conversation_id": "test_conv",
            "user_id": "test_user",
            "messages": [],
            "user_profile": {}
        }

        result = await hybrid_router.route("general_chat", "你好", context)

        assert result["success"] is True
        assert result["intent"] == "general_chat"
        assert "source_system" in result

    @pytest.mark.asyncio
    async def test_hybrid_router_enhanced_routing(self, hybrid_router):
        """测试混合路由器增强系统路由"""
        context = {
            "conversation_id": "test_conv",
            "user_id": "test_user",
            "messages": [],
            "user_profile": {},
            "message": "复杂的健身咨询问题"
        }

        result = await hybrid_router.route("fitness_advice", "复杂的健身咨询问题", context)

        assert result["success"] is True
        assert result["intent"] == "fitness_advice"

    @pytest.mark.asyncio
    async def test_hybrid_router_hybrid_processing(self, hybrid_router):
        """测试混合路由器混合处理"""
        context = {
            "conversation_id": "test_conv",
            "user_id": "test_user",
            "messages": ["消息1", "消息2"],  # 短对话
            "user_profile": {},
            "message": "这是一个非常复杂的多步骤健身计划制定问题，需要综合考虑多个因素"
        }

        result = await hybrid_router.route("training_plan", context["message"], context)

        assert result["success"] is True
        assert "hybrid_info" in result or "source_system" in result

    @pytest.mark.asyncio
    async def test_hybrid_router_statistics(self, hybrid_router):
        """测试混合路由器统计功能"""
        # 执行几次路由
        context = {
            "conversation_id": "test_conv",
            "user_id": "test_user",
            "messages": [],
            "user_profile": {}
        }

        await hybrid_router.route("exercise_action", "测试1", context)
        await hybrid_router.route("general_chat", "测试2", context)
        await hybrid_router.route("training_plan", "测试3", context)

        stats = hybrid_router.get_routing_statistics()

        assert stats["total_requests"] >= 3
        assert "legacy_percentage" in stats
        assert "state_machine_percentage" in stats

    @pytest.mark.asyncio
    async def test_hybrid_router_health_check(self, hybrid_router):
        """测试混合路由器健康检查"""
        health = await hybrid_router.health_check()

        assert "router" in health
        assert "enhanced_adapter" in health
        assert "legacy_adapter" in health
        assert "routing_stats" in health

    @pytest.mark.asyncio
    async def test_response_quality_validation(self):
        """测试响应质量验证"""
        from app.services.ai_assistant.conversation.routers.unified_response import UnifiedResponse

        # 有效响应
        valid_response = UnifiedResponse.format_response(
            content="测试响应",
            intent="test_intent",
            confidence=0.8,
            source_system="test_system"
        )

        assert UnifiedResponse.validate_response(valid_response) is True

        # 无效响应（缺少字段）
        invalid_response = {"content": "测试"}
        assert UnifiedResponse.validate_response(invalid_response) is False

    @pytest.mark.asyncio
    async def test_error_handling(self, mock_llm_proxy):
        """测试错误处理"""
        from app.services.ai_assistant.intent.adapters.enhanced_recognizer_adapter import EnhancedIntentRecognizerAdapter

        # 测试未初始化的适配器
        adapter = EnhancedIntentRecognizerAdapter(mock_llm_proxy)

        result = await adapter.process({"message": "测试"})
        assert result["intent_type"] == "unknown"
        assert result["confidence"] == 0.0
        assert "error" in result
