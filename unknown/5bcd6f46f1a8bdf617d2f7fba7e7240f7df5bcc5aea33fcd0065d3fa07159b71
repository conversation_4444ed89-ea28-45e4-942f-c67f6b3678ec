#!/usr/bin/env python3
"""
统一智能架构集成项目启动脚本

用于初始化项目环境、创建必要的目录结构和配置文件。
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedArchitectureProjectStarter:
    """统一智能架构项目启动器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.app_dir = self.project_root / "app"
        self.docs_dir = self.project_root / "docs" / "agent"
        self.tests_dir = self.project_root / "tests"
        self.scripts_dir = self.project_root / "scripts"
        
        logger.info(f"项目根目录: {self.project_root}")
    
    def create_directory_structure(self):
        """创建项目目录结构"""
        logger.info("🏗️ 创建项目目录结构...")
        
        directories = [
            # 适配器目录
            self.app_dir / "services" / "ai_assistant" / "intent" / "adapters",
            
            # 路由器目录
            self.app_dir / "services" / "ai_assistant" / "conversation" / "routers",
            
            # LangGraph目录
            self.app_dir / "services" / "ai_assistant" / "langgraph" / "nodes",
            self.app_dir / "services" / "ai_assistant" / "langgraph" / "adapters",
            self.app_dir / "services" / "ai_assistant" / "langgraph" / "utils",
            
            # 测试目录
            self.tests_dir / "integration" / "unified_architecture",
            self.tests_dir / "performance",
            
            # 文档目录
            self.docs_dir / "implementation_logs",
            self.docs_dir / "api",
            
            # 脚本目录
            self.scripts_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
            # 创建__init__.py文件（如果是Python包目录）
            if "app" in str(directory) or "tests" in str(directory):
                init_file = directory / "__init__.py"
                if not init_file.exists():
                    init_file.touch()
            
            logger.info(f"✅ 创建目录: {directory}")
        
        logger.info("✅ 目录结构创建完成")
    
    def create_configuration_files(self):
        """创建配置文件"""
        logger.info("⚙️ 创建配置文件...")
        
        # 更新requirements.txt
        self._update_requirements()
        
        # 创建配置模板
        self._create_config_template()
        
        # 创建环境变量模板
        self._create_env_template()
        
        logger.info("✅ 配置文件创建完成")
    
    def _update_requirements(self):
        """更新requirements.txt"""
        requirements_file = self.project_root / "requirements.txt"
        
        new_dependencies = [
            "# 统一智能架构新增依赖",
            "langgraph>=0.0.40",
            "langsmith>=0.0.50  # 可选，用于监控",
            ""
        ]
        
        if requirements_file.exists():
            with open(requirements_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经添加了依赖
            if "langgraph" not in content:
                with open(requirements_file, 'a', encoding='utf-8') as f:
                    f.write("\n" + "\n".join(new_dependencies))
                logger.info("✅ 更新requirements.txt")
            else:
                logger.info("ℹ️ requirements.txt已包含所需依赖")
        else:
            with open(requirements_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(new_dependencies))
            logger.info("✅ 创建requirements.txt")
    
    def _create_config_template(self):
        """创建配置模板"""
        config_file = self.app_dir / "core" / "unified_config.py"
        
        if config_file.exists():
            logger.info("ℹ️ 配置文件已存在，跳过创建")
            return
        
        config_content = '''"""
统一智能架构配置

用于管理统一智能架构集成项目的配置参数。
"""

from pydantic import BaseSettings
from typing import Optional

class UnifiedArchitectureSettings(BaseSettings):
    """统一智能架构配置"""
    
    # 总体开关
    ENABLE_UNIFIED_ARCHITECTURE: bool = False
    UNIFIED_ARCH_PHASE: str = "phase1"  # phase1, phase2, phase3, phase4
    
    # 阶段一：传统系统集成配置
    ENABLE_ENHANCED_RECOGNIZER: bool = False
    ENABLE_LEGACY_PROCESSORS: bool = False
    ENABLE_HYBRID_ROUTER: bool = False
    
    # 阶段二：LangGraph配置
    ENABLE_LANGGRAPH: bool = False
    LANGGRAPH_CHECKPOINT_TTL: int = 3600
    LANGGRAPH_MAX_RECURSION: int = 50
    LANGGRAPH_ENABLE_STREAMING: bool = True
    
    # 阶段三：高级特性配置
    ENABLE_HUMAN_IN_LOOP: bool = False
    ENABLE_AB_TESTING: bool = False
    ENABLE_DYNAMIC_ROUTING: bool = False
    
    # LangSmith配置（可选）
    LANGSMITH_API_KEY: Optional[str] = None
    LANGSMITH_PROJECT: str = "fitness-ai-assistant"
    LANGSMITH_TRACING: bool = False
    
    # 性能配置
    UNIFIED_ARCH_CACHE_TTL: int = 300
    UNIFIED_ARCH_TIMEOUT: int = 30
    UNIFIED_ARCH_MAX_RETRIES: int = 3
    
    class Config:
        env_prefix = "UNIFIED_ARCH_"
        case_sensitive = True

# 全局配置实例
unified_settings = UnifiedArchitectureSettings()
'''
        
        config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        logger.info("✅ 创建统一架构配置文件")
    
    def _create_env_template(self):
        """创建环境变量模板"""
        env_file = self.project_root / ".env.unified_architecture"
        
        if env_file.exists():
            logger.info("ℹ️ 环境变量模板已存在，跳过创建")
            return
        
        env_content = '''# 统一智能架构环境变量配置

# 总体配置
UNIFIED_ARCH_ENABLE_UNIFIED_ARCHITECTURE=false
UNIFIED_ARCH_UNIFIED_ARCH_PHASE=phase1

# 阶段一配置
UNIFIED_ARCH_ENABLE_ENHANCED_RECOGNIZER=false
UNIFIED_ARCH_ENABLE_LEGACY_PROCESSORS=false
UNIFIED_ARCH_ENABLE_HYBRID_ROUTER=false

# 阶段二配置
UNIFIED_ARCH_ENABLE_LANGGRAPH=false
UNIFIED_ARCH_LANGGRAPH_CHECKPOINT_TTL=3600
UNIFIED_ARCH_LANGGRAPH_MAX_RECURSION=50

# LangSmith配置（可选）
# UNIFIED_ARCH_LANGSMITH_API_KEY=your_api_key_here
UNIFIED_ARCH_LANGSMITH_PROJECT=fitness-ai-assistant
UNIFIED_ARCH_LANGSMITH_TRACING=false

# 性能配置
UNIFIED_ARCH_UNIFIED_ARCH_CACHE_TTL=300
UNIFIED_ARCH_UNIFIED_ARCH_TIMEOUT=30
UNIFIED_ARCH_UNIFIED_ARCH_MAX_RETRIES=3
'''
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        logger.info("✅ 创建环境变量模板")
    
    def create_initial_files(self):
        """创建初始文件"""
        logger.info("📄 创建初始文件...")
        
        # 创建适配器基类
        self._create_adapter_base()
        
        # 创建路由器基类
        self._create_router_base()
        
        # 创建测试基类
        self._create_test_base()
        
        logger.info("✅ 初始文件创建完成")
    
    def _create_adapter_base(self):
        """创建适配器基类"""
        adapter_base_file = self.app_dir / "services" / "ai_assistant" / "intent" / "adapters" / "base_adapter.py"
        
        if adapter_base_file.exists():
            logger.info("ℹ️ 适配器基类已存在，跳过创建")
            return
        
        adapter_content = '''"""
适配器基类

定义统一智能架构中适配器的基础接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class BaseAdapter(ABC):
    """适配器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.initialized = False
        logger.info(f"初始化适配器: {name}")
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化适配器"""
        pass
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据"""
        pass
    
    def is_ready(self) -> bool:
        """检查适配器是否就绪"""
        return self.initialized
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "adapter": self.name,
            "status": "healthy" if self.is_ready() else "not_ready",
            "initialized": self.initialized
        }
'''
        
        with open(adapter_base_file, 'w', encoding='utf-8') as f:
            f.write(adapter_content)
        
        logger.info("✅ 创建适配器基类")
    
    def _create_router_base(self):
        """创建路由器基类"""
        router_base_file = self.app_dir / "services" / "ai_assistant" / "conversation" / "routers" / "base_router.py"
        
        if router_base_file.exists():
            logger.info("ℹ️ 路由器基类已存在，跳过创建")
            return
        
        router_content = '''"""
路由器基类

定义统一智能架构中路由器的基础接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class BaseRouter(ABC):
    """路由器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.routing_rules = {}
        logger.info(f"初始化路由器: {name}")
    
    @abstractmethod
    async def route(self, intent: str, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """路由请求"""
        pass
    
    def add_routing_rule(self, rule_name: str, rule_config: Dict[str, Any]):
        """添加路由规则"""
        self.routing_rules[rule_name] = rule_config
        logger.info(f"添加路由规则: {rule_name}")
    
    def get_routing_rules(self) -> Dict[str, Any]:
        """获取路由规则"""
        return self.routing_rules
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "router": self.name,
            "status": "healthy",
            "rules_count": len(self.routing_rules)
        }
'''
        
        with open(router_base_file, 'w', encoding='utf-8') as f:
            f.write(router_content)
        
        logger.info("✅ 创建路由器基类")
    
    def _create_test_base(self):
        """创建测试基类"""
        test_base_file = self.tests_dir / "integration" / "unified_architecture" / "base_test.py"
        
        if test_base_file.exists():
            logger.info("ℹ️ 测试基类已存在，跳过创建")
            return
        
        test_content = '''"""
统一智能架构测试基类

提供统一智能架构集成测试的基础功能。
"""

import pytest
import asyncio
from typing import Dict, Any
from unittest.mock import Mock, AsyncMock

class UnifiedArchitectureTestBase:
    """统一智能架构测试基类"""
    
    @pytest.fixture
    def mock_llm_proxy(self):
        """模拟LLM代理"""
        mock = AsyncMock()
        mock.generate_text.return_value = "测试响应"
        return mock
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        mock = Mock()
        return mock
    
    @pytest.fixture
    def sample_user_profile(self):
        """示例用户配置"""
        return {
            "id": "test_user_123",
            "name": "测试用户",
            "fitness_level": "beginner",
            "goals": ["增肌", "减脂"]
        }
    
    @pytest.fixture
    def sample_context(self):
        """示例上下文"""
        return {
            "conversation_id": "test_conv_123",
            "user_id": "test_user_123",
            "messages": [],
            "intent_parameters": {}
        }
    
    async def assert_response_quality(self, response: Dict[str, Any]):
        """断言响应质量"""
        assert "content" in response
        assert "confidence" in response
        assert response["confidence"] >= 0.0
        assert response["confidence"] <= 1.0
        assert len(response["content"]) > 0
    
    async def measure_response_time(self, async_func, *args, **kwargs):
        """测量响应时间"""
        import time
        start_time = time.time()
        result = await async_func(*args, **kwargs)
        end_time = time.time()
        
        response_time = end_time - start_time
        return result, response_time
'''
        
        with open(test_base_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        logger.info("✅ 创建测试基类")
    
    def initialize_project_management(self):
        """初始化项目管理"""
        logger.info("📊 初始化项目管理...")
        
        try:
            # 运行项目管理器初始化
            manager_script = self.scripts_dir / "unified_architecture_manager.py"
            if manager_script.exists():
                result = subprocess.run([
                    sys.executable, str(manager_script),
                    "--project-root", str(self.project_root),
                    "--action", "init"
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("✅ 项目管理器初始化成功")
                else:
                    logger.error(f"❌ 项目管理器初始化失败: {result.stderr}")
            else:
                logger.warning("⚠️ 项目管理器脚本不存在")
                
        except Exception as e:
            logger.error(f"❌ 项目管理初始化失败: {str(e)}")
    
    def generate_startup_report(self):
        """生成启动报告"""
        logger.info("📋 生成启动报告...")
        
        report_content = f"""# 统一智能架构集成项目启动报告

## 项目信息
- **项目名称**: 智能健身AI助手统一架构集成
- **启动时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **项目根目录**: {self.project_root}

## 初始化完成项目
✅ 目录结构创建
✅ 配置文件创建
✅ 初始文件创建
✅ 项目管理初始化

## 下一步操作
1. 检查并安装依赖: `pip install -r requirements.txt`
2. 配置环境变量: 复制 `.env.unified_architecture` 到 `.env`
3. 运行健康检查: `python scripts/unified_architecture_manager.py --action health`
4. 开始阶段一开发工作

## 重要文件
- 配置文件: `app/core/unified_config.py`
- 环境变量: `.env.unified_architecture`
- 项目管理: `scripts/unified_architecture_manager.py`
- 文档目录: `docs/agent/`

## 联系信息
如有问题，请参考项目文档或联系技术负责人。

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        report_file = self.docs_dir / "项目启动报告.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info("✅ 启动报告生成完成")
        print(f"\n📋 启动报告已保存到: {report_file}")
    
    def run_startup_sequence(self):
        """运行启动序列"""
        logger.info("🚀 开始统一智能架构集成项目启动...")
        
        try:
            # 1. 创建目录结构
            self.create_directory_structure()
            
            # 2. 创建配置文件
            self.create_configuration_files()
            
            # 3. 创建初始文件
            self.create_initial_files()
            
            # 4. 初始化项目管理
            self.initialize_project_management()
            
            # 5. 生成启动报告
            self.generate_startup_report()
            
            logger.info("🎉 项目启动完成！")
            print("\n🎉 统一智能架构集成项目启动成功！")
            print("📖 请查看启动报告了解下一步操作。")
            
        except Exception as e:
            logger.error(f"❌ 项目启动失败: {str(e)}")
            print(f"\n❌ 项目启动失败: {str(e)}")
            sys.exit(1)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="统一智能架构集成项目启动器")
    parser.add_argument("--project-root", default=".", help="项目根目录")
    parser.add_argument("--force", action="store_true", help="强制重新创建文件")
    
    args = parser.parse_args()
    
    starter = UnifiedArchitectureProjectStarter(args.project_root)
    starter.run_startup_sequence()

if __name__ == "__main__":
    main()
