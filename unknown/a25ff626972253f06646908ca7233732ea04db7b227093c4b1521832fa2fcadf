# 增强版运动动作处理图生产环境部署指南

## 🎯 部署概述

本指南详细说明如何将重构后的增强版运动动作处理图部署到生产环境。系统已通过全面测试验证，具备生产环境部署条件。

## ✅ 部署前检查

### 系统要求
- **Python**: 3.8+
- **PostgreSQL**: 12+
- **Redis**: 6.0+
- **内存**: 最少2GB，推荐4GB+
- **CPU**: 最少2核，推荐4核+
- **存储**: 最少10GB可用空间

### 性能基准验证
在部署前，请确认系统满足以下性能基准：
- ✅ 响应时间 < 1秒（实测0.011s）
- ✅ 并发处理 ≥ 5个请求（实测10个）
- ✅ 内存增长 < 100MB（实测23.8MB）
- ✅ 成功率 ≥ 90%（实测100%）

## 🔧 环境配置

### 1. 数据库配置

#### PostgreSQL设置
```sql
-- 创建数据库
CREATE DATABASE fitness_db;

-- 创建用户
CREATE USER fitness_user WITH PASSWORD 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE fitness_db TO fitness_user;
```

#### 环境变量配置
```bash
# 数据库连接
DATABASE_URL=postgresql://fitness_user:your_secure_password@localhost:5432/fitness_db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# LLM配置
OPENAI_API_KEY=your_openai_api_key
DEFAULT_LLM_MODEL=gpt-3.5-turbo

# 应用配置
IS_DEV=false
LOG_LEVEL=INFO
```

### 2. 依赖安装

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装额外的生产依赖
pip install gunicorn uvicorn[standard] psycopg2-binary
```

### 3. 数据库迁移

```bash
# 运行数据库迁移
alembic upgrade head

# 验证数据库连接
python -c "from app.db.session import get_db; print('数据库连接成功')"
```

## 🚀 部署步骤

### 1. 代码部署

```bash
# 克隆代码
git clone <repository_url>
cd fitness-ai-backend

# 切换到生产分支
git checkout production

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置文件设置

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

### 3. 系统验证

```bash
# 运行生产就绪测试
python tests/comprehensive/interactive/test_production_ready.py

# 运行性能基准测试
python tests/comprehensive/interactive/test_performance_benchmark.py
```

### 4. 启动服务

#### 使用Gunicorn（推荐）
```bash
# 启动API服务
gunicorn app.main:app \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --timeout 120 \
  --keep-alive 2 \
  --max-requests 1000 \
  --max-requests-jitter 100
```

#### 使用Docker
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000
CMD ["gunicorn", "app.main:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

## 📊 监控配置

### 1. 健康检查端点

```python
# 添加到app/api/v1/health.py
@router.get("/health/enhanced-exercise")
async def enhanced_exercise_health():
    """增强版运动图健康检查"""
    try:
        from app.services.ai_assistant.langgraph.test_basic_graph import basic_test_graph
        
        # 快速健康检查
        result = await basic_test_graph.process_message(
            message="健康检查",
            conversation_id="health_check",
            user_info={"user_id": "health", "user_profile": {}}
        )
        
        return {
            "status": "healthy" if result.get("success") else "unhealthy",
            "service": "enhanced_exercise_graph",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "enhanced_exercise_graph",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
```

### 2. 日志配置

```python
# 生产环境日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
    },
    "handlers": {
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "/var/log/fitness-ai/enhanced_exercise.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "formatter": "default",
        },
    },
    "loggers": {
        "app.services.ai_assistant.langgraph": {
            "handlers": ["file"],
            "level": "INFO",
            "propagate": False,
        },
    },
}
```

### 3. 性能监控

```bash
# 安装监控工具
pip install prometheus-client psutil

# 添加性能指标收集
# 在app/core/metrics.py中添加
from prometheus_client import Counter, Histogram, Gauge

# 定义指标
REQUEST_COUNT = Counter('enhanced_exercise_requests_total', 'Total requests')
REQUEST_DURATION = Histogram('enhanced_exercise_request_duration_seconds', 'Request duration')
MEMORY_USAGE = Gauge('enhanced_exercise_memory_usage_bytes', 'Memory usage')
```

## 🔒 安全配置

### 1. API限流

```python
# 在app/core/security.py中添加
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

# 应用限流
@limiter.limit("100/minute")
async def enhanced_exercise_endpoint():
    pass
```

### 2. 输入验证

```python
# 增强输入验证
from pydantic import BaseModel, validator

class ExerciseRequest(BaseModel):
    message: str
    conversation_id: str
    user_info: dict
    
    @validator('message')
    def validate_message(cls, v):
        if len(v) > 1000:
            raise ValueError('消息长度不能超过1000字符')
        return v
```

## 🔄 负载均衡

### Nginx配置示例

```nginx
upstream fitness_ai_backend {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
}

server {
    listen 80;
    server_name api.fitness-ai.com;
    
    location /api/v1/enhanced-exercise {
        proxy_pass http://fitness_ai_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_timeout 30s;
    }
}
```

## 📋 部署检查清单

### 部署前
- [ ] 环境变量配置完成
- [ ] 数据库连接测试通过
- [ ] 依赖安装完成
- [ ] 生产就绪测试通过
- [ ] 性能基准测试通过

### 部署后
- [ ] 服务启动成功
- [ ] 健康检查端点正常
- [ ] 日志记录正常
- [ ] 监控指标收集正常
- [ ] API响应正常

### 验证测试
- [ ] 运动动作查询功能正常
- [ ] 用户信息收集流程正常
- [ ] 数据库查询功能正常
- [ ] 多轮对话状态管理正常
- [ ] 错误处理机制正常

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库状态
   systemctl status postgresql
   
   # 测试连接
   psql -h localhost -U fitness_user -d fitness_db
   ```

2. **内存使用过高**
   ```bash
   # 监控内存使用
   python tests/comprehensive/interactive/test_performance_benchmark.py
   
   # 调整worker数量
   gunicorn --workers 2 app.main:app
   ```

3. **响应时间过长**
   ```bash
   # 检查数据库查询性能
   # 优化数据库索引
   # 调整LLM模型配置
   ```

## 📞 支持联系

- **技术支持**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx
- **文档更新**: 请提交GitHub Issue

---

**部署完成后，请运行完整的生产环境验证测试，确保所有功能正常工作！**
