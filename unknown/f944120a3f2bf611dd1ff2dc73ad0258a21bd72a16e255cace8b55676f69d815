"""
LangGraph环境验证器

验证LangGraph环境配置和依赖完整性。
"""

import logging
import sys
from typing import Dict, Any, List, Tuple
from importlib import import_module

logger = logging.getLogger(__name__)

class EnvironmentValidator:
    """LangGraph环境验证器"""
    
    def __init__(self):
        self.validation_results: Dict[str, Any] = {}
        self.required_packages = [
            "langgraph",
            "langchain_core",
            "langchain",
            "pydantic",
            "psycopg2"  # PostgreSQL适配器
        ]
        self.optional_packages = [
            "langsmith",
            "openai",
            "anthropic"
        ]
    
    def validate_all(self) -> Dict[str, Any]:
        """
        执行完整的环境验证
        
        Returns:
            验证结果字典
        """
        logger.info("开始LangGraph环境验证")
        
        results = {
            "overall_status": "unknown",
            "python_version": self._check_python_version(),
            "package_dependencies": self._check_package_dependencies(),
            "langgraph_functionality": self._check_langgraph_functionality(),
            "database_connectivity": self._check_database_connectivity(),
            "configuration": self._check_configuration(),
            "recommendations": []
        }
        
        # 确定总体状态
        results["overall_status"] = self._determine_overall_status(results)
        
        # 生成建议
        results["recommendations"] = self._generate_recommendations(results)
        
        self.validation_results = results
        logger.info(f"环境验证完成，状态: {results['overall_status']}")
        
        return results
    
    def _check_python_version(self) -> Dict[str, Any]:
        """检查Python版本"""
        try:
            version = sys.version_info
            version_str = f"{version.major}.{version.minor}.{version.micro}"
            
            # LangGraph需要Python 3.8+
            is_compatible = version >= (3, 8)
            
            return {
                "version": version_str,
                "compatible": is_compatible,
                "minimum_required": "3.8.0",
                "status": "pass" if is_compatible else "fail"
            }
            
        except Exception as e:
            logger.error(f"Python版本检查失败: {str(e)}")
            return {
                "version": "unknown",
                "compatible": False,
                "error": str(e),
                "status": "error"
            }
    
    def _check_package_dependencies(self) -> Dict[str, Any]:
        """检查包依赖"""
        results = {
            "required": {},
            "optional": {},
            "missing_required": [],
            "missing_optional": [],
            "status": "unknown"
        }
        
        # 检查必需包
        for package in self.required_packages:
            result = self._check_single_package(package)
            results["required"][package] = result
            if not result["available"]:
                results["missing_required"].append(package)
        
        # 检查可选包
        for package in self.optional_packages:
            result = self._check_single_package(package)
            results["optional"][package] = result
            if not result["available"]:
                results["missing_optional"].append(package)
        
        # 确定状态
        if not results["missing_required"]:
            results["status"] = "pass"
        else:
            results["status"] = "fail"
        
        return results
    
    def _check_single_package(self, package_name: str) -> Dict[str, Any]:
        """检查单个包"""
        try:
            module = import_module(package_name)
            version = getattr(module, '__version__', 'unknown')
            
            return {
                "available": True,
                "version": version,
                "status": "installed"
            }
            
        except ImportError as e:
            return {
                "available": False,
                "error": str(e),
                "status": "missing"
            }
        except Exception as e:
            return {
                "available": False,
                "error": str(e),
                "status": "error"
            }
    
    def _check_langgraph_functionality(self) -> Dict[str, Any]:
        """检查LangGraph功能"""
        try:
            # 尝试导入核心组件
            from langgraph.graph import StateGraph
            from langgraph.graph.message import MessagesState
            from langgraph.checkpoint.memory import MemorySaver
            
            # 尝试创建简单的图
            class TestState(MessagesState):
                test_field: str = ""
            
            def test_node(state: TestState):
                return {"test_field": "test_value"}
            
            graph = StateGraph(TestState)
            graph.add_node("test", test_node)
            graph.set_entry_point("test")
            graph.set_finish_point("test")
            
            # 尝试编译
            memory = MemorySaver()
            compiled_graph = graph.compile(checkpointer=memory)
            
            return {
                "core_imports": True,
                "graph_creation": True,
                "graph_compilation": True,
                "status": "pass"
            }
            
        except ImportError as e:
            logger.error(f"LangGraph导入失败: {str(e)}")
            return {
                "core_imports": False,
                "error": str(e),
                "status": "fail"
            }
        except Exception as e:
            logger.error(f"LangGraph功能测试失败: {str(e)}")
            return {
                "core_imports": True,
                "graph_creation": False,
                "error": str(e),
                "status": "fail"
            }
    
    def _check_database_connectivity(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            from app.services.ai_assistant.langgraph.utils.checkpoint_config import checkpoint_config
            
            # 尝试初始化检查点存储
            init_success = checkpoint_config.initialize()
            
            if init_success:
                # 测试连接
                connection_test = checkpoint_config.test_connection()
                config_info = checkpoint_config.get_config_info()
                
                return {
                    "checkpoint_init": True,
                    "connection_test": connection_test,
                    "config_info": config_info,
                    "status": "pass" if connection_test else "warning"
                }
            else:
                return {
                    "checkpoint_init": False,
                    "connection_test": False,
                    "status": "warning",
                    "note": "将使用内存存储作为备用"
                }
                
        except Exception as e:
            logger.error(f"数据库连接检查失败: {str(e)}")
            return {
                "checkpoint_init": False,
                "connection_test": False,
                "error": str(e),
                "status": "warning"
            }
    
    def _check_configuration(self) -> Dict[str, Any]:
        """检查配置"""
        try:
            from app.core.unified_config import unified_settings
            
            config_checks = {
                "unified_architecture_enabled": unified_settings.ENABLE_UNIFIED_ARCHITECTURE,
                "langgraph_enabled": unified_settings.ENABLE_LANGGRAPH,
                "checkpoint_ttl": unified_settings.LANGGRAPH_CHECKPOINT_TTL,
                "max_recursion": unified_settings.LANGGRAPH_MAX_RECURSION,
                "langsmith_tracing": unified_settings.LANGSMITH_TRACING
            }
            
            # 检查关键配置
            issues = []
            if not config_checks["unified_architecture_enabled"]:
                issues.append("统一架构未启用")
            
            if not config_checks["langgraph_enabled"]:
                issues.append("LangGraph未启用")
            
            if config_checks["checkpoint_ttl"] <= 0:
                issues.append("检查点TTL配置无效")
            
            return {
                "config_values": config_checks,
                "issues": issues,
                "status": "pass" if not issues else "warning"
            }
            
        except Exception as e:
            logger.error(f"配置检查失败: {str(e)}")
            return {
                "error": str(e),
                "status": "error"
            }
    
    def _determine_overall_status(self, results: Dict[str, Any]) -> str:
        """确定总体状态"""
        statuses = []
        
        for key, value in results.items():
            if isinstance(value, dict) and "status" in value:
                statuses.append(value["status"])
        
        if "fail" in statuses or "error" in statuses:
            return "fail"
        elif "warning" in statuses:
            return "warning"
        elif all(status == "pass" for status in statuses):
            return "pass"
        else:
            return "unknown"
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # Python版本建议
        if results["python_version"]["status"] != "pass":
            recommendations.append("升级Python到3.8或更高版本")
        
        # 包依赖建议
        pkg_results = results["package_dependencies"]
        if pkg_results["missing_required"]:
            recommendations.append(
                f"安装缺失的必需包: {', '.join(pkg_results['missing_required'])}"
            )
        
        if pkg_results["missing_optional"]:
            recommendations.append(
                f"考虑安装可选包以获得更好的功能: {', '.join(pkg_results['missing_optional'])}"
            )
        
        # LangGraph功能建议
        if results["langgraph_functionality"]["status"] != "pass":
            recommendations.append("检查LangGraph安装并重新安装相关依赖")
        
        # 数据库建议
        if results["database_connectivity"]["status"] == "warning":
            recommendations.append("配置PostgreSQL连接以启用持久化检查点存储")
        
        # 配置建议
        config_results = results["configuration"]
        if config_results.get("issues"):
            for issue in config_results["issues"]:
                recommendations.append(f"配置问题: {issue}")
        
        return recommendations
    
    def print_validation_report(self):
        """打印验证报告"""
        if not self.validation_results:
            print("请先运行validate_all()方法")
            return
        
        results = self.validation_results
        
        print("\n" + "="*60)
        print("LangGraph环境验证报告")
        print("="*60)
        
        print(f"\n总体状态: {results['overall_status'].upper()}")
        
        print(f"\nPython版本: {results['python_version']['version']} "
              f"({'✓' if results['python_version']['compatible'] else '✗'})")
        
        print("\n包依赖:")
        pkg_results = results['package_dependencies']
        for pkg, info in pkg_results['required'].items():
            status = "✓" if info['available'] else "✗"
            version = info.get('version', 'N/A')
            print(f"  {pkg}: {status} ({version})")
        
        print(f"\nLangGraph功能: {'✓' if results['langgraph_functionality']['status'] == 'pass' else '✗'}")
        
        print(f"\n数据库连接: {'✓' if results['database_connectivity']['status'] == 'pass' else '⚠'}")
        
        print(f"\n配置检查: {'✓' if results['configuration']['status'] == 'pass' else '⚠'}")
        
        if results['recommendations']:
            print("\n建议:")
            for i, rec in enumerate(results['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print("\n" + "="*60)

# 全局验证器实例
environment_validator = EnvironmentValidator()

def validate_langgraph_environment() -> Dict[str, Any]:
    """
    验证LangGraph环境
    
    Returns:
        验证结果字典
    """
    return environment_validator.validate_all()

def print_environment_report():
    """打印环境验证报告"""
    environment_validator.validate_all()
    environment_validator.print_validation_report()
